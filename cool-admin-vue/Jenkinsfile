pipeline {
    agent any
    
    environment {
        // 项目配置
        PROJECT_NAME = 'cool-admin-vue'
        DOCKER_IMAGE = 'cool-admin/frontend'
        DOCKER_TAG = "${BUILD_NUMBER}"
        NODE_VERSION = '18'
        
        // Docker Registry配置
        DOCKER_REGISTRY = '**************:5000'
        DOCKER_CREDENTIALS_ID = 'docker-registry-credentials'
        
        // 部署配置
        DEPLOY_SERVER = '**************'
        DEPLOY_USER = 'root'
        DEPLOY_PATH = '/opt/deploy/cool-admin'
        
        // pnpm配置
        PNPM_REGISTRY = 'https://registry.npmmirror.com'
        PNPM_VERSION = 'latest'
    }
    
    tools {
        nodejs 'node18'
    }
    
    stages {
        stage('Checkout') {
            steps {
                echo '🔄 检出代码...'
                checkout scm
                
                script {
                    // 获取Git信息
                    env.GIT_COMMIT_SHORT = sh(
                        script: "git rev-parse --short HEAD",
                        returnStdout: true
                    ).trim()
                    env.GIT_BRANCH = sh(
                        script: "git rev-parse --abbrev-ref HEAD",
                        returnStdout: true
                    ).trim()
                }
                
                echo "Git分支: ${env.GIT_BRANCH}"
                echo "Git提交: ${env.GIT_COMMIT_SHORT}"
            }
        }
        
        stage('Environment Setup') {
            steps {
                echo '⚙️ 环境设置...'
                
                // 检查项目文件
                sh 'ls -la package.json'
                sh 'ls -la pnpm-lock.yaml || echo "pnpm-lock.yaml 不存在"'
                
                // 检查Node.js版本
                sh 'node --version'
                sh 'npm --version'
                
                // 设置npm国内镜像源
                sh "npm config set registry ${PNPM_REGISTRY}"
                
                // 卸载并重新安装pnpm（确保版本正确）
                sh 'npm uninstall -g pnpm || true'
                sh "npm install -g pnpm@${PNPM_VERSION}"
                sh 'pnpm --version'
                
                // 设置pnpm配置
                sh "pnpm config set registry ${PNPM_REGISTRY}"
                sh 'pnpm config set store-dir ~/.pnpm-store'
                sh 'pnpm config set shamefully-hoist true'
                sh 'pnpm config set auto-install-peers true'
                sh 'pnpm config set strict-peer-dependencies false'
                
                // 完全清理缓存和依赖
                sh 'pnpm store prune --force || true'
                sh 'rm -rf node_modules .pnpm-store pnpm-lock.yaml || true'
                sh 'rm -rf ~/.pnpm-store || true'
                
                echo 'pnpm 环境配置完成'
            }
        }
        
        stage('Dependencies') {
            steps {
                echo '📦 安装依赖...'
                
                // 清理可能损坏的依赖
                sh 'rm -rf node_modules .pnpm-store || true'
                sh 'pnpm store prune --force || true'
                
                // 重新安装依赖，不使用frozen-lockfile
                sh 'pnpm install --no-frozen-lockfile --force'
                
            }
        }
        

        
        stage('Build') {
            steps {
                echo '🔨 构建项目...'
                
                // 设置构建环境变量
                script {
                    def buildEnv = 'production'
                    if (env.GIT_BRANCH == 'develop') {
                        buildEnv = 'development'
                    } else if (env.GIT_BRANCH == 'staging') {
                        buildEnv = 'staging'
                    }
                    
                    echo "构建环境: ${buildEnv}"
                    env.BUILD_ENV = buildEnv
                }
                
                // 执行构建（带重试机制）
                script {
                    try {
                        sh 'pnpm run build'
                    } catch (Exception e) {
                        echo "pnpm 构建失败，尝试使用 npm 构建: ${e.getMessage()}"
                        
                        // 使用 npm 作为备用方案
                        sh 'npm install --force'
                        sh 'npm run build'
                    }
                }
                
                // 检查构建结果
                sh 'ls -la dist/'
                
                // 归档构建产物
                archiveArtifacts artifacts: 'dist/**/*', fingerprint: true
            }
        }
        
        
        stage('Docker Build') {
            steps {
                echo '🐳 构建Docker镜像...'
                
                // 检查Dockerfile是否存在
                sh 'ls -la Dockerfile'
                
                script {
                    // 构建Docker镜像
                    def image = docker.build("${DOCKER_IMAGE}:${DOCKER_TAG}")
                    
                    // 同时打上latest标签
                    sh "docker tag ${DOCKER_IMAGE}:${DOCKER_TAG} ${DOCKER_IMAGE}:latest"
                    
                    // 推送到Registry
                    docker.withRegistry("http://${DOCKER_REGISTRY}", DOCKER_CREDENTIALS_ID) {
                        image.push()
                        image.push('latest')
                    }
                }
            }
        }
        

        
        stage('Deploy') {
            steps {
                echo '🚀 部署到服务器...'
                script {
                    // 使用 ssh-agent 方式部署
                    sshagent(credentials: ['ssh-gkubuntu']) {
                        withCredentials([
                            usernamePassword(
                                credentialsId: 'docker-registry-credentials',
                                usernameVariable: 'DOCKER_REGISTRY_USERNAME',
                                passwordVariable: 'DOCKER_REGISTRY_PASSWORD'
                            )
                        ]) {
                            sh """
                                echo "🚀 直接通过SSH执行前端部署命令..."
                                ssh -o StrictHostKeyChecking=no -p 6000 ${DEPLOY_USER}@${DEPLOY_SERVER} " \\
                                    set -e && \\
                                    cd ${DEPLOY_PATH} && \\
                                    echo '>>> 登录到 Docker Registry...' && \\
                                    echo '${DOCKER_REGISTRY_PASSWORD}' | docker login ${DOCKER_REGISTRY} --username '${DOCKER_REGISTRY_USERNAME}' --password-stdin && \\
                                    echo '>>> 拉取最新前端镜像...' && \\
                                    docker-compose -f docker-compose-cool.yml --env-file cool.env pull cool-front && \\
                                    echo '>>> 重启前端服务...' && \\
                                    docker-compose -f docker-compose-cool.yml --env-file cool.env up -d cool-front && \\
                                    echo '>>> 清理旧镜像...' && \\
                                    docker image prune -f && \\
                                    echo '✅ 前端部署完成'
                                "
                            """
                        }
                    }
                }
            }
        }
    }
    
    post {
        always {
            echo '🧹 清理工作空间...'
            
            // 清理Docker镜像（更安全的方式）
            script {
                try {
                    // 检查镜像是否存在再删除
                    def imageExists = sh(
                        script: "docker images -q ${DOCKER_IMAGE}:${DOCKER_TAG}",
                        returnStdout: true
                    ).trim()
                    
                    if (imageExists) {
                        echo "删除镜像: ${DOCKER_IMAGE}:${DOCKER_TAG}"
                        sh "docker rmi ${DOCKER_IMAGE}:${DOCKER_TAG}"
                    } else {
                        echo "镜像不存在，跳过删除: ${DOCKER_IMAGE}:${DOCKER_TAG}"
                    }
                } catch (Exception e) {
                    echo "清理镜像时出错: ${e.getMessage()}"
                }
                
                try {
                    // 清理悬空镜像和容器
                    sh "docker system prune -f"
                } catch (Exception e) {
                    echo "系统清理时出错: ${e.getMessage()}"
                }
            }
            
            // 清理工作空间
            cleanWs()
        }
        
        success {
            echo '✅ 前端构建成功！'
            echo "项目: ${PROJECT_NAME}"
            echo "分支: ${env.GIT_BRANCH}"
            echo "构建号: ${BUILD_NUMBER}"
            echo "提交: ${env.GIT_COMMIT_SHORT}"
            echo "访问地址: http://${DEPLOY_SERVER}"
        }
        
        failure {
            echo '❌ 前端构建失败！'
            echo "项目: ${PROJECT_NAME}"
            echo "分支: ${env.GIT_BRANCH}"
            echo "构建号: ${BUILD_NUMBER}"
            echo "查看详情: ${BUILD_URL}"
        }
        
        unstable {
            echo '⚠️ 前端构建不稳定！'
            echo "项目: ${PROJECT_NAME}"
            echo "分支: ${env.GIT_BRANCH}"
            echo "构建号: ${BUILD_NUMBER}"
            echo "请检查构建日志"
        }
    }
} 