import { ModuleConfig } from "/@/cool";

export default (): ModuleConfig => {
  return {
    enable: true,
    label: "业主公示",
    description: "物业财务与服务信息公开，业主可随时查阅公示内容。",
    author: "Cool Team",
    version: "1.0.0",
    order: 80,
    views: [
      {
        path: "/sun/area-overview",
        name: "sun-area-overview",
        meta: {
          label: "物业服务区域概况",
          icon: "el-icon-document",
          keepAlive: true
        },
        component: () => import("./views/area-overview.vue")
      },
      {
        path: "/sun/owner-income-expense",
        name: "sun-owner-income-expense",
        meta: {
          label: "业主共有部分经营收益收支",
          icon: "el-icon-document",
          keepAlive: true
        },
        component: () => import("./views/owner-income-expense.vue")
      },
      {
        path: "/sun/fund-income-expense",
        name: "sun-fund-income-expense",
        meta: {
          label: "物业服务资金收支（酬金制）",
          icon: "el-icon-document",
          keepAlive: true
        },
        component: () => import("./views/fund-income-expense.vue")
      },
      {
        path: "/sun/issue-report",
        name: "sun-issue-report",
        meta: {
          label: "小区问题马上解决报告",
          icon: "el-icon-document",
          keepAlive: true
        },
        component: () => import("./views/issue-report.vue")
      }
    ]
  };
}; 