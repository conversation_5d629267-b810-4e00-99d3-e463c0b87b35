<template>
  <el-scrollbar>
    <div class="sun-issue-report-page">
      <el-card class="main-card">
        <div class="title">物业小区问题“用心用情”马上解决工作报告</div>
        <el-row class="section-row">
          <el-col :xs="24">
            <el-alert
              :title="headerText"
              type="info"
              :closable="false"
              show-icon
              class="mb16"
            />
          </el-col>
        </el-row>
        <el-card shadow="never" class="sub-card mt24">
          <el-table :data="tableData" border style="width: 100%">
            <el-table-column prop="index" label="序号" width="60" align="center" />
            <el-table-column prop="channel" label="诉求渠道" />
            <el-table-column prop="total" label="反映问题总量" />
            <el-table-column prop="responseTime" label="平均响应时长(小时)" />
            <el-table-column prop="mainTotal" label="物业主责问题总量" />
            <el-table-column prop="solved" label="已解决" />
            <el-table-column prop="pending" label="待解决满意" />
            <el-table-column prop="otherTotal" label="其他（非物业）问题总量" />
            <el-table-column prop="reported" label="上报" />
            <el-table-column prop="assist" label="协助解决" />
            <el-table-column prop="remark" label="备注" />
          </el-table>
        </el-card>
        <el-row class="footer-row">
          <el-col :xs="24" :md="8"><span class="footer-label">制表：</span></el-col>
          <el-col :xs="24" :md="8"><span class="footer-label">审核：</span></el-col>
          <el-col :xs="24" :md="8"><span class="footer-label">日期：</span></el-col>
        </el-row>
      </el-card>
    </div>
  </el-scrollbar>
</template>

<script lang="ts" setup>
import { ref } from 'vue';

defineOptions({
  name: 'sun-issue-report'
});

// 顶部说明（动态数据接口预留）
const headerText = ref('尊敬的业主：现将 XX 年 X 季度业主通过相关渠道反映的小区问题解决情况报告如下。');

// 表格数据（动态数据接口预留）
const tableData = ref([
  { index: 1, channel: '客户问询', total: '', responseTime: '', mainTotal: '', solved: '', pending: '', otherTotal: '', reported: '', assist: '', remark: '' },
  { index: 2, channel: '客户报修', total: '', responseTime: '', mainTotal: '', solved: '', pending: '', otherTotal: '', reported: '', assist: '', remark: '' },
  { index: 3, channel: '客户投诉', total: '', responseTime: '', mainTotal: '', solved: '', pending: '', otherTotal: '', reported: '', assist: '', remark: '' },
  { index: 4, channel: '智慧工单', total: '', responseTime: '', mainTotal: '', solved: '', pending: '', otherTotal: '', reported: '', assist: '', remark: '' },
  { index: 5, channel: '其他', total: '', responseTime: '', mainTotal: '', solved: '', pending: '', otherTotal: '', reported: '', assist: '', remark: '' },
]);

// 说明内容（完全按主题定制）
const usageNotes = [
  '本报告按季度统计业主通过各渠道反映的问题及解决情况。',
  '“诉求渠道”包括问询、报修、投诉、智慧工单等，需如实填写。',
  '“反映问题总量”指各渠道收集到的问题数量。',
  '“平均响应时长”指从问题受理到首次响应的平均用时。',
  '“物业主责问题总量”及“已解决”“待解决满意”需与实际处理情况一致。',
  '“其他（非物业）问题总量”指非物业职责范围内的问题。',
  '“上报”“协助解决”指已上报或协助相关部门处理的问题。',
  '本模板及其列举项目、填写范例供参考，物业服务企业可结合实际调整。',
];
</script>

<style scoped>
/*
  主题色适配说明
  - 页面主题色：#7d3c98（紫色，服务/关怀/响应）
  - 变量 --theme-color：主色，用于标题、分区、表格头等
  - 变量 --theme-bg：分区背景色
  - 支持自动适配 dark/light 主题
  - 如需切换主题色，仅需修改 .sun-issue-report-page 内的变量
*/
.sun-issue-report-page {
  --theme-color: #7d3c98;
  --theme-bg: #f7f0fa;
  --card-bg: #fff;
  --text-color: #222;
  --table-th-bg: var(--theme-color);
  --table-th-color: #fff;
  background: #f8f9fa;
  min-height: 100vh;
  padding: 24px 0;
  color: var(--text-color);
}
.main-card {
  max-width: 1100px;
  margin: 0 auto;
  border-radius: 18px;
  box-shadow: 0 4px 24px rgba(80,120,200,0.08);
  background: var(--card-bg);
  padding: 32px 18px 24px 18px;
}
.title {
  font-size: 2rem;
  font-weight: 700;
  color: var(--theme-color);
  text-align: center;
  margin-bottom: 32px;
  letter-spacing: 2px;
  border-bottom: 3px solid var(--theme-color);
  padding-bottom: 8px;
}
.section-row {
  margin-bottom: 24px;
}
.sub-card {
  border-radius: 12px;
  margin-bottom: 0;
  background: var(--theme-bg);
  border-left: 4px solid var(--theme-color);
}
.sub-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--theme-color);
  margin-bottom: 16px;
}
.mt24 {
  margin-top: 24px;
}
.mb16 {
  margin-bottom: 16px;
}
.note-alert {
  margin-bottom: 8px;
}
.el-table th {
  background: var(--table-th-bg) !important;
  color: var(--table-th-color) !important;
}
.footer-row {
  margin-top: 24px;
  text-align: center;
  color: #888;
  font-size: 15px;
}
.footer-label {
  margin-right: 12px;
}
@media (max-width: 900px) {
  .main-card { padding: 12px 2px; }
  .title { font-size: 1.3rem; }
}
@media (max-width: 600px) {
  .main-card { padding: 2px 0; }
  .title { font-size: 1.1rem; }
}
@media (prefers-color-scheme: dark) {
  .sun-issue-report-page {
    --theme-color: #b18cff;
    --theme-bg: #241a32;
    --card-bg: #231e2b;
    --text-color: #ede6f3;
    --table-th-bg: #7d3c98;
    --table-th-color: #fff;
    background: #18151f;
  }
  .main-card {
    box-shadow: 0 4px 24px rgba(80,60,120,0.18);
  }
}
</style> 