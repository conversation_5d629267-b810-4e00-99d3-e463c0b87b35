<template>
  <el-scrollbar>
    <div class="sun-owner-income-expense-page">
      <el-card class="main-card">
        <div class="title">业主共有部分经营收益收支情况表</div>
        <el-row :gutter="16" class="section-row">
          <el-col :xs="24" :md="12">
            <el-descriptions :column="1" border>
              <el-descriptions-item label="物业服务区域">{{ baseInfo.area }}</el-descriptions-item>
              <el-descriptions-item label="物业服务人（盖章）">{{ baseInfo.provider }}</el-descriptions-item>
            </el-descriptions>
          </el-col>
        </el-row>
        <el-card shadow="never" class="sub-card mt24">
          <el-table :data="tableData" border style="width: 100%">
            <el-table-column prop="project" label="项目" />
            <el-table-column prop="serial" label="序列" width="70" align="center" />
            <el-table-column prop="amount" label="金额 (元)" width="120" align="center" />
            <el-table-column prop="remark" label="备注" />
          </el-table>
        </el-card>
        <el-row class="footer-row">
          <el-col :xs="24" :md="8"><span class="footer-label">制表：</span></el-col>
          <el-col :xs="24" :md="8"><span class="footer-label">审核：</span></el-col>
          <el-col :xs="24" :md="8"><span class="footer-label">日期：</span></el-col>
        </el-row>
      </el-card>
    </div>
  </el-scrollbar>
</template>

<script lang="ts" setup>
import { ref } from 'vue';

defineOptions({
  name: 'sun-owner-income-expense'
});

// 基础信息（动态数据接口预留）
const baseInfo = ref({
  area: '',
  provider: ''
});

// 表格数据（动态数据接口预留）
const tableData = ref([
  { project: '1.业主共有部分经营收入', serial: '1', amount: '', remark: '' },
  { project: '1.1共有停车位收入', serial: '2', amount: '', remark: '' },
  { project: '1.1.1车位月租收入', serial: '3', amount: '', remark: '' },
  { project: '1.1.2车位临停收入', serial: '4', amount: '', remark: '' },
  { project: '1.2广告位收入', serial: '5', amount: '', remark: '' },
  { project: '1.2.1电梯轿厢广告位收入', serial: '6', amount: '', remark: '' },
  { project: '1.2.2××区域广告位收入', serial: '7', amount: '', remark: '' },
  { project: '1.2.3 ××区域广告位收入', serial: '8', amount: '', remark: '' },
  { project: '1.3信号发射基站场地收入', serial: '9', amount: '', remark: '' },
  { project: '1.4室外公共场地租赁收入', serial: '10', amount: '', remark: '' },
  { project: '1.5智能快递柜场地收入', serial: '11', amount: '', remark: '' },
  { project: '1.6××场所经营收入', serial: '12', amount: '', remark: '' },
  { project: '1.7其他收入', serial: '13', amount: '', remark: '' },
  { project: '1.8利息收入', serial: '14', amount: '', remark: '' },
  { project: '2.物业共有部分经营成本支出', serial: '15', amount: '', remark: '' },
  { project: '2.1经营人员人工成本支出', serial: '16', amount: '', remark: '' },
  { project: '2.2水电能耗支出', serial: '17', amount: '', remark: '' },
  { project: '2.3税金及附加', serial: '18', amount: '', remark: '' },
  { project: '2.4其他成本支出', serial: '19', amount: '', remark: '' },
  { project: '3.经营收益 (扣除经营成本后)', serial: '20', amount: '', remark: '' },
  { project: '4.经营收益依约或经业主同意的支出', serial: '21', amount: '', remark: '' },
  { project: '4.1共有部分、共用设施设备的维修、更新与改造费用支出', serial: '22', amount: '', remark: '' },
  { project: '4.2业主大会、业主委员会工作经费或开展业主活动支出', serial: '23', amount: '', remark: '' },
  { project: '4.3补充物业专项维修资金', serial: '24', amount: '', remark: '' },
  { project: '4.4依约分配给物业服务企业所有部分', serial: '25', amount: '', remark: '' },
  { project: '4.5财务审计费用', serial: '26', amount: '', remark: '' },
  { project: '4.6经业主大会同意的其他支出', serial: '27', amount: '', remark: '' },
  { project: '5.本期经营收益结余 (属业主共有)', serial: '28', amount: '', remark: '' },
  { project: '6.累计经营收益结余 (属业主共有)', serial: '29', amount: '', remark: '' },
]);

// 说明内容（完全按主题定制）
const usageNotes = [
  '经营收益（扣除经营成本后）序列 20 = 1 - 15；本期经营收益结余（属业主共有）序列 28 = 20 - 21。',
  '各项费用应在“备注”栏内列明具体收支依据、标准。',
  '本模板未罗列穷尽的其他依法属于业主共有部分的收入及支出项应当在本表中合适位置增加栏目予以公开。',
  '本表应当由物业项目经理审核签字，并加盖公章后向业主公开。',
  '本模板及其列举项目、填写范例供参考使用，物业服务企业可结合实际作相应调整。',
  '公开的文本应保持清晰醒目、便于阅览，字号以小四或12号及以上为宜。',
];
</script>

<style scoped>
/*
  主题色适配说明
  - 页面主题色：#218a5a（绿色，收益/增长/公开）
  - 变量 --theme-color：主色，用于标题、分区、表格头等
  - 变量 --theme-bg：分区背景色
  - 支持自动适配 dark/light 主题
  - 如需切换主题色，仅需修改 .sun-owner-income-expense-page 内的变量
*/
.sun-owner-income-expense-page {
  --theme-color: #218a5a;
  --theme-bg: #eafaf3;
  --card-bg: #fff;
  --text-color: #222;
  --table-th-bg: var(--theme-color);
  --table-th-color: #fff;
  background: #f8f9fa;
  min-height: 100vh;
  padding: 24px 0;
  color: var(--text-color);
}
.main-card {
  max-width: 1100px;
  margin: 0 auto;
  border-radius: 18px;
  box-shadow: 0 4px 24px rgba(80,120,200,0.08);
  background: var(--card-bg);
  padding: 32px 18px 24px 18px;
}
.title {
  font-size: 2rem;
  font-weight: 700;
  color: var(--theme-color);
  text-align: center;
  margin-bottom: 32px;
  letter-spacing: 2px;
  border-bottom: 3px solid var(--theme-color);
  padding-bottom: 8px;
}
.section-row {
  margin-bottom: 24px;
}
.sub-card {
  border-radius: 12px;
  margin-bottom: 0;
  background: var(--theme-bg);
  border-left: 4px solid var(--theme-color);
}
.sub-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--theme-color);
  margin-bottom: 16px;
}
.mt24 {
  margin-top: 24px;
}
.note-alert {
  margin-bottom: 8px;
}
.el-table th {
  background: var(--table-th-bg) !important;
  color: var(--table-th-color) !important;
}
.footer-row {
  margin-top: 24px;
  text-align: center;
  color: #888;
  font-size: 15px;
}
.footer-label {
  margin-right: 12px;
}
@media (max-width: 900px) {
  .main-card { padding: 12px 2px; }
  .title { font-size: 1.3rem; }
}
@media (max-width: 600px) {
  .main-card { padding: 2px 0; }
  .title { font-size: 1.1rem; }
}
@media (prefers-color-scheme: dark) {
  .sun-owner-income-expense-page {
    --theme-color: #4fd18b;
    --theme-bg: #1a2a22;
    --card-bg: #1e2b23;
    --text-color: #e6f3ea;
    --table-th-bg: #218a5a;
    --table-th-color: #fff;
    background: #151a18;
  }
  .main-card {
    box-shadow: 0 4px 24px rgba(40,80,60,0.18);
  }
}
</style> 