<template>
  <el-scrollbar>
    <div class="sun-area-overview-page">
      <el-card class="main-card">
        <div class="title">物业服务区域概况</div>
        <el-row :gutter="16" class="section-row">
          <el-col :xs="24" :md="12">
            <el-card shadow="never" class="sub-card">
              <div class="sub-title">区域信息</div>
              <el-descriptions :column="1" border>
                <el-descriptions-item label="区域名称">{{ areaInfo.name }}</el-descriptions-item>
                <el-descriptions-item label="所在地">{{ areaInfo.location }}</el-descriptions-item>
                <el-descriptions-item label="区域范围">
                  <el-row>
                    <el-col :span="12">东至：{{ areaInfo.east }}</el-col>
                    <el-col :span="12">南至：{{ areaInfo.south }}</el-col>
                  </el-row>
                  <el-row>
                    <el-col :span="12">西至：{{ areaInfo.west }}</el-col>
                    <el-col :span="12">北至：{{ areaInfo.north }}</el-col>
                  </el-row>
                </el-descriptions-item>
                <el-descriptions-item label="总建筑面积">{{ areaInfo.buildingArea }}</el-descriptions-item>
                <el-descriptions-item label="总户数">{{ areaInfo.households }}</el-descriptions-item>
              </el-descriptions>
            </el-card>
          </el-col>
          <el-col :xs="24" :md="12">
            <el-card shadow="never" class="sub-card">
              <div class="sub-title">服务合同信息</div>
              <el-descriptions :column="1" border>
                <el-descriptions-item label="物业服务人">{{ contractInfo.provider }}</el-descriptions-item>
                <el-descriptions-item label="合同期限">{{ contractInfo.period }}</el-descriptions-item>
                <el-descriptions-item label="执行标准">{{ contractInfo.standard }}</el-descriptions-item>
                <el-descriptions-item label="收费方式">{{ contractInfo.chargeType }}</el-descriptions-item>
                <el-descriptions-item label="收费标准">{{ contractInfo.chargeStandard }}</el-descriptions-item>
              </el-descriptions>
            </el-card>
          </el-col>
        </el-row>
        <el-card shadow="never" class="sub-card mt24">
          <div class="sub-title">业主共有部分</div>
          <el-table :data="ownerSharedList" border style="width: 100%">
            <el-table-column prop="index" label="序号" width="60" align="center" />
            <el-table-column prop="name" label="名称" />
            <el-table-column prop="area" label="面积/数量" />
            <el-table-column prop="location" label="坐落位置" />
          </el-table>
        </el-card>
      </el-card>
    </div>
  </el-scrollbar>
</template>

<script lang="ts" setup>
import { ref } from 'vue';

defineOptions({
  name: 'sun-area-overview'
});

// 区域信息（动态数据接口预留）
const areaInfo = ref({
  name: '',
  location: '',
  east: '',
  south: '',
  west: '',
  north: '',
  buildingArea: '',
  households: ''
});

// 服务合同信息（动态数据接口预留）
const contractInfo = ref({
  provider: '',
  period: '',
  standard: '',
  chargeType: '',
  chargeStandard: ''
});

// 业主共有部分（动态数据接口预留）
const ownerSharedList = ref([
  { index: 1, name: '', area: '', location: '' },
  { index: 2, name: '', area: '', location: '' },
  { index: 3, name: '', area: '', location: '' },
]);

// 说明内容（完全按主题定制）
const usageNotes = [
  '“区域名称”“区域范围”应与物业服务合同、建筑区划划分意见书保持一致。',
  '“合同期限”应按物业服务合同约定填写，期满未续聘的，按民法典相关规定填写为不定期。',
  '“执行标准”应完整填写国家、地方、行业或团体标准的名称、编号及等级。',
  '“收费方式”应填写“包干制”或“酬金制”，并与合同一致。',
  '“收费标准”栏，包干制应分别填写各物业类型标准，酬金制应分别填写业主交纳标准和物业服务人提取标准。',
  '业主共有部分应列明依法属于业主共有的房屋、场所。',
  '本模板及其列举项目、填写范例供参考，物业服务企业可结合实际调整。',
];
</script>

<style scoped>
/*
  主题色适配说明
  - 页面主题色：#2a4d7a（蓝色，权威/专业）
  - 变量 --theme-color：主色，用于标题、分区、表格头等
  - 变量 --theme-bg：分区背景色
  - 支持自动适配 dark/light 主题
  - 如需切换主题色，仅需修改 .sun-area-overview-page 内的变量
*/
.sun-area-overview-page {
  --theme-color: #2a4d7a;
  --theme-bg: #f0f6ff;
  --card-bg: #fff;
  --text-color: #222;
  --table-th-bg: var(--theme-color);
  --table-th-color: #fff;
  background: #f8f9fa;
  min-height: 100vh;
  padding: 24px 0;
  color: var(--text-color);
}
.main-card {
  max-width: 1100px;
  margin: 0 auto;
  border-radius: 18px;
  box-shadow: 0 4px 24px rgba(80,120,200,0.08);
  background: var(--card-bg);
  padding: 32px 18px 24px 18px;
}
.title {
  font-size: 2rem;
  font-weight: 700;
  color: var(--theme-color);
  text-align: center;
  margin-bottom: 32px;
  letter-spacing: 2px;
  border-bottom: 3px solid var(--theme-color);
  padding-bottom: 8px;
}
.section-row {
  margin-bottom: 24px;
}
.sub-card {
  border-radius: 12px;
  margin-bottom: 0;
  background: var(--theme-bg);
  border-left: 4px solid var(--theme-color);
}
.sub-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--theme-color);
  margin-bottom: 16px;
}
.mt24 {
  margin-top: 24px;
}
.note-alert {
  margin-bottom: 8px;
}
.el-table th {
  background: var(--table-th-bg) !important;
  color: var(--table-th-color) !important;
}
@media (prefers-color-scheme: dark) {
  .sun-area-overview-page {
    --theme-color: #6ea8ff;
    --theme-bg: #1a2332;
    --card-bg: #232b3b;
    --text-color: #e6eaf3;
    --table-th-bg: #2a4d7a;
    --table-th-color: #fff;
    background: #151a23;
  }
  .main-card {
    box-shadow: 0 4px 24px rgba(40,60,100,0.18);
  }
}
@media (max-width: 900px) {
  .main-card { padding: 12px 2px; }
  .title { font-size: 1.3rem; }
}
@media (max-width: 600px) {
  .main-card { padding: 2px 0; }
  .title { font-size: 1.1rem; }
}
</style> 