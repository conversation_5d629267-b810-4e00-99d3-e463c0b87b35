<template>
  <el-dialog
    :model-value="modelValue"
    @update:model-value="$emit('update:modelValue', $event)"
    title="模板预览"
    width="80%"
    :close-on-click-modal="false"
  >
    <div v-if="template" class="template-preview">
      <h3>{{ template.name }}</h3>
      <p>{{ template.description }}</p>
      <div class="preview-content">
        <p>模板预览功能开发中...</p>
      </div>
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
interface Props {
  modelValue: boolean;
  template: any;
}

defineProps<Props>();
defineEmits<{
  'update:modelValue': [value: boolean];
}>();
</script>

<style lang="scss" scoped>
.template-preview {
  padding: 20px;
}
</style>
