<template>
  <div class="organization-transition">
    <!-- 切换遮罩层 -->
    <transition name="switch-overlay">
      <div v-if="isTransitioning" class="switch-overlay">
        <div class="switch-content">
          <div class="switch-icon">
            <el-icon class="rotating">
              <component :is="targetModeIcon" />
            </el-icon>
          </div>
          <div class="switch-text">
            <h3>正在切换到{{ targetModeName }}</h3>
            <p>{{ switchMessage }}</p>
          </div>
          <div class="switch-progress">
            <el-progress 
              :percentage="progress" 
              :show-text="false"
              :stroke-width="4"
              color="#409eff"
            />
            <div class="progress-text">{{ progressText }}</div>
          </div>
        </div>
      </div>
    </transition>

    <!-- 模式切换成功提示 -->
    <transition name="success-notification">
      <div v-if="showSuccessNotification" class="success-notification">
        <el-icon class="success-icon">
          <Check />
        </el-icon>
        <span>切换成功！</span>
      </div>
    </transition>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { useOrganizationStore, OrganizationMode } from '../store/organization';
import { Check, OfficeBuilding, FolderOpened } from '@element-plus/icons-vue';

const organizationStore = useOrganizationStore();

// 响应式数据
const isTransitioning = ref(false);
const progress = ref(0);
const progressText = ref('');
const switchMessage = ref('');
const showSuccessNotification = ref(false);
const targetMode = ref<OrganizationMode | null>(null);

// 计算属性
const targetModeIcon = computed(() => {
  return targetMode.value === OrganizationMode.DEPARTMENT ? OfficeBuilding : FolderOpened;
});

const targetModeName = computed(() => {
  return targetMode.value === OrganizationMode.DEPARTMENT ? '部门维度' : '项目维度';
});

// 切换步骤配置
const switchSteps = [
  { progress: 10, message: '正在验证权限...', text: '验证中' },
  { progress: 30, message: '正在保存当前状态...', text: '保存状态' },
  { progress: 50, message: '正在切换组织模式...', text: '切换模式' },
  { progress: 70, message: '正在更新菜单权限...', text: '更新权限' },
  { progress: 90, message: '正在加载新界面...', text: '加载界面' },
  { progress: 100, message: '切换完成', text: '完成' }
];

// 监听组织store的loading状态
watch(() => organizationStore.loading, (loading) => {
  if (loading && !isTransitioning.value) {
    startTransition();
  } else if (!loading && isTransitioning.value) {
    completeTransition();
  }
});

// 开始切换动画
const startTransition = () => {
  isTransitioning.value = true;
  targetMode.value = organizationStore.currentMode === OrganizationMode.DEPARTMENT 
    ? OrganizationMode.PROJECT 
    : OrganizationMode.DEPARTMENT;
  
  progress.value = 0;
  progressText.value = '';
  switchMessage.value = '准备切换...';

  // 模拟切换步骤
  let stepIndex = 0;
  const stepInterval = setInterval(() => {
    if (stepIndex < switchSteps.length) {
      const step = switchSteps[stepIndex];
      progress.value = step.progress;
      switchMessage.value = step.message;
      progressText.value = step.text;
      stepIndex++;
    } else {
      clearInterval(stepInterval);
    }
  }, 300);
};

// 完成切换动画
const completeTransition = () => {
  progress.value = 100;
  progressText.value = '完成';
  switchMessage.value = '切换完成';

  setTimeout(() => {
    isTransitioning.value = false;
    showSuccessNotification.value = true;

    setTimeout(() => {
      showSuccessNotification.value = false;
    }, 2000);
  }, 500);
};

// 暴露方法供外部调用
defineExpose({
  startTransition,
  completeTransition
});
</script>

<style lang="scss" scoped>
.organization-transition {
  .switch-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(4px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;

    .switch-content {
      background: white;
      border-radius: 16px;
      padding: 40px;
      text-align: center;
      min-width: 320px;
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);

      .switch-icon {
        margin-bottom: 20px;

        .rotating {
          font-size: 48px;
          color: var(--el-color-primary);
          animation: rotate 2s linear infinite;
        }
      }

      .switch-text {
        margin-bottom: 30px;

        h3 {
          margin: 0 0 8px 0;
          color: var(--el-text-color-primary);
          font-size: 18px;
          font-weight: 600;
        }

        p {
          margin: 0;
          color: var(--el-text-color-regular);
          font-size: 14px;
        }
      }

      .switch-progress {
        .progress-text {
          margin-top: 8px;
          font-size: 12px;
          color: var(--el-text-color-secondary);
        }
      }
    }
  }

  .success-notification {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: var(--el-color-success);
    color: white;
    padding: 16px 24px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 500;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    z-index: 10000;

    .success-icon {
      font-size: 18px;
    }
  }
}

// 动画定义
.switch-overlay-enter-active,
.switch-overlay-leave-active {
  transition: all 0.3s ease;
}

.switch-overlay-enter-from,
.switch-overlay-leave-to {
  opacity: 0;
}

.switch-overlay-enter-to,
.switch-overlay-leave-from {
  opacity: 1;
}

.success-notification-enter-active,
.success-notification-leave-active {
  transition: all 0.3s ease;
}

.success-notification-enter-from,
.success-notification-leave-to {
  opacity: 0;
  transform: translate(-50%, -50%) scale(0.8);
}

.success-notification-enter-to,
.success-notification-leave-from {
  opacity: 1;
  transform: translate(-50%, -50%) scale(1);
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

// 暗黑模式适配
.dark {
  .switch-overlay {
    .switch-content {
      background: var(--el-bg-color);
      color: var(--el-text-color-primary);
    }
  }
}
</style>
