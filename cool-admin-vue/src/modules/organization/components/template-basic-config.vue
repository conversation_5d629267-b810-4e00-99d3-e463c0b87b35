<template>
  <div class="template-basic-config">
    <el-form :model="modelValue" label-width="120px">
      <el-form-item label="项目周期">
        <el-input-number v-model="modelValue.duration" :min="1" :max="365" />
        <span style="margin-left: 8px;">天</span>
      </el-form-item>
      
      <el-form-item label="默认优先级">
        <el-select v-model="modelValue.defaultPriority">
          <el-option label="低" :value="1" />
          <el-option label="普通" :value="2" />
          <el-option label="中等" :value="3" />
          <el-option label="高" :value="4" />
          <el-option label="紧急" :value="5" />
        </el-select>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup lang="ts">
interface Props {
  modelValue: any;
}

defineProps<Props>();
defineEmits<{
  'update:modelValue': [value: any];
}>();
</script>

<style lang="scss" scoped>
.template-basic-config {
  padding: 20px;
}
</style>
