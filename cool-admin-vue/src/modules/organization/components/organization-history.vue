<template>
  <div class="organization-history">
    <!-- 快速切换按钮 -->
    <el-dropdown 
      trigger="click" 
      placement="bottom-start"
      @command="handleQuickSwitch"
    >
      <el-button text class="history-trigger">
        <el-icon><Clock /></el-icon>
        <span v-if="!isMobile">切换历史</span>
      </el-button>
      
      <template #dropdown>
        <el-dropdown-menu class="history-dropdown">
          <div class="history-header">
            <span>最近切换</span>
            <el-button 
              text 
              size="small" 
              @click="clearHistory"
              v-if="historyList.length > 0"
            >
              清空
            </el-button>
          </div>
          
          <div v-if="historyList.length === 0" class="empty-history">
            <el-empty 
              :image-size="60" 
              description="暂无切换记录"
            />
          </div>
          
          <div v-else class="history-list">
            <el-dropdown-item
              v-for="item in historyList"
              :key="item.id"
              :command="item"
              class="history-item"
            >
              <div class="history-content">
                <div class="history-info">
                  <div class="history-mode">
                    <el-icon class="mode-icon">
                      <component :is="getModeIcon(item.fromMode)" />
                    </el-icon>
                    <el-icon class="arrow-icon"><ArrowRight /></el-icon>
                    <el-icon class="mode-icon">
                      <component :is="getModeIcon(item.toMode)" />
                    </el-icon>
                  </div>
                  <div class="history-desc">
                    {{ getModeText(item.fromMode) }} → {{ getModeText(item.toMode) }}
                  </div>
                </div>
                <div class="history-time">
                  {{ formatTime(item.switchTime) }}
                </div>
              </div>
            </el-dropdown-item>
          </div>
          
          <el-divider style="margin: 8px 0;" />
          
          <el-dropdown-item command="viewAll" class="view-all-item">
            <el-icon><More /></el-icon>
            查看全部记录
          </el-dropdown-item>
        </el-dropdown-menu>
      </template>
    </el-dropdown>

    <!-- 详细历史对话框 -->
    <el-dialog
      v-model="showHistoryDialog"
      title="组织切换历史"
      width="800px"
      :close-on-click-modal="false"
    >
      <div class="history-dialog">
        <!-- 统计信息 -->
        <div class="history-stats">
          <el-row :gutter="16">
            <el-col :span="8">
              <el-statistic title="总切换次数" :value="totalSwitches" />
            </el-col>
            <el-col :span="8">
              <el-statistic title="今日切换" :value="todaySwitches" />
            </el-col>
            <el-col :span="8">
              <el-statistic title="最常用模式" :value="mostUsedMode" />
            </el-col>
          </el-row>
        </div>

        <!-- 历史记录表格 -->
        <el-table 
          :data="paginatedHistory" 
          style="width: 100%; margin-top: 20px;"
          size="small"
        >
          <el-table-column label="切换时间" width="180">
            <template #default="{ row }">
              {{ formatDateTime(row.switchTime) }}
            </template>
          </el-table-column>
          
          <el-table-column label="切换方向" width="200">
            <template #default="{ row }">
              <div class="switch-direction">
                <el-tag size="small" :type="getModeTagType(row.fromMode)">
                  {{ getModeText(row.fromMode) }}
                </el-tag>
                <el-icon class="arrow-icon"><ArrowRight /></el-icon>
                <el-tag size="small" :type="getModeTagType(row.toMode)">
                  {{ getModeText(row.toMode) }}
                </el-tag>
              </div>
            </template>
          </el-table-column>
          
          <el-table-column label="切换原因" show-overflow-tooltip>
            <template #default="{ row }">
              {{ row.reason || '用户主动切换' }}
            </template>
          </el-table-column>
          
          <el-table-column label="耗时" width="80">
            <template #default="{ row }">
              {{ row.duration ? `${row.duration}ms` : '-' }}
            </template>
          </el-table-column>
          
          <el-table-column label="操作" width="100">
            <template #default="{ row }">
              <el-button 
                text 
                size="small" 
                @click="quickSwitchTo(row.toMode)"
                :disabled="row.toMode === currentMode"
              >
                快速切换
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <el-pagination
          v-if="fullHistory.length > pageSize"
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :total="fullHistory.length"
          :page-sizes="[10, 20, 50]"
          layout="total, sizes, prev, pager, next"
          style="margin-top: 16px; justify-content: center;"
        />
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useOrganizationStore, OrganizationMode } from '../store/organization';
import { useResponsive } from '../composables/useResponsive';
import { ElMessage, ElMessageBox } from 'element-plus';
import { 
  Clock, 
  ArrowRight, 
  More, 
  OfficeBuilding, 
  FolderOpened 
} from '@element-plus/icons-vue';

interface SwitchHistoryItem {
  id: string;
  fromMode: OrganizationMode;
  toMode: OrganizationMode;
  switchTime: number;
  reason?: string;
  duration?: number;
}

const organizationStore = useOrganizationStore();
const { isMobile } = useResponsive();

// 响应式数据
const showHistoryDialog = ref(false);
const currentPage = ref(1);
const pageSize = ref(20);
const fullHistory = ref<SwitchHistoryItem[]>([]);

// 计算属性
const currentMode = computed(() => organizationStore.currentMode);

const historyList = computed(() => {
  return fullHistory.value.slice(0, 5); // 只显示最近5条
});

const paginatedHistory = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value;
  const end = start + pageSize.value;
  return fullHistory.value.slice(start, end);
});

const totalSwitches = computed(() => fullHistory.value.length);

const todaySwitches = computed(() => {
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  return fullHistory.value.filter(item => 
    new Date(item.switchTime) >= today
  ).length;
});

const mostUsedMode = computed(() => {
  const modeCount = fullHistory.value.reduce((acc, item) => {
    acc[item.toMode] = (acc[item.toMode] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);
  
  const maxMode = Object.keys(modeCount).reduce((a, b) => 
    modeCount[a] > modeCount[b] ? a : b, 
    OrganizationMode.DEPARTMENT
  );
  
  return getModeText(maxMode as OrganizationMode);
});

// 方法
const getModeIcon = (mode: OrganizationMode) => {
  return mode === OrganizationMode.DEPARTMENT ? OfficeBuilding : FolderOpened;
};

const getModeText = (mode: OrganizationMode) => {
  return mode === OrganizationMode.DEPARTMENT ? '部门维度' : '项目维度';
};

const getModeTagType = (mode: OrganizationMode) => {
  return mode === OrganizationMode.DEPARTMENT ? 'primary' : 'success';
};

const formatTime = (timestamp: number) => {
  const now = Date.now();
  const diff = now - timestamp;
  
  if (diff < 60000) return '刚刚';
  if (diff < 3600000) return `${Math.floor(diff / 60000)}分钟前`;
  if (diff < 86400000) return `${Math.floor(diff / 3600000)}小时前`;
  return `${Math.floor(diff / 86400000)}天前`;
};

const formatDateTime = (timestamp: number) => {
  return new Date(timestamp).toLocaleString();
};

const handleQuickSwitch = (command: any) => {
  if (command === 'viewAll') {
    showHistoryDialog.value = true;
    return;
  }
  
  if (typeof command === 'object' && command.toMode) {
    quickSwitchTo(command.toMode);
  }
};

const quickSwitchTo = async (targetMode: OrganizationMode) => {
  if (targetMode === currentMode.value) {
    ElMessage.info('当前已是该模式');
    return;
  }
  
  try {
    await organizationStore.switchMode({
      targetMode,
      reason: '快速切换'
    });
    
    ElMessage.success('切换成功');
  } catch (error: any) {
    ElMessage.error(error.message || '切换失败');
  }
};

const clearHistory = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要清空所有切换历史记录吗？',
      '确认清空',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    );
    
    fullHistory.value = [];
    saveHistoryToStorage();
    ElMessage.success('历史记录已清空');
  } catch (error) {
    // 用户取消操作
  }
};

const addHistoryItem = (item: Omit<SwitchHistoryItem, 'id'>) => {
  const historyItem: SwitchHistoryItem = {
    ...item,
    id: `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  };
  
  fullHistory.value.unshift(historyItem);
  
  // 限制历史记录数量
  if (fullHistory.value.length > 100) {
    fullHistory.value = fullHistory.value.slice(0, 100);
  }
  
  saveHistoryToStorage();
};

const loadHistoryFromStorage = () => {
  try {
    const stored = localStorage.getItem('organization-switch-history');
    if (stored) {
      fullHistory.value = JSON.parse(stored);
    }
  } catch (error) {
    console.warn('加载切换历史失败:', error);
  }
};

const saveHistoryToStorage = () => {
  try {
    localStorage.setItem('organization-switch-history', JSON.stringify(fullHistory.value));
  } catch (error) {
    console.warn('保存切换历史失败:', error);
  }
};

// 监听组织模式切换
let lastMode = currentMode.value;
let switchStartTime = 0;

// 监听切换开始
const handleSwitchStart = () => {
  switchStartTime = Date.now();
};

// 监听切换完成
const handleSwitchComplete = () => {
  if (lastMode !== currentMode.value) {
    const duration = switchStartTime > 0 ? Date.now() - switchStartTime : undefined;
    
    addHistoryItem({
      fromMode: lastMode,
      toMode: currentMode.value,
      switchTime: Date.now(),
      duration
    });
    
    lastMode = currentMode.value;
    switchStartTime = 0;
  }
};

onMounted(() => {
  loadHistoryFromStorage();
  
  // 监听组织store的状态变化
  organizationStore.$subscribe((mutation, state) => {
    if (mutation.events?.some((event: any) => event.key === 'loading')) {
      if (state.loading) {
        handleSwitchStart();
      } else {
        handleSwitchComplete();
      }
    }
  });
});

// 暴露方法
defineExpose({
  addHistoryItem,
  clearHistory
});
</script>

<style lang="scss" scoped>
.organization-history {
  .history-trigger {
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 6px 8px;
    
    .el-icon {
      font-size: 14px;
    }
  }
}

.history-dropdown {
  min-width: 280px;
  
  .history-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 16px;
    border-bottom: 1px solid var(--el-border-color-light);
    font-weight: 500;
    color: var(--el-text-color-primary);
  }
  
  .empty-history {
    padding: 20px;
  }
  
  .history-list {
    max-height: 300px;
    overflow-y: auto;
  }
  
  .history-item {
    .history-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      width: 100%;
      
      .history-info {
        flex: 1;
        
        .history-mode {
          display: flex;
          align-items: center;
          gap: 4px;
          margin-bottom: 4px;
          
          .mode-icon {
            font-size: 14px;
            color: var(--el-color-primary);
          }
          
          .arrow-icon {
            font-size: 12px;
            color: var(--el-text-color-secondary);
          }
        }
        
        .history-desc {
          font-size: 12px;
          color: var(--el-text-color-regular);
        }
      }
      
      .history-time {
        font-size: 11px;
        color: var(--el-text-color-secondary);
        white-space: nowrap;
      }
    }
  }
  
  .view-all-item {
    border-top: 1px solid var(--el-border-color-light);
    color: var(--el-color-primary);
    
    .el-icon {
      margin-right: 4px;
    }
  }
}

.history-dialog {
  .history-stats {
    background: var(--el-fill-color-light);
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 20px;
  }
  
  .switch-direction {
    display: flex;
    align-items: center;
    gap: 8px;
    
    .arrow-icon {
      font-size: 12px;
      color: var(--el-text-color-secondary);
    }
  }
}

// 移动端适配
@media (max-width: 768px) {
  .history-dropdown {
    min-width: 260px;
  }
  
  .history-dialog {
    .history-stats {
      padding: 16px;
    }
  }
}
</style>
