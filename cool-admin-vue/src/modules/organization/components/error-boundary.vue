<template>
  <div class="error-boundary">
    <div v-if="hasError" class="error-container">
      <div class="error-content">
        <div class="error-icon">
          <el-icon><WarningFilled /></el-icon>
        </div>
        
        <div class="error-info">
          <h3>{{ errorTitle }}</h3>
          <p>{{ errorMessage }}</p>
          
          <div v-if="showDetails" class="error-details">
            <el-collapse>
              <el-collapse-item title="错误详情" name="details">
                <pre>{{ errorDetails }}</pre>
              </el-collapse-item>
            </el-collapse>
          </div>
        </div>
        
        <div class="error-actions">
          <el-button type="primary" @click="handleRetry">
            <el-icon><Refresh /></el-icon>
            重试
          </el-button>
          
          <el-button @click="handleReset">
            <el-icon><RefreshLeft /></el-icon>
            重置
          </el-button>
          
          <el-button v-if="!showDetails" text @click="showDetails = true">
            查看详情
          </el-button>
          
          <el-button text @click="handleReport">
            <el-icon><Warning /></el-icon>
            报告问题
          </el-button>
        </div>
      </div>
    </div>
    
    <slot v-else />
  </div>
</template>

<script setup lang="ts">
import { ref, onErrorCaptured, nextTick } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { WarningFilled, Refresh, RefreshLeft, Warning } from '@element-plus/icons-vue';

interface Props {
  fallbackTitle?: string;
  fallbackMessage?: string;
  enableRetry?: boolean;
  enableReset?: boolean;
  enableReport?: boolean;
  onError?: (error: Error, instance: any, info: string) => void;
  onRetry?: () => void;
  onReset?: () => void;
}

const props = withDefaults(defineProps<Props>(), {
  fallbackTitle: '组织模块出现错误',
  fallbackMessage: '抱歉，组织管理功能遇到了问题。您可以尝试刷新页面或联系管理员。',
  enableRetry: true,
  enableReset: true,
  enableReport: true
});

const emit = defineEmits<{
  error: [error: Error, info: string];
  retry: [];
  reset: [];
}>();

// 响应式数据
const hasError = ref(false);
const errorTitle = ref(props.fallbackTitle);
const errorMessage = ref(props.fallbackMessage);
const errorDetails = ref('');
const showDetails = ref(false);
const retryCount = ref(0);

// 错误捕获
onErrorCaptured((error: Error, instance: any, info: string) => {
  console.error('Error Boundary caught an error:', error, info);
  
  hasError.value = true;
  
  // 根据错误类型设置不同的提示
  if (error.message.includes('Pinia')) {
    errorTitle.value = '状态管理错误';
    errorMessage.value = '组织状态管理出现问题，请尝试刷新页面。';
  } else if (error.message.includes('permission')) {
    errorTitle.value = '权限验证错误';
    errorMessage.value = '权限验证失败，请检查您的访问权限。';
  } else if (error.message.includes('network') || error.message.includes('fetch')) {
    errorTitle.value = '网络连接错误';
    errorMessage.value = '网络连接出现问题，请检查网络连接后重试。';
  } else {
    errorTitle.value = props.fallbackTitle;
    errorMessage.value = props.fallbackMessage;
  }
  
  errorDetails.value = `错误信息: ${error.message}\n错误堆栈: ${error.stack}\n组件信息: ${info}`;
  
  // 调用错误回调
  props.onError?.(error, instance, info);
  emit('error', error, info);
  
  // 阻止错误继续传播
  return false;
});

// 重试处理
const handleRetry = async () => {
  if (retryCount.value >= 3) {
    ElMessage.warning('重试次数过多，请尝试重置或刷新页面');
    return;
  }
  
  retryCount.value++;
  
  try {
    // 重置错误状态
    hasError.value = false;
    showDetails.value = false;
    
    // 等待下一个tick确保组件重新渲染
    await nextTick();
    
    // 调用重试回调
    props.onRetry?.();
    emit('retry');
    
    ElMessage.success('重试成功');
  } catch (error) {
    hasError.value = true;
    ElMessage.error('重试失败，请尝试其他解决方案');
  }
};

// 重置处理
const handleReset = async () => {
  try {
    await ElMessageBox.confirm(
      '重置将清除当前的组织状态和缓存数据，确定要继续吗？',
      '确认重置',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    );
    
    // 清除本地存储
    localStorage.removeItem('cool-organization-store');
    sessionStorage.clear();
    
    // 重置状态
    hasError.value = false;
    showDetails.value = false;
    retryCount.value = 0;
    
    // 调用重置回调
    props.onReset?.();
    emit('reset');
    
    // 刷新页面
    setTimeout(() => {
      window.location.reload();
    }, 500);
    
    ElMessage.success('重置成功，页面即将刷新');
  } catch (error) {
    // 用户取消操作
  }
};

// 报告问题处理
const handleReport = async () => {
  try {
    const reportData = {
      title: errorTitle.value,
      message: errorMessage.value,
      details: errorDetails.value,
      userAgent: navigator.userAgent,
      url: window.location.href,
      timestamp: new Date().toISOString(),
      retryCount: retryCount.value
    };
    
    // 这里可以发送错误报告到服务器
    console.log('Error Report:', reportData);
    
    // 复制错误信息到剪贴板
    if (navigator.clipboard) {
      await navigator.clipboard.writeText(JSON.stringify(reportData, null, 2));
      ElMessage.success('错误信息已复制到剪贴板，请联系技术支持');
    } else {
      ElMessage.info('请手动复制错误详情并联系技术支持');
    }
  } catch (error) {
    ElMessage.error('报告问题失败');
  }
};

// 暴露方法
defineExpose({
  reset: () => {
    hasError.value = false;
    showDetails.value = false;
    retryCount.value = 0;
  },
  triggerError: (error: Error) => {
    hasError.value = true;
    errorDetails.value = error.stack || error.message;
  }
});
</script>

<style lang="scss" scoped>
.error-boundary {
  .error-container {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 400px;
    padding: 40px 20px;
    
    .error-content {
      max-width: 600px;
      text-align: center;
      
      .error-icon {
        margin-bottom: 24px;
        
        .el-icon {
          font-size: 64px;
          color: var(--el-color-warning);
        }
      }
      
      .error-info {
        margin-bottom: 32px;
        
        h3 {
          margin: 0 0 12px 0;
          font-size: 24px;
          font-weight: 600;
          color: var(--el-text-color-primary);
        }
        
        p {
          margin: 0 0 20px 0;
          font-size: 16px;
          line-height: 1.6;
          color: var(--el-text-color-regular);
        }
        
        .error-details {
          text-align: left;
          margin-top: 20px;
          
          pre {
            background: var(--el-fill-color-light);
            padding: 16px;
            border-radius: 8px;
            font-size: 12px;
            line-height: 1.4;
            overflow-x: auto;
            white-space: pre-wrap;
            word-break: break-all;
          }
        }
      }
      
      .error-actions {
        display: flex;
        gap: 12px;
        justify-content: center;
        flex-wrap: wrap;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .error-boundary {
    .error-container {
      min-height: 300px;
      padding: 20px 16px;
      
      .error-content {
        .error-icon {
          .el-icon {
            font-size: 48px;
          }
        }
        
        .error-info {
          h3 {
            font-size: 20px;
          }
          
          p {
            font-size: 14px;
          }
        }
        
        .error-actions {
          flex-direction: column;
          align-items: center;
          
          .el-button {
            width: 100%;
            max-width: 200px;
          }
        }
      }
    }
  }
}
</style>
