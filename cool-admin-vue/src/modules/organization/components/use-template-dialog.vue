<template>
  <el-dialog
    :model-value="modelValue"
    @update:model-value="$emit('update:modelValue', $event)"
    title="使用模板创建项目"
    width="600px"
    :close-on-click-modal="false"
  >
    <div v-if="template" class="use-template">
      <el-form :model="form" label-width="100px">
        <el-form-item label="项目名称" required>
          <el-input v-model="form.projectName" placeholder="请输入项目名称" />
        </el-form-item>
        
        <el-form-item label="项目描述">
          <el-input v-model="form.description" type="textarea" :rows="3" />
        </el-form-item>
      </el-form>
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="$emit('update:modelValue', false)">取消</el-button>
        <el-button type="primary" @click="handleCreate">创建项目</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { reactive } from 'vue';
import { ElMessage } from 'element-plus';

interface Props {
  modelValue: boolean;
  template: any;
}

defineProps<Props>();

const emit = defineEmits<{
  'update:modelValue': [value: boolean];
  created: [project: any];
}>();

const form = reactive({
  projectName: '',
  description: ''
});

const handleCreate = () => {
  if (!form.projectName) {
    ElMessage.warning('请输入项目名称');
    return;
  }
  
  const project = {
    id: Date.now(),
    name: form.projectName,
    description: form.description
  };
  
  emit('created', project);
  emit('update:modelValue', false);
  
  // 重置表单
  form.projectName = '';
  form.description = '';
};
</script>

<style lang="scss" scoped>
.use-template {
  padding: 20px 0;
}
</style>
