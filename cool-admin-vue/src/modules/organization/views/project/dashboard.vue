<template>
  <div class="project-dashboard">
    <!-- 欢迎区域 -->
    <div class="welcome-section">
      <el-card class="welcome-card">
        <div class="welcome-content">
          <div class="welcome-text">
            <h2>项目工作台</h2>
            <p>欢迎使用项目维度管理，这里是您的项目协作中心</p>
          </div>
          <div class="welcome-actions">
            <el-button type="primary" @click="createProject">
              <el-icon><Plus /></el-icon>
              创建项目
            </el-button>
            <el-button @click="joinProject">
              <el-icon><UserPlus /></el-icon>
              加入项目
            </el-button>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-section">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon project">
                <el-icon><FolderOpened /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ stats.totalProjects }}</div>
                <div class="stat-label">我的项目</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon task">
                <el-icon><List /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ stats.totalTasks }}</div>
                <div class="stat-label">待处理任务</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon member">
                <el-icon><User /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ stats.totalMembers }}</div>
                <div class="stat-label">协作成员</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon progress">
                <el-icon><TrendCharts /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ stats.avgProgress }}%</div>
                <div class="stat-label">平均进度</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 项目列表 -->
    <div class="projects-section">
      <el-card>
        <template #header>
          <div class="section-header">
            <h3>我的项目</h3>
            <el-button text @click="viewAllProjects">查看全部</el-button>
          </div>
        </template>
        
        <div class="project-grid">
          <div 
            v-for="project in recentProjects" 
            :key="project.id"
            class="project-card"
            @click="openProject(project)"
          >
            <div class="project-header">
              <div class="project-name">{{ project.projectName }}</div>
              <el-tag :type="getStatusType(project.status)" size="small">
                {{ getStatusText(project.status) }}
              </el-tag>
            </div>
            <div class="project-description">{{ project.description }}</div>
            <div class="project-progress">
              <el-progress :percentage="project.progress" :show-text="false" />
              <span class="progress-text">{{ project.progress }}%</span>
            </div>
            <div class="project-footer">
              <div class="project-members">
                <el-avatar-group :max="3" size="small">
                  <el-avatar 
                    v-for="member in project.members" 
                    :key="member.id"
                    :src="member.avatar"
                  />
                </el-avatar-group>
              </div>
              <div class="project-date">
                {{ formatDate(project.updateTime) }}
              </div>
            </div>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 最近任务 -->
    <div class="tasks-section">
      <el-card>
        <template #header>
          <div class="section-header">
            <h3>最近任务</h3>
            <el-button text @click="viewAllTasks">查看全部</el-button>
          </div>
        </template>
        
        <el-table :data="recentTasks" style="width: 100%">
          <el-table-column prop="taskName" label="任务名称" />
          <el-table-column prop="projectName" label="所属项目" />
          <el-table-column prop="status" label="状态">
            <template #default="{ row }">
              <el-tag :type="getTaskStatusType(row.status)" size="small">
                {{ getTaskStatusText(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="dueDate" label="截止时间">
            <template #default="{ row }">
              {{ formatDate(row.dueDate) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="120">
            <template #default="{ row }">
              <el-button text size="small" @click="viewTask(row)">查看</el-button>
              <el-button text size="small" @click="editTask(row)">编辑</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import { Plus, UserPlus, FolderOpened, List, User, TrendCharts } from '@element-plus/icons-vue';
import { useCool } from '/@/cool';
import { useRouter } from 'vue-router';

defineOptions({
  name: 'project-dashboard'
});

const { service } = useCool();
const router = useRouter();

// 统计数据
const stats = reactive({
  totalProjects: 0,
  totalTasks: 0,
  totalMembers: 0,
  avgProgress: 0
});

// 最近项目
const recentProjects = ref([]);

// 最近任务
const recentTasks = ref([]);

// 方法
const createProject = () => {
  router.push('/project/create');
};

const joinProject = () => {
  router.push('/project/join');
};

const openProject = (project: any) => {
  router.push(`/project/detail/${project.id}`);
};

const viewAllProjects = () => {
  router.push('/project/list');
};

const viewAllTasks = () => {
  router.push('/project/task');
};

const viewTask = (task: any) => {
  router.push(`/project/task/${task.id}`);
};

const editTask = (task: any) => {
  router.push(`/project/task/edit/${task.id}`);
};

const getStatusType = (status: number) => {
  const types = ['info', 'success', 'warning', 'danger'];
  return types[status - 1] || 'info';
};

const getStatusText = (status: number) => {
  const texts = ['进行中', '已完成', '已暂停', '已取消'];
  return texts[status - 1] || '未知';
};

const getTaskStatusType = (status: number) => {
  const types = ['warning', 'success', 'info'];
  return types[status - 1] || 'info';
};

const getTaskStatusText = (status: number) => {
  const texts = ['待处理', '已完成', '进行中'];
  return texts[status - 1] || '未知';
};

const formatDate = (date: string) => {
  return new Date(date).toLocaleDateString();
};

// 加载数据
const loadDashboardData = async () => {
  try {
    // 检查服务是否可用
    if (!service.organization?.project?.dashboard) {
      console.warn('项目仪表板服务不可用，使用模拟数据');
      // 使用模拟数据
      Object.assign(stats, {
        totalProjects: 12,
        totalTasks: 45,
        totalMembers: 8,
        avgProgress: 75
      });

      recentProjects.value = [
        {
          id: 1,
          projectName: '示例项目1',
          description: '这是一个示例项目',
          status: 1,
          progress: 80,
          members: [
            { id: 1, avatar: '', name: '用户1' }
          ],
          updateTime: new Date().toISOString()
        }
      ];

      recentTasks.value = [
        {
          id: 1,
          taskName: '示例任务1',
          projectName: '示例项目1',
          status: 1,
          dueDate: new Date().toISOString()
        }
      ];
      return;
    }

    // 加载统计数据
    const statsRes = await service.organization.project.dashboard.stats();
    Object.assign(stats, statsRes);

    // 加载最近项目
    const projectsRes = await service.organization.project.dashboard.recentProjects();
    recentProjects.value = projectsRes;

    // 加载最近任务
    const tasksRes = await service.organization.project.dashboard.recentTasks();
    recentTasks.value = tasksRes;
  } catch (error) {
    console.warn('加载数据失败，使用模拟数据:', error);
    // 使用模拟数据作为降级方案
    Object.assign(stats, {
      totalProjects: 0,
      totalTasks: 0,
      totalMembers: 0,
      avgProgress: 0
    });
    recentProjects.value = [];
    recentTasks.value = [];
  }
};

onMounted(() => {
  loadDashboardData();
});
</script>

<style lang="scss" scoped>
.project-dashboard {
  padding: 20px;

  .welcome-section {
    margin-bottom: 20px;

    .welcome-card {
      .welcome-content {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .welcome-text {
          h2 {
            margin: 0 0 8px 0;
            color: var(--el-text-color-primary);
          }

          p {
            margin: 0;
            color: var(--el-text-color-regular);
          }
        }

        .welcome-actions {
          display: flex;
          gap: 12px;
        }
      }
    }
  }

  .stats-section {
    margin-bottom: 20px;

    .stat-card {
      .stat-content {
        display: flex;
        align-items: center;

        .stat-icon {
          width: 48px;
          height: 48px;
          border-radius: 8px;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 16px;
          font-size: 24px;

          &.project {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
          }

          &.task {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
          }

          &.member {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
          }

          &.progress {
            background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
            color: white;
          }
        }

        .stat-info {
          .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: var(--el-text-color-primary);
          }

          .stat-label {
            font-size: 14px;
            color: var(--el-text-color-regular);
          }
        }
      }
    }
  }

  .projects-section,
  .tasks-section {
    margin-bottom: 20px;

    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      h3 {
        margin: 0;
      }
    }

    .project-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
      gap: 16px;

      .project-card {
        padding: 16px;
        border: 1px solid var(--el-border-color-light);
        border-radius: 8px;
        cursor: pointer;
        transition: all 0.3s;

        &:hover {
          border-color: var(--el-color-primary);
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .project-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 8px;

          .project-name {
            font-weight: bold;
            color: var(--el-text-color-primary);
          }
        }

        .project-description {
          color: var(--el-text-color-regular);
          font-size: 14px;
          margin-bottom: 12px;
          line-height: 1.4;
        }

        .project-progress {
          display: flex;
          align-items: center;
          margin-bottom: 12px;

          .el-progress {
            flex: 1;
            margin-right: 8px;
          }

          .progress-text {
            font-size: 12px;
            color: var(--el-text-color-regular);
          }
        }

        .project-footer {
          display: flex;
          justify-content: space-between;
          align-items: center;

          .project-date {
            font-size: 12px;
            color: var(--el-text-color-secondary);
          }
        }
      }
    }
  }
}
</style>
