<template>
  <div class="project-member">
    <cl-crud ref="Crud">
      <cl-row>
        <!-- 刷新按钮 -->
        <cl-refresh-btn />
        <!-- 添加成员按钮 -->
        <el-button 
          type="primary" 
          @click="showAddMemberDialog = true"
          v-dual-permission="'project:member:add'"
        >
          <el-icon><UserPlus /></el-icon>
          添加成员
        </el-button>
        <!-- 批量移除按钮 -->
        <cl-multi-delete-btn v-dual-permission="'project:member:remove'" />
        <cl-flex1 />
        <!-- 条件搜索 -->
        <cl-search ref="Search" />
      </cl-row>

      <cl-row>
        <!-- 数据表格 -->
        <cl-table ref="Table" />
      </cl-row>

      <cl-row>
        <cl-flex1 />
        <!-- 分页控件 -->
        <cl-pagination />
      </cl-row>
    </cl-crud>

    <!-- 添加成员对话框 -->
    <el-dialog
      v-model="showAddMemberDialog"
      title="添加项目成员"
      width="600px"
      :close-on-click-modal="false"
    >
      <el-form ref="addMemberFormRef" :model="addMemberForm" label-width="100px">
        <el-form-item label="选择用户" prop="userIds" required>
          <el-select
            v-model="addMemberForm.userIds"
            multiple
            filterable
            remote
            reserve-keyword
            placeholder="请输入用户名或邮箱搜索"
            :remote-method="searchUsers"
            :loading="userSearchLoading"
            style="width: 100%"
          >
            <el-option
              v-for="user in userOptions"
              :key="user.id"
              :label="`${user.nickName} (${user.email})`"
              :value="user.id"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="项目角色" prop="roleCode" required>
          <el-select v-model="addMemberForm.roleCode" style="width: 100%">
            <el-option
              v-for="role in projectRoles"
              :key="role.code"
              :label="role.name"
              :value="role.code"
            >
              <div style="display: flex; justify-content: space-between;">
                <span>{{ role.name }}</span>
                <span style="color: var(--el-text-color-secondary); font-size: 12px;">
                  {{ role.description }}
                </span>
              </div>
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="权限到期时间" prop="expireTime">
          <el-date-picker
            v-model="addMemberForm.expireTime"
            type="datetime"
            placeholder="选择到期时间（可选）"
            value-format="YYYY-MM-DD HH:mm:ss"
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="addMemberForm.remark"
            type="textarea"
            :rows="3"
            placeholder="添加备注信息（可选）"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showAddMemberDialog = false">取消</el-button>
          <el-button 
            type="primary" 
            :loading="addMemberLoading"
            @click="handleAddMember"
          >
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 编辑成员对话框 -->
    <el-dialog
      v-model="showEditMemberDialog"
      title="编辑成员信息"
      width="500px"
      :close-on-click-modal="false"
    >
      <el-form ref="editMemberFormRef" :model="editMemberForm" label-width="100px">
        <el-form-item label="项目角色" prop="roleCode" required>
          <el-select v-model="editMemberForm.roleCode" style="width: 100%">
            <el-option
              v-for="role in projectRoles"
              :key="role.code"
              :label="role.name"
              :value="role.code"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="权限到期时间" prop="expireTime">
          <el-date-picker
            v-model="editMemberForm.expireTime"
            type="datetime"
            placeholder="选择到期时间（可选）"
            value-format="YYYY-MM-DD HH:mm:ss"
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="editMemberForm.status">
            <el-radio :label="1">正常</el-radio>
            <el-radio :label="2">暂停</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showEditMemberDialog = false">取消</el-button>
          <el-button 
            type="primary" 
            :loading="editMemberLoading"
            @click="handleEditMember"
          >
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
defineOptions({
  name: "project-member",
});

import { ref, reactive, onMounted } from 'vue';
import { useCrud, useTable, useSearch } from "@cool-vue/crud";
import { useCool } from "/@/cool";
import { useI18n } from "vue-i18n";
import { ElMessage, ElMessageBox } from 'element-plus';
import { UserPlus } from '@element-plus/icons-vue';
import { useRoute } from 'vue-router';

const { service } = useCool();
const { t } = useI18n();
const route = useRoute();

// 项目ID
const projectId = ref(route.params.id);

// 对话框状态
const showAddMemberDialog = ref(false);
const showEditMemberDialog = ref(false);

// 加载状态
const addMemberLoading = ref(false);
const editMemberLoading = ref(false);
const userSearchLoading = ref(false);

// 用户搜索选项
const userOptions = ref([]);

// 项目角色选项
const projectRoles = ref([
  { code: 'PROJECT_OWNER', name: '项目负责人', description: '完全控制权限' },
  { code: 'PROJECT_ADMIN', name: '项目管理员', description: '项目管理权限' },
  { code: 'PROJECT_MEMBER', name: '项目成员', description: '项目参与权限' },
  { code: 'PROJECT_VIEWER', name: '项目观察者', description: '项目只读权限' }
]);

// 添加成员表单
const addMemberForm = reactive({
  userIds: [],
  roleCode: 'PROJECT_MEMBER',
  expireTime: '',
  remark: ''
});

// 编辑成员表单
const editMemberForm = reactive({
  id: null,
  roleCode: '',
  expireTime: '',
  status: 1
});

// cl-table
const Table = useTable({
  columns: [
    { type: "selection" },
    { 
      label: t("用户名"), 
      prop: "userName", 
      minWidth: 120
    },
    { 
      label: t("姓名"), 
      prop: "nickName", 
      minWidth: 120
    },
    { 
      label: t("邮箱"), 
      prop: "email", 
      minWidth: 180
    },
    { 
      label: t("项目角色"), 
      prop: "roleCode", 
      minWidth: 120,
      dict: projectRoles.value.map(role => ({
        label: role.name,
        value: role.code
      }))
    },
    { 
      label: t("加入时间"), 
      prop: "joinTime", 
      minWidth: 170,
      component: { name: "cl-date-text" }
    },
    { 
      label: t("到期时间"), 
      prop: "expireTime", 
      minWidth: 170,
      component: { name: "cl-date-text" }
    },
    { 
      label: t("状态"), 
      prop: "status", 
      minWidth: 100,
      dict: [
        { label: "正常", value: 1, type: "success" },
        { label: "暂停", value: 2, type: "warning" },
        { label: "已移除", value: 3, type: "danger" }
      ]
    },
    { 
      type: "op", 
      buttons: [
        {
          label: "编辑",
          type: "primary",
          onClick: ({ scope }) => {
            editMemberForm.id = scope.row.id;
            editMemberForm.roleCode = scope.row.roleCode;
            editMemberForm.expireTime = scope.row.expireTime;
            editMemberForm.status = scope.row.status;
            showEditMemberDialog.value = true;
          }
        },
        {
          label: "移除",
          type: "danger",
          onClick: ({ scope }) => {
            handleRemoveMember(scope.row);
          }
        }
      ]
    },
  ],
});

// cl-search
const Search = useSearch({
  items: [
    {
      label: t("用户名"),
      prop: "userName",
      component: { name: "el-input", props: { clearable: true } }
    },
    {
      label: t("姓名"),
      prop: "nickName",
      component: { name: "el-input", props: { clearable: true } }
    },
    {
      label: t("项目角色"),
      prop: "roleCode",
      component: { 
        name: "el-select",
        props: {
          clearable: true,
          options: projectRoles.value.map(role => ({
            label: role.name,
            value: role.code
          }))
        }
      }
    },
    {
      label: t("状态"),
      prop: "status",
      component: { 
        name: "el-select",
        props: {
          clearable: true,
          options: [
            { label: "正常", value: 1 },
            { label: "暂停", value: 2 },
            { label: "已移除", value: 3 }
          ]
        }
      }
    }
  ]
});

// cl-crud
const Crud = useCrud(
  {
    service: service.organization?.project?.member || {
      // 模拟服务，避免报错
      list: () => Promise.resolve([]),
      add: () => Promise.resolve({}),
      update: () => Promise.resolve({}),
      delete: () => Promise.resolve({}),
      batchAdd: () => Promise.resolve({}),
      remove: () => Promise.resolve({}),
      searchUsers: () => Promise.resolve([])
    },
    dict: {
      api: {
        list: 'list',
        add: 'add',
        update: 'update',
        delete: 'delete'
      }
    }
  },
  (app) => {
    app.refresh({
      projectId: projectId.value
    });
  },
);

// 搜索用户
const searchUsers = async (query: string) => {
  if (!query) {
    userOptions.value = [];
    return;
  }

  userSearchLoading.value = true;
  try {
    if (service.organization?.project?.member?.searchUsers) {
      const result = await service.organization.project.member.searchUsers({
        keyword: query,
        projectId: projectId.value
      });
      userOptions.value = result;
    } else {
      // 模拟数据
      userOptions.value = [
        { id: 1, nickName: '示例用户', email: '<EMAIL>' }
      ];
    }
  } catch (error) {
    console.warn('搜索用户失败:', error);
    userOptions.value = [];
  } finally {
    userSearchLoading.value = false;
  }
};

// 添加成员
const handleAddMember = async () => {
  if (!addMemberForm.userIds.length) {
    ElMessage.warning('请选择要添加的用户');
    return;
  }

  addMemberLoading.value = true;
  try {
    await service.organization.project.member.batchAdd({
      projectId: projectId.value,
      userIds: addMemberForm.userIds,
      roleCode: addMemberForm.roleCode,
      expireTime: addMemberForm.expireTime,
      remark: addMemberForm.remark
    });

    ElMessage.success('添加成员成功');
    showAddMemberDialog.value = false;
    
    // 重置表单
    Object.assign(addMemberForm, {
      userIds: [],
      roleCode: 'PROJECT_MEMBER',
      expireTime: '',
      remark: ''
    });

    // 刷新列表
    Crud.value?.refresh();
  } catch (error) {
    ElMessage.error('添加成员失败');
  } finally {
    addMemberLoading.value = false;
  }
};

// 编辑成员
const handleEditMember = async () => {
  editMemberLoading.value = true;
  try {
    await service.organization.project.member.update({
      id: editMemberForm.id,
      roleCode: editMemberForm.roleCode,
      expireTime: editMemberForm.expireTime,
      status: editMemberForm.status
    });

    ElMessage.success('更新成员信息成功');
    showEditMemberDialog.value = false;
    
    // 刷新列表
    Crud.value?.refresh();
  } catch (error) {
    ElMessage.error('更新成员信息失败');
  } finally {
    editMemberLoading.value = false;
  }
};

// 移除成员
const handleRemoveMember = async (member: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要移除成员 "${member.nickName}" 吗？`,
      '确认移除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    );

    await service.organization.project.member.remove({
      id: member.id
    });

    ElMessage.success('移除成员成功');
    Crud.value?.refresh();
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('移除成员失败');
    }
  }
};

// 刷新
function refresh(params?: any) {
  Crud.value?.refresh({
    ...params,
    projectId: projectId.value
  });
}

onMounted(() => {
  if (!projectId.value) {
    ElMessage.error('项目ID不能为空');
    return;
  }
});
</script>

<style lang="scss" scoped>
.project-member {
  height: 100%;
}
</style>
