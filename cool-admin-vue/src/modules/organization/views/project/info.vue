<template>
	<cl-crud ref="Crud">
		<cl-row>
			<!-- 刷新按钮 -->
			<cl-refresh-btn />
			<!-- 新增按钮 -->
			<cl-add-btn />
			<!-- 删除按钮 -->
			<cl-multi-delete-btn />
			<cl-flex1 />
			<!-- 条件搜索 -->
			<cl-search ref="Search" />
		</cl-row>

		<cl-row>
			<!-- 数据表格 -->
			<cl-table ref="Table" />
		</cl-row>

		<cl-row>
			<cl-flex1 />
			<!-- 分页控件 -->
			<cl-pagination />
		</cl-row>

		<!-- 新增、编辑 -->
		<cl-upsert ref="Upsert" />
	</cl-crud>
</template>

<script lang="ts" setup>
defineOptions({
	name: "organization-project-info",
});

import { useCrud, useTable, useUpsert, useSearch } from "@cool-vue/crud";
import { useCool } from "/@/cool";
import { useI18n } from "vue-i18n";

const { service } = useCool();
const { t } = useI18n();

// cl-upsert
const Upsert = useUpsert({
	items: [
		{
			label: t("项目名称"),
			prop: "projectName",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
			required: true,
		},
		{
			label: t("项目编码"),
			prop: "projectCode",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
			required: true,
		},
		{
			label: t("项目描述"),
			prop: "description",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
		},
		{
			label: t("项目状态"),
			prop: "status",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
		},
		{
			label: t("项目优先级"),
			prop: "priority",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
		},
		{
			label: t("选择项目负责人"),
			prop: "ownerId",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
			required: true,
		},
		{
			label: t("选择创建人"),
			prop: "creatorId",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
			required: true,
		},
		{
			label: t("计划开始时间"),
			prop: "plannedStartTime",
			component: {
				name: "el-date-picker",
				props: { type: "datetime", valueFormat: "YYYY-MM-DD HH:mm:ss" },
			},
			span: 12,
		},
		{
			label: t("计划结束时间"),
			prop: "plannedEndTime",
			component: {
				name: "el-date-picker",
				props: { type: "datetime", valueFormat: "YYYY-MM-DD HH:mm:ss" },
			},
			span: 12,
		},
		{
			label: t("实际开始时间"),
			prop: "actualStartTime",
			component: {
				name: "el-date-picker",
				props: { type: "datetime", valueFormat: "YYYY-MM-DD HH:mm:ss" },
			},
			span: 12,
		},
		{
			label: t("实际结束时间"),
			prop: "actualEndTime",
			component: {
				name: "el-date-picker",
				props: { type: "datetime", valueFormat: "YYYY-MM-DD HH:mm:ss" },
			},
			span: 12,
		},
		{
			label: t("项目预算"),
			prop: "budget",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
		},
		{
			label: t("项目标签"),
			prop: "tags",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
		},
		{
			label: t("项目附件"),
			prop: "attachments",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
		},
		{
			label: t("是否启用"),
			prop: "enabled",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
		},
		{
			label: t("排序号"),
			prop: "orderNum",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
		},
		{
			label: t("备注"),
			prop: "remark",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
		},
	],
});

// cl-table
const Table = useTable({
	columns: [
		{ type: "selection" },
		{ label: t("项目名称"), prop: "projectName", minWidth: 120 },
		{ label: t("项目编码"), prop: "projectCode", minWidth: 120 },
		{ label: t("项目描述"), prop: "description", minWidth: 120 },
		{ label: t("项目状态"), prop: "status", minWidth: 120 },
		{ label: t("项目优先级"), prop: "priority", minWidth: 120 },
		{ label: t("项目负责人ID"), prop: "ownerId", minWidth: 120 },
		{ label: t("创建人ID"), prop: "creatorId", minWidth: 120 },
		{
			label: t("计划开始时间"),
			prop: "plannedStartTime",
			minWidth: 170,
			sortable: "custom",
			component: { name: "cl-date-text" },
		},
		{
			label: t("计划结束时间"),
			prop: "plannedEndTime",
			minWidth: 170,
			sortable: "custom",
			component: { name: "cl-date-text" },
		},
		{
			label: t("实际开始时间"),
			prop: "actualStartTime",
			minWidth: 170,
			sortable: "custom",
			component: { name: "cl-date-text" },
		},
		{
			label: t("实际结束时间"),
			prop: "actualEndTime",
			minWidth: 170,
			sortable: "custom",
			component: { name: "cl-date-text" },
		},
		{ label: t("项目预算"), prop: "budget", minWidth: 120 },
		{ label: t("项目标签"), prop: "tags", minWidth: 120 },
		{ label: t("项目附件"), prop: "attachments", minWidth: 120 },
		{ label: t("是否启用"), prop: "enabled", minWidth: 120 },
		{ label: t("排序号"), prop: "orderNum", minWidth: 120 },
		{ label: t("备注"), prop: "remark", minWidth: 120 },
		{
			label: t("创建时间"),
			prop: "createTime",
			minWidth: 170,
			sortable: "desc",
			component: { name: "cl-date-text" },
		},
		{
			label: t("更新时间"),
			prop: "updateTime",
			minWidth: 170,
			sortable: "custom",
			component: { name: "cl-date-text" },
		},
		{ type: "op", buttons: ["edit", "delete"] },
	],
});

// cl-search
const Search = useSearch();

// cl-crud
const Crud = useCrud(
	{
		service: service.organization.project.info,
	},
	(app) => {
		app.refresh();
	},
);

// 刷新
function refresh(params?: any) {
	Crud.value?.refresh(params);
}
</script>
