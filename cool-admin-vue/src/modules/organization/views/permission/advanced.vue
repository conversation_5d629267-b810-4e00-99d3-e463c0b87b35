<template>
  <div class="advanced-permission">
    <!-- 权限管理头部 -->
    <div class="permission-header">
      <div class="header-left">
        <h2>高级权限管理</h2>
        <p>细粒度权限控制和角色管理</p>
      </div>
      
      <div class="header-right">
        <el-button type="primary" @click="showCreateRoleDialog = true">
          <el-icon><Plus /></el-icon>
          创建角色
        </el-button>
        
        <el-button @click="showPermissionMatrixDialog = true">
          <el-icon><Grid /></el-icon>
          权限矩阵
        </el-button>
      </div>
    </div>

    <!-- 权限概览 -->
    <div class="permission-overview">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="overview-card">
            <el-statistic title="总角色数" :value="overviewData.totalRoles" />
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="overview-card">
            <el-statistic title="权限点数" :value="overviewData.totalPermissions" />
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="overview-card">
            <el-statistic title="活跃用户" :value="overviewData.activeUsers" />
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="overview-card">
            <el-statistic title="权限组数" :value="overviewData.permissionGroups" />
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 主要内容区域 -->
    <div class="permission-content">
      <el-row :gutter="20">
        <!-- 角色管理 -->
        <el-col :span="12">
          <el-card title="角色管理">
            <template #header>
              <div class="card-header">
                <span>角色管理</span>
                <el-input
                  v-model="roleSearchKeyword"
                  placeholder="搜索角色"
                  size="small"
                  style="width: 200px;"
                  clearable
                >
                  <template #prefix>
                    <el-icon><Search /></el-icon>
                  </template>
                </el-input>
              </div>
            </template>
            
            <div class="role-list">
              <div 
                v-for="role in filteredRoles" 
                :key="role.id"
                class="role-item"
                :class="{ active: selectedRole?.id === role.id }"
                @click="selectRole(role)"
              >
                <div class="role-info">
                  <div class="role-name">{{ role.name }}</div>
                  <div class="role-description">{{ role.description }}</div>
                  <div class="role-meta">
                    <el-tag size="small" :type="getRoleType(role.type)">
                      {{ getRoleTypeText(role.type) }}
                    </el-tag>
                    <span class="user-count">{{ role.userCount }}人</span>
                  </div>
                </div>
                
                <div class="role-actions">
                  <el-dropdown @command="handleRoleAction">
                    <el-icon class="role-menu"><MoreFilled /></el-icon>
                    <template #dropdown>
                      <el-dropdown-menu>
                        <el-dropdown-item :command="`edit-${role.id}`">
                          编辑
                        </el-dropdown-item>
                        <el-dropdown-item :command="`copy-${role.id}`">
                          复制
                        </el-dropdown-item>
                        <el-dropdown-item :command="`delete-${role.id}`" divided>
                          删除
                        </el-dropdown-item>
                      </el-dropdown-menu>
                    </template>
                  </el-dropdown>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <!-- 权限配置 -->
        <el-col :span="12">
          <el-card title="权限配置">
            <template #header>
              <div class="card-header">
                <span>权限配置</span>
                <div v-if="selectedRole">
                  <el-button size="small" @click="saveRolePermissions">
                    <el-icon><Check /></el-icon>
                    保存
                  </el-button>
                </div>
              </div>
            </template>
            
            <div v-if="!selectedRole" class="no-selection">
              <el-empty description="请选择一个角色来配置权限" />
            </div>
            
            <div v-else class="permission-config">
              <div class="role-info-header">
                <h4>{{ selectedRole.name }}</h4>
                <p>{{ selectedRole.description }}</p>
              </div>
              
              <el-tabs v-model="activePermissionTab" type="border-card">
                <el-tab-pane 
                  v-for="group in permissionGroups" 
                  :key="group.code"
                  :label="group.name" 
                  :name="group.code"
                >
                  <div class="permission-group">
                    <div class="group-header">
                      <el-checkbox
                        :model-value="isGroupAllSelected(group.code)"
                        :indeterminate="isGroupIndeterminate(group.code)"
                        @change="handleGroupSelectAll(group.code, $event)"
                      >
                        全选
                      </el-checkbox>
                    </div>
                    
                    <div class="permission-items">
                      <el-checkbox-group v-model="selectedPermissions">
                        <div 
                          v-for="permission in group.permissions" 
                          :key="permission.code"
                          class="permission-item"
                        >
                          <el-checkbox :label="permission.code">
                            <div class="permission-info">
                              <div class="permission-name">{{ permission.name }}</div>
                              <div class="permission-description">{{ permission.description }}</div>
                            </div>
                          </el-checkbox>
                        </div>
                      </el-checkbox-group>
                    </div>
                  </div>
                </el-tab-pane>
              </el-tabs>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 创建角色对话框 -->
    <el-dialog
      v-model="showCreateRoleDialog"
      title="创建角色"
      width="500px"
      :close-on-click-modal="false"
    >
      <el-form ref="createRoleFormRef" :model="createRoleForm" label-width="100px">
        <el-form-item label="角色名称" prop="name" required>
          <el-input v-model="createRoleForm.name" placeholder="请输入角色名称" />
        </el-form-item>
        
        <el-form-item label="角色编码" prop="code" required>
          <el-input v-model="createRoleForm.code" placeholder="请输入角色编码" />
        </el-form-item>
        
        <el-form-item label="角色类型" prop="type">
          <el-select v-model="createRoleForm.type" style="width: 100%">
            <el-option label="系统角色" :value="1" />
            <el-option label="业务角色" :value="2" />
            <el-option label="自定义角色" :value="3" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="角色描述" prop="description">
          <el-input 
            v-model="createRoleForm.description" 
            type="textarea" 
            :rows="3"
            placeholder="请输入角色描述"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showCreateRoleDialog = false">取消</el-button>
          <el-button 
            type="primary" 
            :loading="createRoleLoading"
            @click="handleCreateRole"
          >
            创建
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 权限矩阵对话框 -->
    <el-dialog
      v-model="showPermissionMatrixDialog"
      title="权限矩阵"
      width="80%"
      :close-on-click-modal="false"
    >
      <div class="permission-matrix">
        <el-table :data="permissionMatrixData" border>
          <el-table-column prop="roleName" label="角色" width="150" fixed="left" />
          <el-table-column 
            v-for="permission in matrixPermissions" 
            :key="permission.code"
            :label="permission.name" 
            width="120"
            align="center"
          >
            <template #default="{ row }">
              <el-checkbox
                :model-value="hasPermission(row.roleId, permission.code)"
                @change="togglePermission(row.roleId, permission.code, $event)"
              />
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { 
  Plus, 
  Grid, 
  Search, 
  MoreFilled, 
  Check 
} from '@element-plus/icons-vue';
import { useCool } from '/@/cool';

defineOptions({
  name: "advanced-permission",
});

const { service } = useCool();

// 响应式数据
const showCreateRoleDialog = ref(false);
const showPermissionMatrixDialog = ref(false);
const createRoleLoading = ref(false);
const roleSearchKeyword = ref('');
const activePermissionTab = ref('');
const selectedRole = ref(null);
const selectedPermissions = ref([]);

// 数据
const roles = ref([]);
const permissionGroups = ref([]);
const overviewData = reactive({
  totalRoles: 0,
  totalPermissions: 0,
  activeUsers: 0,
  permissionGroups: 0
});

const permissionMatrixData = ref([]);
const matrixPermissions = ref([]);

// 创建角色表单
const createRoleForm = reactive({
  name: '',
  code: '',
  type: 2,
  description: ''
});

// 计算属性
const filteredRoles = computed(() => {
  if (!roleSearchKeyword.value) return roles.value;
  return roles.value.filter(role => 
    role.name.includes(roleSearchKeyword.value) ||
    role.description.includes(roleSearchKeyword.value)
  );
});

// 方法
const getRoleType = (type: number) => {
  const types = ['', 'danger', 'warning', 'success'];
  return types[type] || '';
};

const getRoleTypeText = (type: number) => {
  const texts = ['', '系统角色', '业务角色', '自定义角色'];
  return texts[type] || '未知';
};

const selectRole = async (role: any) => {
  selectedRole.value = role;
  
  // 加载角色权限
  try {
    const permissions = await service.organization.permission.getRolePermissions({
      roleId: role.id
    });
    selectedPermissions.value = permissions.map((p: any) => p.code);
    
    // 设置默认选中的权限组
    if (permissionGroups.value.length > 0) {
      activePermissionTab.value = permissionGroups.value[0].code;
    }
  } catch (error) {
    ElMessage.error('加载角色权限失败');
  }
};

const isGroupAllSelected = (groupCode: string) => {
  const group = permissionGroups.value.find(g => g.code === groupCode);
  if (!group) return false;
  
  return group.permissions.every((p: any) => selectedPermissions.value.includes(p.code));
};

const isGroupIndeterminate = (groupCode: string) => {
  const group = permissionGroups.value.find(g => g.code === groupCode);
  if (!group) return false;
  
  const selectedCount = group.permissions.filter((p: any) => 
    selectedPermissions.value.includes(p.code)
  ).length;
  
  return selectedCount > 0 && selectedCount < group.permissions.length;
};

const handleGroupSelectAll = (groupCode: string, checked: boolean) => {
  const group = permissionGroups.value.find(g => g.code === groupCode);
  if (!group) return;
  
  if (checked) {
    // 添加该组所有权限
    group.permissions.forEach((p: any) => {
      if (!selectedPermissions.value.includes(p.code)) {
        selectedPermissions.value.push(p.code);
      }
    });
  } else {
    // 移除该组所有权限
    group.permissions.forEach((p: any) => {
      const index = selectedPermissions.value.indexOf(p.code);
      if (index > -1) {
        selectedPermissions.value.splice(index, 1);
      }
    });
  }
};

const handleRoleAction = async (command: string) => {
  const [action, roleId] = command.split('-');
  const role = roles.value.find(r => r.id === parseInt(roleId));
  
  if (action === 'edit') {
    // 编辑角色
  } else if (action === 'copy') {
    // 复制角色
  } else if (action === 'delete') {
    try {
      await ElMessageBox.confirm(
        `确定要删除角色 "${role.name}" 吗？`,
        '确认删除',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      );
      
      await service.organization.permission.deleteRole({ id: roleId });
      ElMessage.success('删除成功');
      loadRoles();
    } catch (error) {
      if (error !== 'cancel') {
        ElMessage.error('删除失败');
      }
    }
  }
};

const handleCreateRole = async () => {
  createRoleLoading.value = true;
  try {
    await service.organization.permission.createRole(createRoleForm);
    ElMessage.success('角色创建成功');
    showCreateRoleDialog.value = false;
    
    // 重置表单
    Object.assign(createRoleForm, {
      name: '',
      code: '',
      type: 2,
      description: ''
    });
    
    loadRoles();
  } catch (error) {
    ElMessage.error('角色创建失败');
  } finally {
    createRoleLoading.value = false;
  }
};

const saveRolePermissions = async () => {
  if (!selectedRole.value) return;
  
  try {
    await service.organization.permission.updateRolePermissions({
      roleId: selectedRole.value.id,
      permissions: selectedPermissions.value
    });
    
    ElMessage.success('权限保存成功');
  } catch (error) {
    ElMessage.error('权限保存失败');
  }
};

const hasPermission = (roleId: number, permissionCode: string) => {
  const roleData = permissionMatrixData.value.find(r => r.roleId === roleId);
  return roleData?.permissions?.includes(permissionCode) || false;
};

const togglePermission = async (roleId: number, permissionCode: string, checked: boolean) => {
  try {
    await service.organization.permission.toggleRolePermission({
      roleId,
      permissionCode,
      enabled: checked
    });
    
    // 更新本地数据
    const roleData = permissionMatrixData.value.find(r => r.roleId === roleId);
    if (roleData) {
      if (checked) {
        if (!roleData.permissions.includes(permissionCode)) {
          roleData.permissions.push(permissionCode);
        }
      } else {
        const index = roleData.permissions.indexOf(permissionCode);
        if (index > -1) {
          roleData.permissions.splice(index, 1);
        }
      }
    }
  } catch (error) {
    ElMessage.error('权限更新失败');
  }
};

const loadOverviewData = async () => {
  try {
    if (service.organization?.permission?.overview) {
      const data = await service.organization.permission.overview();
      Object.assign(overviewData, data);
    } else {
      console.warn('权限概览服务不可用，使用模拟数据');
      Object.assign(overviewData, {
        totalRoles: 8,
        totalPermissions: 45,
        activeUsers: 12,
        permissionGroups: 6
      });
    }
  } catch (error) {
    console.warn('加载概览数据失败:', error);
    Object.assign(overviewData, {
      totalRoles: 0,
      totalPermissions: 0,
      activeUsers: 0,
      permissionGroups: 0
    });
  }
};

const loadRoles = async () => {
  try {
    const data = await service.organization.permission.roles();
    roles.value = data;
  } catch (error) {
    ElMessage.error('加载角色列表失败');
  }
};

const loadPermissionGroups = async () => {
  try {
    const data = await service.organization.permission.groups();
    permissionGroups.value = data;
    
    if (data.length > 0) {
      activePermissionTab.value = data[0].code;
    }
  } catch (error) {
    ElMessage.error('加载权限组失败');
  }
};

const loadPermissionMatrix = async () => {
  try {
    const data = await service.organization.permission.matrix();
    permissionMatrixData.value = data.roles;
    matrixPermissions.value = data.permissions;
  } catch (error) {
    ElMessage.error('加载权限矩阵失败');
  }
};

onMounted(() => {
  loadOverviewData();
  loadRoles();
  loadPermissionGroups();
  loadPermissionMatrix();
});
</script>

<style lang="scss" scoped>
.advanced-permission {
  padding: 20px;
  
  .permission-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    
    .header-left {
      h2 {
        margin: 0 0 4px 0;
        color: var(--el-text-color-primary);
      }
      
      p {
        margin: 0;
        color: var(--el-text-color-regular);
        font-size: 14px;
      }
    }
    
    .header-right {
      display: flex;
      gap: 12px;
    }
  }
  
  .permission-overview {
    margin-bottom: 24px;
    
    .overview-card {
      text-align: center;
    }
  }
  
  .permission-content {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    
    .role-list {
      max-height: 600px;
      overflow-y: auto;
      
      .role-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 16px;
        border: 1px solid var(--el-border-color-light);
        border-radius: 8px;
        margin-bottom: 8px;
        cursor: pointer;
        transition: all 0.2s;
        
        &:hover {
          border-color: var(--el-color-primary-light-7);
          background: var(--el-fill-color-lighter);
        }
        
        &.active {
          border-color: var(--el-color-primary);
          background: var(--el-color-primary-light-9);
        }
        
        .role-info {
          flex: 1;
          
          .role-name {
            font-weight: 500;
            color: var(--el-text-color-primary);
            margin-bottom: 4px;
          }
          
          .role-description {
            font-size: 13px;
            color: var(--el-text-color-regular);
            margin-bottom: 8px;
          }
          
          .role-meta {
            display: flex;
            align-items: center;
            gap: 8px;
            
            .user-count {
              font-size: 12px;
              color: var(--el-text-color-secondary);
            }
          }
        }
        
        .role-actions {
          .role-menu {
            cursor: pointer;
            color: var(--el-text-color-secondary);
            
            &:hover {
              color: var(--el-text-color-primary);
            }
          }
        }
      }
    }
    
    .no-selection {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 400px;
    }
    
    .permission-config {
      .role-info-header {
        margin-bottom: 16px;
        padding-bottom: 16px;
        border-bottom: 1px solid var(--el-border-color-light);
        
        h4 {
          margin: 0 0 4px 0;
          color: var(--el-text-color-primary);
        }
        
        p {
          margin: 0;
          color: var(--el-text-color-regular);
          font-size: 14px;
        }
      }
      
      .permission-group {
        .group-header {
          margin-bottom: 16px;
          padding-bottom: 8px;
          border-bottom: 1px solid var(--el-border-color-lighter);
        }
        
        .permission-items {
          .permission-item {
            margin-bottom: 12px;
            
            .permission-info {
              margin-left: 8px;
              
              .permission-name {
                font-weight: 500;
                color: var(--el-text-color-primary);
              }
              
              .permission-description {
                font-size: 12px;
                color: var(--el-text-color-secondary);
                margin-top: 2px;
              }
            }
          }
        }
      }
    }
  }
  
  .permission-matrix {
    .el-table {
      .el-checkbox {
        display: flex;
        justify-content: center;
      }
    }
  }
}

// 移动端适配
@media (max-width: 768px) {
  .advanced-permission {
    padding: 16px;
    
    .permission-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 16px;
      
      .header-right {
        width: 100%;
        justify-content: space-between;
      }
    }
    
    .permission-content {
      .el-col {
        margin-bottom: 20px;
      }
    }
  }
}
</style>
