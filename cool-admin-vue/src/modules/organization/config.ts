import { ModuleConfig } from '/@/cool';

const config: ModuleConfig = {
  order: 1,
  label: '双维度组织管理',
  description: '支持部门维度和项目维度的双重组织架构管理系统',
  enable: true,

  // 注册全局组件
  components: [
    () => import('./components/organization-mode-switcher.vue'),
    () => import('./components/organization-transition.vue'),
    () => import('./components/organization-history.vue'),
    () => import('./components/error-boundary.vue')
  ],

  // 注册指令 - 暂时注释掉，避免加载问题
  // directives: [
  //   {
  //     name: 'dual-permission',
  //     directive: () => import('./directives/dual-permission')
  //   }
  // ],

  // 注册组合式函数 - ModuleConfig不支持此属性，注释掉
  // composables: [
  //   () => import('./composables/useLoadingState'),
  //   () => import('./composables/useResponsive'),
  //   () => import('./utils/performance')
  // ],

  // 项目维度视图路由
  views: [
    {
      path: '/project/dashboard',
      meta: {
        label: '项目工作台',
        keepAlive: true
      },
      component: () => import('./views/project/dashboard.vue')
    },
    {
      path: '/project/list',
      meta: {
        label: '项目列表',
        keepAlive: true
      },
      component: () => import('./views/project/list.vue')
    },
    {
      path: '/project/info',
      meta: {
        label: '项目管理',
        keepAlive: true
      },
      component: () => import('./views/project/info.vue')
    },
    {
      path: '/project/member/:id?',
      meta: {
        label: '项目成员',
        keepAlive: true
      },
      component: () => import('./views/project/member.vue')
    },
    {
      path: '/project/task',
      meta: {
        label: '任务管理',
        keepAlive: true
      },
      component: () => import('./views/project/task.vue')
    },
    {
      path: '/project/report',
      meta: {
        label: '项目报表',
        keepAlive: true
      },
      component: () => import('./views/project/report.vue')
    },
    {
      path: '/project/template',
      meta: {
        label: '项目模板',
        keepAlive: true
      },
      component: () => import('./views/project/template.vue')
    },
    {
      path: '/permission/advanced',
      meta: {
        label: '高级权限管理',
        keepAlive: true
      },
      component: () => import('./views/permission/advanced.vue')
    }
  ],

  // 服务配置 - ModuleConfig不支持此属性，注释掉
  // services: [
  //   {
  //     path: '/admin/organization/project/info',
  //     name: 'info'
  //   },
  //   {
  //     path: '/admin/organization/project/member',
  //     name: 'member'
  //   },
  //   {
  //     path: '/admin/organization/project/dashboard',
  //     name: 'dashboard'
  //   },
  //   {
  //     path: '/admin/organization/mode',
  //     name: 'mode'
  //   },
  //   {
  //     path: '/admin/organization/project/task',
  //     name: 'task'
  //   },
  //   {
  //     path: '/admin/organization/project/report',
  //     name: 'report'
  //   },
  //   {
  //     path: '/admin/organization/project/template',
  //     name: 'template'
  //   },
  //   {
  //     path: '/admin/organization/permission',
  //     name: 'permission'
  //   }
  // ],

  // 模块安装钩子
  install(app) {
    console.log('组织模块已加载');
    // 延迟初始化，确保Pinia已经安装
    setTimeout(() => {
      try {
        import('./utils/init').then(({ initOrganizationModule }) => {
          initOrganizationModule();
        }).catch(error => {
          console.warn('组织模块初始化失败:', error);
        });
      } catch (error) {
        console.warn('组织模块初始化失败:', error);
      }
    }, 1000); // 增加延迟时间
  }
};

export default (): ModuleConfig => config;