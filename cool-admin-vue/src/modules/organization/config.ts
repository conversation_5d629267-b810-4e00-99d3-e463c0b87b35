import { ModuleConfig } from '/@/cool';

const config: ModuleConfig = {
  order: 1,
  label: '双维度组织管理',
  description: '支持部门维度和项目维度的双重组织架构管理系统',
  enable: true,

  // 注册全局组件
  components: [
    () => import('./components/organization-mode-switcher.vue')
  ],

  // 注册指令
  directives: [
    {
      name: 'dual-permission',
      directive: () => import('./directives/dual-permission')
    }
  ],

  // 项目维度视图路由
  views: [
    {
      path: '/project/dashboard',
      name: 'project-dashboard',
      meta: {
        label: '项目工作台',
        icon: 'el-icon-data-board',
        keepAlive: true,
        organizationType: 'PROJECT'
      },
      component: () => import('./views/project/dashboard.vue')
    },
    {
      path: '/project/list',
      name: 'project-list',
      meta: {
        label: '项目列表',
        icon: 'el-icon-folder-opened',
        keepAlive: true,
        organizationType: 'PROJECT'
      },
      component: () => import('./views/project/list.vue')
    },
    {
      path: '/project/member/:id',
      name: 'project-member',
      meta: {
        label: '项目成员',
        icon: 'el-icon-user',
        keepAlive: true,
        organizationType: 'PROJECT'
      },
      component: () => import('./views/project/member.vue')
    },
    {
      path: '/project/info',
      name: 'project-info',
      meta: {
        label: '项目管理',
        icon: 'el-icon-setting',
        keepAlive: true,
        organizationType: 'PROJECT'
      },
      component: () => import('./views/project/info.vue')
    }
  ],

  // 服务配置
  services: [
    {
      path: '/admin/organization/project/info',
      name: 'info'
    },
    {
      path: '/admin/organization/project/member',
      name: 'member'
    },
    {
      path: '/admin/organization/project/dashboard',
      name: 'dashboard'
    },
    {
      path: '/admin/organization/mode',
      name: 'mode'
    }
  ]
};

export default (): ModuleConfig => config;