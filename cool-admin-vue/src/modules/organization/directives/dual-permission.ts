import { checkOrganizationPerm } from '/$/base/utils/permission';

interface DualPermissionValue {
  permission: string | { or?: string[]; and?: string[] };
  mode?: string;
  projectId?: number;
  fallback?: boolean;
}

function checkDualPermission(value: string | DualPermissionValue): boolean {
  if (typeof value === 'string') {
    return checkOrganizationPerm(value);
  }

  return checkOrganizationPerm(value.permission, {
    mode: value.mode,
    projectId: value.projectId,
    fallback: value.fallback
  });
}

function change(el: HTMLElement, binding: { value: any }) {
  el.style.display = checkDualPermission(binding.value) ? el.getAttribute('_display') || '' : 'none';
}

export default {
  created(el: HTMLElement, binding: { value: any }) {
    el.setAttribute('_display', el.style.display || '');
    change(el, binding);
  },
  updated: change
};

/**
 * 双维度权限检查Hook
 */
export function useDualPermission() {
  return {
    hasPermission: checkDualPermission
  };
}
