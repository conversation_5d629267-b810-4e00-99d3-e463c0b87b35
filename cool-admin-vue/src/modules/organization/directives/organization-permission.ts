import type { App, DirectiveBinding } from 'vue';
import { useOrganizationStore, OrganizationMode } from '../store/organization';

/**
 * 组织权限指令参数
 */
interface OrganizationPermissionValue {
  mode?: OrganizationMode | OrganizationMode[];
  permission?: string | string[];
  role?: string | string[];
  fallback?: 'hide' | 'disable';
}

/**
 * 检查组织模式权限
 */
function checkOrganizationPermission(
  value: OrganizationPermissionValue | string,
  organizationStore: any
): boolean {
  // 如果传入的是字符串，则作为权限检查
  if (typeof value === 'string') {
    return checkPermission(value);
  }

  const { mode, permission, role } = value;

  // 检查组织模式
  if (mode) {
    const modes = Array.isArray(mode) ? mode : [mode];
    if (!modes.includes(organizationStore.currentMode)) {
      return false;
    }
  }

  // 检查权限
  if (permission) {
    const permissions = Array.isArray(permission) ? permission : [permission];
    if (!permissions.some(p => checkPermission(p))) {
      return false;
    }
  }

  // 检查角色
  if (role) {
    const roles = Array.isArray(role) ? role : [role];
    if (!roles.some(r => checkRole(r, organizationStore))) {
      return false;
    }
  }

  return true;
}

/**
 * 检查权限
 */
function checkPermission(permission: string): boolean {
  // 这里需要集成现有的权限检查逻辑
  // 可以从 useBase() 中获取权限信息
  try {
    const { user } = useBase();
    const perms = user.perms || [];
    return perms.includes(permission);
  } catch {
    return false;
  }
}

/**
 * 检查角色
 */
function checkRole(role: string, organizationStore: any): boolean {
  // 检查用户在当前组织形态下是否有指定角色
  const permissionScope = organizationStore.permissionScope;
  if (!permissionScope) {
    return false;
  }

  // 这里需要根据具体的权限范围数据结构来实现
  // 暂时返回 true，实际使用时需要完善
  return true;
}

/**
 * 应用权限结果到元素
 */
function applyPermissionResult(
  el: HTMLElement,
  hasPermission: boolean,
  fallback: 'hide' | 'disable' = 'hide'
) {
  if (hasPermission) {
    // 有权限，恢复元素状态
    el.style.display = '';
    el.removeAttribute('disabled');
    el.classList.remove('is-disabled');
  } else {
    // 无权限，根据 fallback 处理
    if (fallback === 'hide') {
      el.style.display = 'none';
    } else if (fallback === 'disable') {
      el.setAttribute('disabled', 'true');
      el.classList.add('is-disabled');
    }
  }
}

/**
 * 组织权限指令
 */
const organizationPermissionDirective = {
  mounted(el: HTMLElement, binding: DirectiveBinding<OrganizationPermissionValue | string>) {
    const organizationStore = useOrganizationStore();
    const value = binding.value;
    const fallback = typeof value === 'object' ? value.fallback : 'hide';

    const hasPermission = checkOrganizationPermission(value, organizationStore);
    applyPermissionResult(el, hasPermission, fallback);
  },

  updated(el: HTMLElement, binding: DirectiveBinding<OrganizationPermissionValue | string>) {
    const organizationStore = useOrganizationStore();
    const value = binding.value;
    const fallback = typeof value === 'object' ? value.fallback : 'hide';

    const hasPermission = checkOrganizationPermission(value, organizationStore);
    applyPermissionResult(el, hasPermission, fallback);
  }
};

/**
 * 组织模式指令 - 只在指定模式下显示
 */
const organizationModeDirective = {
  mounted(el: HTMLElement, binding: DirectiveBinding<OrganizationMode | OrganizationMode[]>) {
    const organizationStore = useOrganizationStore();
    const modes = Array.isArray(binding.value) ? binding.value : [binding.value];
    
    const isVisible = modes.includes(organizationStore.currentMode);
    el.style.display = isVisible ? '' : 'none';
  },

  updated(el: HTMLElement, binding: DirectiveBinding<OrganizationMode | OrganizationMode[]>) {
    const organizationStore = useOrganizationStore();
    const modes = Array.isArray(binding.value) ? binding.value : [binding.value];
    
    const isVisible = modes.includes(organizationStore.currentMode);
    el.style.display = isVisible ? '' : 'none';
  }
};

/**
 * 安装指令
 */
export function setupOrganizationDirectives(app: App) {
  // 组织权限指令
  app.directive('org-permission', organizationPermissionDirective);
  
  // 组织模式指令
  app.directive('org-mode', organizationModeDirective);
}

/**
 * 组合式API - 权限检查
 */
export function useOrganizationPermission() {
  const organizationStore = useOrganizationStore();

  /**
   * 检查是否有权限
   */
  const hasPermission = (value: OrganizationPermissionValue | string): boolean => {
    return checkOrganizationPermission(value, organizationStore);
  };

  /**
   * 检查是否为指定模式
   */
  const isMode = (mode: OrganizationMode | OrganizationMode[]): boolean => {
    const modes = Array.isArray(mode) ? mode : [mode];
    return modes.includes(organizationStore.currentMode);
  };

  /**
   * 检查是否为部门模式
   */
  const isDepartmentMode = (): boolean => {
    return organizationStore.isDepartmentMode;
  };

  /**
   * 检查是否为项目模式
   */
  const isProjectMode = (): boolean => {
    return organizationStore.isProjectMode;
  };

  return {
    hasPermission,
    isMode,
    isDepartmentMode,
    isProjectMode,
    organizationStore
  };
}

// 导出类型
export type { OrganizationPermissionValue };

export default organizationPermissionDirective;
