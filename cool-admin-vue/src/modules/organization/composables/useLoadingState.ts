import { ref, computed, reactive } from 'vue';
import { ElLoading, ElMessage } from 'element-plus';

interface LoadingTask {
  id: string;
  message: string;
  progress?: number;
  startTime: number;
  timeout?: number;
}

interface LoadingOptions {
  message?: string;
  timeout?: number;
  showProgress?: boolean;
  target?: HTMLElement | string;
}

/**
 * 加载状态管理组合式函数
 */
export function useLoadingState() {
  // 当前加载任务
  const loadingTasks = reactive<Map<string, LoadingTask>>(new Map());
  
  // 全局加载状态
  const globalLoading = ref(false);
  
  // 当前加载实例
  let loadingInstance: any = null;

  // 计算属性
  const isLoading = computed(() => loadingTasks.size > 0 || globalLoading.value);
  
  const currentTask = computed(() => {
    const tasks = Array.from(loadingTasks.values());
    return tasks.length > 0 ? tasks[tasks.length - 1] : null;
  });

  const loadingProgress = computed(() => {
    if (!currentTask.value) return 0;
    return currentTask.value.progress || 0;
  });

  /**
   * 开始加载
   */
  const startLoading = (options: LoadingOptions = {}): string => {
    const taskId = `loading_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    const task: LoadingTask = {
      id: taskId,
      message: options.message || '加载中...',
      progress: options.showProgress ? 0 : undefined,
      startTime: Date.now(),
      timeout: options.timeout
    };

    loadingTasks.set(taskId, task);

    // 如果是第一个任务，显示全局加载
    if (loadingTasks.size === 1) {
      showGlobalLoading(task, options.target);
    }

    // 设置超时
    if (options.timeout) {
      setTimeout(() => {
        if (loadingTasks.has(taskId)) {
          stopLoading(taskId);
          ElMessage.warning('操作超时，请重试');
        }
      }, options.timeout);
    }

    return taskId;
  };

  /**
   * 停止加载
   */
  const stopLoading = (taskId: string) => {
    if (loadingTasks.has(taskId)) {
      loadingTasks.delete(taskId);
      
      // 如果没有其他任务，隐藏全局加载
      if (loadingTasks.size === 0) {
        hideGlobalLoading();
      }
    }
  };

  /**
   * 更新加载进度
   */
  const updateProgress = (taskId: string, progress: number, message?: string) => {
    const task = loadingTasks.get(taskId);
    if (task) {
      task.progress = Math.max(0, Math.min(100, progress));
      if (message) {
        task.message = message;
      }
      
      // 更新全局加载显示
      if (loadingInstance && currentTask.value?.id === taskId) {
        updateGlobalLoading(task);
      }
    }
  };

  /**
   * 显示全局加载
   */
  const showGlobalLoading = (task: LoadingTask, target?: HTMLElement | string) => {
    globalLoading.value = true;
    
    const loadingOptions: any = {
      text: task.message,
      background: 'rgba(0, 0, 0, 0.7)',
      customClass: 'organization-loading'
    };

    if (target) {
      loadingOptions.target = target;
    }

    loadingInstance = ElLoading.service(loadingOptions);
  };

  /**
   * 更新全局加载显示
   */
  const updateGlobalLoading = (task: LoadingTask) => {
    if (loadingInstance) {
      loadingInstance.setText(task.message);
    }
  };

  /**
   * 隐藏全局加载
   */
  const hideGlobalLoading = () => {
    globalLoading.value = false;
    
    if (loadingInstance) {
      loadingInstance.close();
      loadingInstance = null;
    }
  };

  /**
   * 清除所有加载任务
   */
  const clearAllLoading = () => {
    loadingTasks.clear();
    hideGlobalLoading();
  };

  /**
   * 获取任务信息
   */
  const getTaskInfo = (taskId: string) => {
    return loadingTasks.get(taskId);
  };

  /**
   * 获取所有任务
   */
  const getAllTasks = () => {
    return Array.from(loadingTasks.values());
  };

  /**
   * 异步操作包装器
   */
  const withLoading = async <T>(
    asyncFn: () => Promise<T>,
    options: LoadingOptions = {}
  ): Promise<T> => {
    const taskId = startLoading(options);
    
    try {
      const result = await asyncFn();
      return result;
    } catch (error) {
      throw error;
    } finally {
      stopLoading(taskId);
    }
  };

  /**
   * 带进度的异步操作包装器
   */
  const withProgressLoading = async <T>(
    asyncFn: (updateProgress: (progress: number, message?: string) => void) => Promise<T>,
    options: LoadingOptions = {}
  ): Promise<T> => {
    const taskId = startLoading({ ...options, showProgress: true });
    
    const progressUpdater = (progress: number, message?: string) => {
      updateProgress(taskId, progress, message);
    };
    
    try {
      const result = await asyncFn(progressUpdater);
      return result;
    } catch (error) {
      throw error;
    } finally {
      stopLoading(taskId);
    }
  };

  return {
    // 状态
    isLoading,
    currentTask,
    loadingProgress,
    globalLoading,
    
    // 方法
    startLoading,
    stopLoading,
    updateProgress,
    clearAllLoading,
    getTaskInfo,
    getAllTasks,
    withLoading,
    withProgressLoading
  };
}

// 全局加载状态管理器
const globalLoadingState = useLoadingState();

export { globalLoadingState };
