import { ref, computed, onMounted, onUnmounted } from 'vue';

export interface BreakpointConfig {
  xs: number;  // 超小屏幕
  sm: number;  // 小屏幕
  md: number;  // 中等屏幕
  lg: number;  // 大屏幕
  xl: number;  // 超大屏幕
  xxl: number; // 超超大屏幕
}

const defaultBreakpoints: BreakpointConfig = {
  xs: 480,
  sm: 576,
  md: 768,
  lg: 992,
  xl: 1200,
  xxl: 1600
};

/**
 * 响应式设计组合式函数
 */
export function useResponsive(breakpoints: BreakpointConfig = defaultBreakpoints) {
  // 当前屏幕宽度
  const screenWidth = ref(0);
  const screenHeight = ref(0);

  // 更新屏幕尺寸
  const updateScreenSize = () => {
    screenWidth.value = window.innerWidth;
    screenHeight.value = window.innerHeight;
  };

  // 计算当前断点
  const currentBreakpoint = computed(() => {
    const width = screenWidth.value;
    
    if (width < breakpoints.xs) return 'xs';
    if (width < breakpoints.sm) return 'sm';
    if (width < breakpoints.md) return 'md';
    if (width < breakpoints.lg) return 'lg';
    if (width < breakpoints.xl) return 'xl';
    return 'xxl';
  });

  // 各种屏幕尺寸判断
  const isXs = computed(() => screenWidth.value < breakpoints.xs);
  const isSm = computed(() => screenWidth.value >= breakpoints.xs && screenWidth.value < breakpoints.sm);
  const isMd = computed(() => screenWidth.value >= breakpoints.sm && screenWidth.value < breakpoints.md);
  const isLg = computed(() => screenWidth.value >= breakpoints.md && screenWidth.value < breakpoints.lg);
  const isXl = computed(() => screenWidth.value >= breakpoints.lg && screenWidth.value < breakpoints.xl);
  const isXxl = computed(() => screenWidth.value >= breakpoints.xl);

  // 移动端判断
  const isMobile = computed(() => screenWidth.value < breakpoints.md);
  const isTablet = computed(() => screenWidth.value >= breakpoints.md && screenWidth.value < breakpoints.lg);
  const isDesktop = computed(() => screenWidth.value >= breakpoints.lg);

  // 屏幕方向
  const isPortrait = computed(() => screenHeight.value > screenWidth.value);
  const isLandscape = computed(() => screenWidth.value > screenHeight.value);

  // 响应式列数计算
  const getResponsiveColumns = (config: {
    xs?: number;
    sm?: number;
    md?: number;
    lg?: number;
    xl?: number;
    xxl?: number;
  }) => {
    const bp = currentBreakpoint.value;
    return config[bp as keyof typeof config] || config.md || 1;
  };

  // 响应式间距计算
  const getResponsiveSpacing = (config: {
    xs?: number;
    sm?: number;
    md?: number;
    lg?: number;
    xl?: number;
    xxl?: number;
  }) => {
    const bp = currentBreakpoint.value;
    return config[bp as keyof typeof config] || config.md || 16;
  };

  // 响应式字体大小
  const getResponsiveFontSize = (baseSize: number) => {
    if (isXs.value) return baseSize * 0.8;
    if (isSm.value) return baseSize * 0.9;
    if (isMd.value) return baseSize;
    if (isLg.value) return baseSize * 1.1;
    if (isXl.value) return baseSize * 1.2;
    return baseSize * 1.3;
  };

  // 监听窗口大小变化
  let resizeObserver: ResizeObserver | null = null;

  onMounted(() => {
    updateScreenSize();
    
    // 使用 ResizeObserver 监听窗口变化
    if (window.ResizeObserver) {
      resizeObserver = new ResizeObserver(updateScreenSize);
      resizeObserver.observe(document.documentElement);
    } else {
      // 降级到 resize 事件
      window.addEventListener('resize', updateScreenSize);
    }
  });

  onUnmounted(() => {
    if (resizeObserver) {
      resizeObserver.disconnect();
    } else {
      window.removeEventListener('resize', updateScreenSize);
    }
  });

  return {
    // 屏幕尺寸
    screenWidth,
    screenHeight,
    
    // 断点判断
    currentBreakpoint,
    isXs,
    isSm,
    isMd,
    isLg,
    isXl,
    isXxl,
    
    // 设备类型
    isMobile,
    isTablet,
    isDesktop,
    
    // 屏幕方向
    isPortrait,
    isLandscape,
    
    // 工具函数
    getResponsiveColumns,
    getResponsiveSpacing,
    getResponsiveFontSize,
    updateScreenSize
  };
}

/**
 * 移动端优化组合式函数
 */
export function useMobileOptimization() {
  const { isMobile, isTablet } = useResponsive();

  // 触摸事件支持
  const isTouchDevice = computed(() => {
    return 'ontouchstart' in window || navigator.maxTouchPoints > 0;
  });

  // 移动端菜单配置
  const getMobileMenuConfig = () => {
    if (isMobile.value) {
      return {
        collapsed: true,
        mode: 'drawer',
        width: '280px'
      };
    }
    
    if (isTablet.value) {
      return {
        collapsed: false,
        mode: 'sidebar',
        width: '200px'
      };
    }
    
    return {
      collapsed: false,
      mode: 'sidebar',
      width: '240px'
    };
  };

  // 移动端表格配置
  const getMobileTableConfig = () => {
    if (isMobile.value) {
      return {
        size: 'small',
        showHeader: false,
        cardMode: true,
        pagination: {
          pageSize: 10,
          layout: 'prev, pager, next'
        }
      };
    }
    
    return {
      size: 'default',
      showHeader: true,
      cardMode: false,
      pagination: {
        pageSize: 20,
        layout: 'total, sizes, prev, pager, next, jumper'
      }
    };
  };

  // 移动端表单配置
  const getMobileFormConfig = () => {
    if (isMobile.value) {
      return {
        labelPosition: 'top',
        size: 'default',
        span: 24
      };
    }
    
    if (isTablet.value) {
      return {
        labelPosition: 'left',
        size: 'default',
        span: 12
      };
    }
    
    return {
      labelPosition: 'left',
      size: 'default',
      span: 8
    };
  };

  return {
    isTouchDevice,
    getMobileMenuConfig,
    getMobileTableConfig,
    getMobileFormConfig
  };
}

// 全局响应式状态
const globalResponsive = useResponsive();

export { globalResponsive };
