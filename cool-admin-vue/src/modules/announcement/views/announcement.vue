<template>
	<cl-crud ref="Crud">
		<cl-row>
			<!-- 刷新按钮 -->
			<cl-refresh-btn />
			<!-- 新增按钮 -->
			<cl-add-btn />
			<!-- 删除按钮 -->
			<cl-multi-delete-btn />
			<cl-flex1 />
			<!-- 条件搜索 -->
			<cl-search ref="Search" />
		</cl-row>

		<cl-row>
			<!-- 数据表格 -->
			<cl-table ref="Table" />
		</cl-row>

		<cl-row>
			<cl-flex1 />
			<!-- 分页控件 -->
			<cl-pagination />
		</cl-row>

		<!-- 新增、编辑 -->
		<cl-upsert ref="Upsert" />
	</cl-crud>
</template>

<script lang="ts" setup>
defineOptions({
	name: "announcement",
});

import { useCrud, useTable, useUpsert, useSearch } from "@cool-vue/crud";
import { useCool } from "/@/cool";
import { useI18n } from "vue-i18n";
import { ref, onMounted } from "vue";

const { service } = useCool();
const { t } = useI18n();

// 状态枚举
const statusOptions = [
  { label: t("草稿"), value: 0 },
  { label: t("已发布"), value: 1 },
  { label: t("已撤销"), value: 2 }
];

// 月份选项（可根据实际需要动态生成）
const monthOptions = [
  { label: "1月", value: "01" },
  { label: "2月", value: "02" },
  { label: "3月", value: "03" },
  { label: "4月", value: "04" },
  { label: "5月", value: "05" },
  { label: "6月", value: "06" },
  { label: "7月", value: "07" },
  { label: "8月", value: "08" },
  { label: "9月", value: "09" },
  { label: "10月", value: "10" },
  { label: "11月", value: "11" },
  { label: "12月", value: "12" }
];

// 项目下拉选项（动态获取）
const projectOptions = ref<any[]>([]);

// 获取项目列表（管理端接口）
async function loadProjects() {
  try {
    console.log('调用 service.project.list');
    const res = await service.project.list({ page: 1, size: 1000 });
    console.log('项目接口返回:', res);
    if (Array.isArray(res)) {
      projectOptions.value = res.map((item: any) => ({
        label: item.name,
        value: item.id
      }));
    } else if (res?.list) {
      // EPS 可能返回 { list: [], pagination: {} }
      projectOptions.value = res.list.map((item: any) => ({
        label: item.name,
        value: item.id
      }));
    }
  } catch (e) {
    console.error('项目接口异常', e);
    projectOptions.value = [];
  }
}

onMounted(() => {
  console.log('onMounted 调用 loadProjects');
  loadProjects();
});

// cl-upsert
const Upsert = useUpsert({
  items: [
    {
      label: t("选择项目"),
      prop: "projectId",
      component: {
        name: "cl-select",
        props: {
          options: projectOptions, // 直接绑定ref
          clearable: true
        }
      },
      span: 12,
    },
    {
      label: t("公示名称"),
      prop: "title",
      component: { name: "el-input", props: { clearable: true } },
      span: 12,
    },
    {
      label: t("月份"),
      prop: "month",
      component: {
        name: "cl-select",
        props: {
          options: monthOptions,
          clearable: true
        }
      },
      span: 12,
    },
    {
      label: t("公示内容HTML"),
      prop: "html",
      component: { name: "el-input", props: { clearable: true } },
      span: 12,
    },
    {
      label: t("状态"),
      prop: "status",
      component: {
        name: "cl-select",
        props: {
          options: statusOptions,
          clearable: true
        }
      },
      span: 12,
    },
  ],
  onOpen() {
    console.log('Upsert onOpen 调用 loadProjects');
    loadProjects(); // 弹窗每次打开都刷新项目列表
  }
});

// cl-table
const Table = useTable({
  columns: [
    { type: "selection" },
    {
      label: t("项目"),
      prop: "projectId",
      minWidth: 120,
      component: {
        name: "cl-select",
        props: {
          options: projectOptions,
          disabled: true
        }
      }
    },
    { label: t("公示名称"), prop: "title", minWidth: 120 },
    { label: t("月份"), prop: "month", minWidth: 120, component: { name: "cl-select", props: { options: monthOptions, disabled: true } } },
    { label: t("公示内容HTML"), prop: "html", minWidth: 120, hidden: true },
    {
      label: t("状态"),
      prop: "status",
      minWidth: 120,
      component: {
        name: "cl-select",
        props: {
          options: statusOptions,
          disabled: true
        }
      }
    },
    {
      label: t("创建时间"),
      prop: "createTime",
      minWidth: 170,
      sortable: "desc",
      component: { name: "cl-date-text" },
    },
    {
      label: t("更新时间"),
      prop: "updateTime",
      minWidth: 170,
      sortable: "custom",
      component: { name: "cl-date-text" },
    },
    { type: "op", buttons: ["edit", "delete"] },
  ],
});

// cl-search 配置部分
const searchOptions = [
  {
    prop: "projectId",
    label: t("项目"),
    component: {
      name: "cl-select",
      props: {
        options: projectOptions,
        clearable: true
      }
    }
  },
  {
    prop: "status",
    label: t("状态"),
    component: {
      name: "cl-select",
      props: {
        options: statusOptions,
        clearable: true
      }
    }
  },
  {
    prop: "month",
    label: t("月份"),
    component: {
      name: "el-date-picker",
      props: {
        type: "month",
        valueFormat: "yyyy-MM",
        placeholder: t("请选择月份"),
        clearable: true
      }
    }
  },
  {
    prop: "title",
    label: t("公示名称"),
    component: { name: "el-input", props: { clearable: true } }
  }
];

// cl-search 组件使用时：
const Search = useSearch({
  items: searchOptions
});

// cl-crud
const Crud = useCrud(
	{
		service: service.announcement,
	},
	(app) => {
		app.refresh();
	},
);

// 刷新
function refresh(params?: any) {
	Crud.value?.refresh(params);
}
</script>
