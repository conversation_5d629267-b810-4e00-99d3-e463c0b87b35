<template>
  <el-scrollbar class="sop-import-scrollbar">
    <div class="sop-import-container">
      <el-card class="import-card">
        <template #header>
          <div class="card-header">
            <span class="title">SOP场景导入</span>
            <el-button 
              type="primary" 
              :icon="Download" 
              @click="downloadTemplate"
              :loading="downloading"
            >
              下载模板
            </el-button>
          </div>
        </template>

        <!-- 导入步骤指示器 -->
        <el-steps :active="currentStep" finish-status="success" class="import-steps">
          <el-step title="上传文件" description="选择Excel文件"></el-step>
          <el-step title="预览数据" description="检查导入数据"></el-step>
          <el-step title="确认导入" description="执行导入操作"></el-step>
          <el-step title="导入完成" description="查看导入结果"></el-step>
        </el-steps>

        <!-- 步骤1: 文件上传 -->
        <div v-show="currentStep === 0" class="step-content">
          <div class="upload-section">
            <el-upload
              ref="uploadRef"
              class="upload-dragger"
              drag
              :auto-upload="false"
              :limit="1"
              accept=".xlsx"
              :on-change="handleFileChange"
              :on-exceed="handleExceed"
            >
              <el-icon class="el-icon--upload"><upload-filled /></el-icon>
              <div class="el-upload__text">
                将Excel文件拖到此处，或<em>点击上传</em>
              </div>
              <template #tip>
                <div class="el-upload__tip">
                  只能上传.xlsx格式文件，且不超过10MB
                </div>
              </template>
            </el-upload>

            <div v-if="selectedFile" class="file-info">
              <el-alert
                :title="`已选择文件: ${selectedFile.name}`"
                type="info"
                show-icon
                :closable="false"
              />
            </div>

            <div class="step-actions">
              <el-button 
                type="primary" 
                @click="previewImport"
                :disabled="!selectedFile"
                :loading="previewing"
              >
                预览数据
              </el-button>
            </div>
          </div>
        </div>

        <!-- 步骤2: 数据预览 -->
        <div v-show="currentStep === 1" class="step-content">
          <div class="preview-section">
            <!-- 统计信息 -->
            <div v-if="previewResult" class="statistics">
              <el-row :gutter="20">
                <el-col :span="6">
                  <el-statistic title="场景数量" :value="previewResult.statistics?.totalScenarios || 0" />
                </el-col>
                <el-col :span="6">
                  <el-statistic title="步骤数量" :value="previewResult.statistics?.totalSteps || 0" />
                </el-col>
                <el-col :span="6">
                  <el-statistic title="校验错误" :value="previewResult.validationErrors?.length || 0" />
                </el-col>
                <el-col :span="6">
                  <el-statistic title="版本冲突" :value="previewResult.versionConflicts?.length || 0" />
                </el-col>
              </el-row>
            </div>

            <!-- 校验错误 -->
            <div v-if="previewResult?.validationErrors?.length" class="validation-errors">
              <el-alert
                title="发现数据校验错误"
                type="error"
                show-icon
                :closable="false"
              />
              <el-scrollbar height="200px">
                <el-table :data="previewResult.validationErrors" class="error-table">
                  <el-table-column prop="rowNumber" label="行号" width="80" />
                  <el-table-column prop="field" label="字段" width="120" />
                  <el-table-column prop="value" label="值" width="150" />
                  <el-table-column prop="message" label="错误信息" />
                </el-table>
              </el-scrollbar>
            </div>

            <!-- 版本冲突 -->
            <div v-if="previewResult?.versionConflicts?.length" class="version-conflicts">
              <el-alert
                title="发现版本冲突"
                type="warning"
                show-icon
                :closable="false"
              />
              <el-scrollbar height="200px">
                <el-table :data="previewResult.versionConflicts" class="conflict-table">
                  <el-table-column prop="scenarioCode" label="场景编码" width="100" />
                  <el-table-column prop="scenarioName" label="场景名称" width="200" />
                  <el-table-column prop="existingVersion" label="现有版本" width="100" />
                  <el-table-column prop="importVersion" label="导入版本" width="100" />
                  <el-table-column prop="conflictType" label="冲突类型" width="120" />
                  <el-table-column prop="recommendation" label="建议操作" width="100" />
                </el-table>
              </el-scrollbar>
            </div>

            <!-- 数据预览明细 -->
            <div v-if="previewResult?.data?.length" class="data-preview">
              <el-divider content-position="left">
                <span class="divider-text">数据预览明细</span>
              </el-divider>
              <div class="table-container">
                <el-table
                  :data="previewResult.data"
                  class="preview-table"
                  stripe
                  border
                  height="400"
                  style="width: 100%;"
                >
                  <!-- 固定列 -->
                              <el-table-column prop="rowNumber" label="行号" width="60" fixed="left" />
            <el-table-column prop="industryName" label="行业名称" width="100" fixed="left" />
            <el-table-column prop="projectStage" label="项目阶段" width="90" fixed="left" />
            <el-table-column prop="moduleCode" label="模块编号" width="90" fixed="left" />
            <el-table-column prop="moduleName" label="模块名称" width="120" fixed="left" />
            <el-table-column prop="scenarioCode" label="场景编号" width="90" fixed="left" />
            <el-table-column prop="scenarioName" label="场景名称" width="150" fixed="left" />
            <el-table-column prop="stepOrder" label="步骤编号" width="90" fixed="left" />
            <el-table-column prop="step" label="步骤" width="120" fixed="left" />
                  
                  <!-- 可滚动列 -->
                  <el-table-column prop="executionCycle" label="执行周期" width="100" />
                  <el-table-column prop="entityTouchpoint" label="实体触点" width="120" show-overflow-tooltip />
                  <el-table-column prop="userActivity" label="用户活动" width="150" show-overflow-tooltip />
                  <el-table-column prop="employeeBehavior" label="员工行为" width="150" show-overflow-tooltip />
                  <el-table-column prop="workHighlight" label="工作亮点" width="150" show-overflow-tooltip />
                  <el-table-column prop="employeeRole" label="员工角色" width="120" />
                  <el-table-column prop="frontBackend" label="前台/中后台" width="120" />
                  <el-table-column prop="supportSystem" label="支持系统" width="120" show-overflow-tooltip />
                  <el-table-column prop="relatedAttachments" label="相关附件" width="150" show-overflow-tooltip />
                </el-table>
              </div>
            </div>

            <!-- 导入提示 -->
            <div v-if="previewResult?.success" class="import-guide">
              <el-alert
                title="📋 数据预览完成，请配置导入参数后点击下方的【🚀 确认导入】按钮"
                type="success"
                :closable="false"
                show-icon
                style="margin-bottom: 20px;"
              />
            </div>

            <!-- 导入配置 -->
            <div class="import-config">
              <el-form :model="importConfig" label-width="120px">
                <el-form-item label="版本策略">
                  <el-radio-group v-model="importConfig.versionStrategy">
                    <el-radio value="MAJOR">主版本升级 (1.0 → 2.0)</el-radio>
                    <el-radio value="MINOR">次版本升级 (1.0 → 1.1)</el-radio>
                    <el-radio value="PATCH">补丁版本升级 (1.0.0 → 1.0.1)</el-radio>
                  </el-radio-group>
                </el-form-item>

                <el-form-item label="导入模式">
                  <el-radio-group v-model="importConfig.importMode">
                    <el-radio value="CREATE_NEW">创建新版本</el-radio>
                    <el-radio value="UPDATE_EXISTING">更新现有版本</el-radio>
                    <el-radio value="MERGE">合并模式</el-radio>
                  </el-radio-group>
                </el-form-item>

                <el-form-item label="其他选项">
                  <el-checkbox v-model="importConfig.forceOverride">强制覆盖现有版本</el-checkbox>
                  <el-checkbox v-model="importConfig.backupExisting">备份现有数据</el-checkbox>
                </el-form-item>

                <el-form-item label="导入说明">
                  <el-input
                    v-model="importConfig.importDescription"
                    type="textarea"
                    :rows="3"
                    placeholder="请输入导入说明（可选）"
                  />
                </el-form-item>
              </el-form>
            </div>

            <div class="step-actions">
              <el-button @click="goToPreviousStep">上一步</el-button>
              <el-button 
                type="primary" 
                @click="confirmImport"
                :disabled="hasValidationErrors"
                :loading="importing"
                size="large"
              >
                🚀 确认导入
              </el-button>
              
              <!-- 调试信息 -->
              <div style="margin-top: 10px; font-size: 12px; color: #666;">
                调试信息: 当前步骤={{ currentStep }}, 有错误={{ hasValidationErrors }}, 导入中={{ importing }}
              </div>
            </div>
          </div>
        </div>

        <!-- 步骤3: 导入确认 -->
        <div v-show="currentStep === 2" class="step-content">
          <div class="confirm-section">
            <el-result
              icon="warning"
              title="确认导入操作"
              sub-title="请仔细检查以下信息，确认无误后点击执行导入"
            >
              <template #extra>
                <div class="confirm-info">
                  <p><strong>文件名:</strong> {{ selectedFile?.name }}</p>
                  <p><strong>场景数量:</strong> {{ previewResult?.statistics?.totalScenarios }}</p>
                  <p><strong>步骤数量:</strong> {{ previewResult?.statistics?.totalSteps }}</p>
                  <p><strong>版本策略:</strong> {{ getVersionStrategyText(importConfig.versionStrategy) }}</p>
                  <p><strong>导入模式:</strong> {{ getImportModeText(importConfig.importMode) }}</p>
                  <p v-if="importConfig.forceOverride"><strong>强制覆盖:</strong> 是</p>
                  <p v-if="importConfig.backupExisting"><strong>数据备份:</strong> 是</p>
                </div>
                
                <div class="step-actions">
                  <el-button @click="goToPreviousStep">上一步</el-button>
                  <el-button 
                    type="primary" 
                    @click="executeImport"
                    :loading="importing"
                    size="large"
                  >
                    ⚡ 执行导入
                  </el-button>
                </div>
              </template>
            </el-result>
          </div>
        </div>

        <!-- 步骤4: 导入结果 -->
        <div v-show="currentStep === 3" class="step-content">
          <div class="result-section">
            <el-result
              :icon="importResult?.success ? 'success' : 'error'"
              :title="importResult?.success ? '导入成功' : '导入失败'"
              :sub-title="importResult?.message"
            >
              <template #extra>
                <div v-if="importResult?.success && importResult?.statistics" class="result-statistics">
                  <el-descriptions title="导入统计" :column="2" border>
                    <el-descriptions-item label="总场景数">
                      {{ importResult.statistics.totalScenarios }}
                    </el-descriptions-item>
                    <el-descriptions-item label="成功场景数">
                      {{ importResult.statistics.successScenarios }}
                    </el-descriptions-item>
                    <el-descriptions-item label="失败场景数">
                      {{ importResult.statistics.failedScenarios }}
                    </el-descriptions-item>
                    <el-descriptions-item label="总步骤数">
                      {{ importResult.statistics.totalSteps }}
                    </el-descriptions-item>
                  </el-descriptions>
                </div>

                <div v-if="importResult?.importedScenarios?.length" class="imported-scenarios">
                  <h4>导入的场景列表</h4>
                  <el-scrollbar height="300px">
                    <el-table :data="importResult.importedScenarios">
                      <el-table-column prop="scenarioCode" label="场景编码" width="120" />
                      <el-table-column prop="scenarioName" label="场景名称" />
                      <el-table-column prop="version" label="版本" width="100" />
                      <el-table-column prop="action" label="操作" width="100">
                        <template #default="{ row }">
                          <el-tag :type="getActionTagType(row.action)">
                            {{ getActionText(row.action) }}
                          </el-tag>
                        </template>
                      </el-table-column>
                      <el-table-column prop="stepCount" label="步骤数" width="80" />
                    </el-table>
                  </el-scrollbar>
                </div>

                <div class="step-actions">
                  <el-button @click="resetImport">重新导入</el-button>
                  <el-button type="primary" @click="goToScenarioList">查看场景列表</el-button>
                </div>
              </template>
            </el-result>
          </div>
        </div>
      </el-card>
    </div>
  </el-scrollbar>
</template>

<script lang="ts" setup>
import { ref, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Download, UploadFilled } from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'
import { useCool } from '/@/cool'

const { service } = useCool()
const router = useRouter()

// 响应式数据
const currentStep = ref(0)
const selectedFile = ref<File | null>(null)
const downloading = ref(false)
const previewing = ref(false)
const importing = ref(false)
const previewResult = ref<any>(null)
const importResult = ref<any>(null)
const uploadRef = ref<any>(null)

// 导入配置
const importConfig = ref({
  versionStrategy: 'MINOR',
  importMode: 'CREATE_NEW',
  forceOverride: false,
  backupExisting: true,
  importDescription: ''
})

// 计算属性
const hasValidationErrors = computed(() => {
  return previewResult.value?.validationErrors?.length > 0
})

// 方法
const downloadTemplate = async () => {
  try {
    downloading.value = true

    const response = await service.request({
      url: '/admin/sop/import/template',
      method: 'GET',
      responseType: 'blob'
    })
    
    // 创建下载链接
    const blob = new Blob([response], { 
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' 
    })
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = 'SOP导入模板.xlsx'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)
    
    ElMessage.success('模板下载成功')
  } catch (error) {
    console.error('下载模板失败:', error)
    ElMessage.error('下载模板失败')
  } finally {
    downloading.value = false
  }
}

const handleFileChange = (file: any) => {
  selectedFile.value = file.raw
}

const handleExceed = () => {
  ElMessage.warning('只能选择一个文件')
}

const previewImport = async () => {
  if (!selectedFile.value) {
    ElMessage.warning('请先选择文件')
    return
  }
  
  try {
    previewing.value = true

    const formData = new FormData()
    formData.append('file', selectedFile.value)

    const response = await service.request({
      url: '/admin/sop/import/preview',
      method: 'POST',
      data: formData,
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
    
    // 处理预览数据，添加行号和步骤编号
    if (response.data && Array.isArray(response.data)) {
      // 按场景分组，为每个场景内的步骤生成1.1, 1.2, 1.3...的编号
      const scenarioGroups = new Map<string, any[]>()
      
      // 先按场景分组
      response.data.forEach((row: any, index: number) => {
        row.rowNumber = index + 1 // 行号从1开始
        const scenarioCode = row.scenarioCode || 'unknown'
        if (!scenarioGroups.has(scenarioCode)) {
          scenarioGroups.set(scenarioCode, [])
        }
        scenarioGroups.get(scenarioCode)!.push(row)
      })
      
      // 为每个场景内的步骤生成编号
      scenarioGroups.forEach((steps, scenarioCode) => {
        steps.forEach((step, index) => {
          // 首先尝试从步骤文本中提取编号
          const extractedOrder = extractStepOrder(step.step, index + 1)
          if (extractedOrder) {
            step.stepOrder = extractedOrder
          } else {
            // 如果没有提取到编号，生成1.1, 1.2, 1.3...格式
            step.stepOrder = `1.${index + 1}`
          }
        })
      })
    }
    
    previewResult.value = response
    currentStep.value = 1
    
    if (response.success) {
      ElMessage.success('数据预览成功')
    } else {
      ElMessage.warning(response.message || '数据预览完成，请检查错误信息')
    }
  } catch (error) {
    console.error('预览失败:', error)
    ElMessage.error('预览失败')
  } finally {
    previewing.value = false
  }
}

const confirmImport = () => {
  if (hasValidationErrors.value) {
    ElMessage.error('请先修复数据校验错误')
    return
  }
  
  currentStep.value = 2
}

const executeImport = async () => {
  try {
    importing.value = true

    const formData = new FormData()
    formData.append('file', selectedFile.value!)
    formData.append('forceOverride', String(importConfig.value.forceOverride))
    formData.append('versionStrategy', importConfig.value.versionStrategy)
    formData.append('importMode', importConfig.value.importMode)
    formData.append('backupExisting', String(importConfig.value.backupExisting))
    if (importConfig.value.importDescription) {
      formData.append('importDescription', importConfig.value.importDescription)
    }

    const response = await service.request({
      url: '/admin/sop/import/import',
      method: 'POST',
      data: formData,
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
    
    importResult.value = response
    currentStep.value = 3
    
    if (response.success) {
      ElMessage.success('导入成功')
    } else {
      ElMessage.error(response.message || '导入失败')
    }
  } catch (error) {
    console.error('导入失败:', error)
    ElMessage.error('导入失败')
  } finally {
    importing.value = false
  }
}

const resetImport = () => {
  currentStep.value = 0
  selectedFile.value = null
  previewResult.value = null
  importResult.value = null
  importing.value = false
  previewing.value = false
  downloading.value = false
  
  // 重置导入配置
  importConfig.value = {
    versionStrategy: 'MINOR',
    importMode: 'CREATE_NEW',
    forceOverride: false,
    backupExisting: true,
    importDescription: ''
  }
  
  // 重置文件上传组件
  if (uploadRef.value) {
    uploadRef.value.clearFiles()
  }
  
  ElMessage.success('已重置，可以重新导入')
}

const goToScenarioList = () => {
  router.push('/sop/scenario')
}

const goToPreviousStep = () => {
  if (currentStep.value > 0) {
    currentStep.value--
    ElMessage.info('已返回上一步')
  }
}

// 辅助方法
const getVersionStrategyText = (strategy: string) => {
  const map: Record<string, string> = {
    'MAJOR': '主版本升级',
    'MINOR': '次版本升级',
    'PATCH': '补丁版本升级'
  }
  return map[strategy] || strategy
}

const getImportModeText = (mode: string) => {
  const map: Record<string, string> = {
    'CREATE_NEW': '创建新版本',
    'UPDATE_EXISTING': '更新现有版本',
    'MERGE': '合并模式'
  }
  return map[mode] || mode
}

const getActionTagType = (action: string): 'success' | 'warning' | 'info' | 'primary' | 'danger' => {
  const map: Record<string, 'success' | 'warning' | 'info' | 'primary' | 'danger'> = {
    'CREATED': 'success',
    'UPDATED': 'warning',
    'SKIPPED': 'info'
  }
  return map[action] || 'info'
}

const getActionText = (action: string) => {
  const map: Record<string, string> = {
    'CREATED': '新建',
    'UPDATED': '更新',
    'SKIPPED': '跳过'
  }
  return map[action] || action
}

// 从步骤文本中提取步骤编号
const extractStepOrder = (stepText: string, defaultOrder: number): string | null => {
  if (!stepText || typeof stepText !== 'string') {
    return null
  }
  
  stepText = stepText.trim()
  
  // 匹配模式：数字开头的编号，如 "1", "2", "1.1", "1.2", "1.1.1" 等
  const pattern = /^(\d+(?:\.\d+)*)/
  const match = stepText.match(pattern)
  
  if (match) {
    return match[1]
  }
  
  // 如果没有找到编号，返回null
  return null
}
</script>

<style lang="scss" scoped>
.sop-import-scrollbar {
  height: 100vh;
}

.sop-import-container {
  padding: 20px;

  .import-card {
    max-width: 1200px;
    margin: 0 auto;

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .title {
        font-size: 18px;
        font-weight: bold;
        color: var(--el-text-color-primary);
      }
    }
  }

  .import-steps {
    margin: 30px 0;
  }

  .step-content {
    min-height: 400px;
    padding: 20px 0;
  }

  .upload-section {
    .upload-dragger {
      width: 100%;

      :deep(.el-upload-dragger) {
        width: 100%;
        height: 200px;
        border: 2px dashed #d9d9d9;
        border-radius: 6px;
        cursor: pointer;
        position: relative;
        overflow: hidden;
        transition: border-color 0.2s;

        &:hover {
          border-color: #409eff;
        }
      }
    }

    .file-info {
      margin: 20px 0;
    }

    .step-actions {
      margin-top: 30px;
      text-align: center;
    }
  }

  .preview-section {
    .statistics {
      margin-bottom: 30px;
      padding: 20px;
      background: var(--el-fill-color-lighter);
      border-radius: 6px;
      border: 1px solid var(--el-border-color-light);
    }

    .validation-errors,
    .version-conflicts {
      margin-bottom: 30px;

      .error-table,
      .conflict-table {
        margin-top: 15px;
      }
    }

    .data-preview {
      margin: 30px 0;

      .divider-text {
        font-weight: 500;
        color: var(--el-text-color-primary);
      }

      .table-container {
        margin-top: 15px;
        border-radius: 6px;
        overflow: hidden;
        border: 1px solid var(--el-border-color-light);
      }

      .preview-table {
        :deep(.el-table__header) {
          background-color: var(--el-fill-color-lighter);
        }
        
        // 固定列样式
        :deep(.el-table__fixed-left) {
          box-shadow: 2px 0 6px rgba(0, 0, 0, 0.1);
        }
        
        // 固定列分隔线
        :deep(.el-table__fixed-left::after) {
          content: '';
          position: absolute;
          right: 0;
          top: 0;
          bottom: 0;
          width: 1px;
          background-color: var(--el-border-color);
        }
        
        // 表头固定列样式
        :deep(.el-table__fixed-header-wrapper) {
          background-color: var(--el-fill-color-lighter);
        }
      }
    }

    .import-config {
      margin: 30px 0;
      padding: 20px;
      background: var(--el-fill-color-lighter);
      border-radius: 6px;
      border: 1px solid var(--el-border-color);

      .divider-text {
        font-weight: 500;
        color: var(--el-text-color-primary);
      }
    }

    .step-actions {
      margin-top: 30px;
      text-align: center;
      padding: 20px;
      background: var(--el-fill-color-lighter);
      border-radius: 8px;
      border: 2px dashed var(--el-border-color);

      .el-button + .el-button {
        margin-left: 15px;
      }
      
      .el-button--primary {
        font-weight: bold;
        font-size: 16px;
        padding: 12px 30px;
        
        &:hover {
          transform: translateY(-1px);
        }
      }
    }
  }

  .confirm-section {
    .confirm-info {
      text-align: left;
      margin: 20px 0;
      padding: 20px;
      background: var(--el-fill-color-lighter);
      border-radius: 6px;
      border: 1px solid var(--el-border-color-light);

      p {
        margin: 8px 0;
        font-size: 14px;
        color: var(--el-text-color-regular);

        strong {
          color: var(--el-text-color-primary);
          margin-right: 8px;
        }
      }
    }

    .step-actions {
      margin-top: 30px;

      .el-button + .el-button {
        margin-left: 15px;
      }
    }
  }

  .result-section {
    .result-statistics {
      margin: 20px 0;
    }

    .imported-scenarios {
      margin: 30px 0;

      h4 {
        margin-bottom: 15px;
        color: var(--el-text-color-primary);
      }
    }

    .step-actions {
      margin-top: 30px;

      .el-button + .el-button {
        margin-left: 15px;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .sop-import-container {
    padding: 10px;

    .import-card {
      .card-header {
        flex-direction: column;
        gap: 15px;

        .title {
          font-size: 16px;
        }
      }
    }

    .step-content {
      min-height: 300px;
      padding: 15px 0;
    }

    .preview-section {
      .statistics {
        padding: 15px;

        :deep(.el-col) {
          margin-bottom: 15px;
        }
      }
    }
  }
}

// 深色主题适配
html.dark {
  .sop-import-container {
    .step-actions {
      background: var(--el-fill-color-dark);
      border-color: var(--el-border-color-darker);
    }
  }
}
</style>
