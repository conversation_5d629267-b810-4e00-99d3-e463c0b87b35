import { ref, onUnmounted, computed } from "vue";
import { ElMessage } from "element-plus";
import { config } from "/@/config";

// 根据状态和进度生成消息
function getStatusMessage(status: number, progress?: number): string {
	const progressPercent = progress || 0;
	
	switch (status) {
		case 0:
			return "生成预览任务已创建，等待处理...";
		case 1:
			if (progressPercent < 10) return "初始化中...";
			if (progressPercent < 20) return "接收需求中...";
			if (progressPercent < 40) return "AI识别中...";
			if (progressPercent < 60) return "识别完成，准备生成...";
			if (progressPercent < 80) return "任务生成中...";
			if (progressPercent < 100) return "即将完成...";
			return "处理中...";
		case 2:
			return "生成预览任务完成！";
		case 3:
			return "生成预览任务失败";
		default:
			return `状态更新 (${progressPercent}%)`;
	}
}

export function useSseTaskStatus() {
	const eventSource = ref<EventSource | null>(null);
	const taskRecord = ref<any>(null);
	const progressDetails = ref<any[]>([]);
	const taskErrorMsg = ref<string | null>(null);

	// 新增一个标志位，用于识别是否是后端主动、正常关闭
	let isClosedByServer = false;

	const connect = (taskId: number | string) => {
		if (eventSource.value) {
			close();
		}

		// 重置状态
		isClosedByServer = false;
		taskRecord.value = null;
		progressDetails.value = [];
		taskErrorMsg.value = null;

		let token = localStorage.getItem("token");

		// 移除token可能包含的双引号
		if (token) {
			token = token.replace(/"/g, '');
		}

		const url = `${config.baseUrl}/admin/sop/ai-task-generator/subscribe/${taskId}?Authorization=${token}`;
		console.log("[SSE] 准备连接:", url);

		eventSource.value = new EventSource(url);

		eventSource.value.onopen = () => {
			console.log("[SSE] 连接已建立。");
		};

		eventSource.value.onmessage = (event: MessageEvent) => {
			try {
				const data = JSON.parse(event.data);
				console.log("[SSE] 收到消息:", data);

				// 如果有error字段，直接友好提示
				if (data.error) {
					ElMessage.error(data.error);
					taskErrorMsg.value = data.error;
					close();
					return;
				}

				taskRecord.value = data;
				
				// 处理进度详情
				if (data.progressDetails) {
					try {
						const parsedDetails = JSON.parse(data.progressDetails);
						console.log("[SSE] 解析进度详情:", parsedDetails);
						if (Array.isArray(parsedDetails) && parsedDetails.length > 0) {
							// 改进的去重处理：基于消息内容和时间戳进行去重
							const newProgressDetails: any[] = [];
							const existingKeys = new Set<string>();
							
							// 为现有消息创建唯一键
							progressDetails.value.forEach(item => {
								const message = typeof item === 'string' ? item : (item.message || item.text || item.content || '');
								const timestamp = typeof item === 'object' && item.timestamp ? item.timestamp : '';
								const key = `${message}_${timestamp}`;
								existingKeys.add(key);
							});
							
							// 检查新消息是否已存在
							parsedDetails.forEach(item => {
								const message = typeof item === 'string' ? item : (item.message || item.text || item.content || '');
								const timestamp = typeof item === 'object' && item.timestamp ? item.timestamp : new Date().toLocaleTimeString();
								const key = `${message}_${timestamp}`;
								
								if (!existingKeys.has(key) && message.trim()) {
									newProgressDetails.push({
										...item,
										timestamp: timestamp,
										message: message
									});
									existingKeys.add(key);
								}
							});
							
							// 只有当有新消息时才更新
							if (newProgressDetails.length > 0) {
								progressDetails.value = [...progressDetails.value, ...newProgressDetails];
								console.log("[SSE] 添加新消息:", newProgressDetails.length, "条，总计:", progressDetails.value.length);
							} else {
								console.log("[SSE] 没有新消息，跳过更新");
							}
						}
					} catch (e) {
						console.error("解析progressDetails失败", e, "原始数据:", data.progressDetails);
						// 如果解析失败，尝试将原始字符串作为单个消息
						const errorMessage = {
							message: data.progressDetails,
							timestamp: new Date().toLocaleTimeString()
						};
						
						// 避免重复添加错误消息
						const lastMessage = progressDetails.value[progressDetails.value.length - 1];
						const lastMessageText = typeof lastMessage === 'string' ? lastMessage : (lastMessage?.message || '');
						if (!lastMessage || lastMessageText !== errorMessage.message) {
							progressDetails.value.push(errorMessage);
						}
					}
				} else {
					console.log("[SSE] 没有progressDetails字段");
				}
				
				// 如果没有progressDetails但有其他进度信息，创建进度消息
				if (!data.progressDetails && data.status !== undefined) {
					const progressMessage = {
						timestamp: new Date().toLocaleTimeString(),
						message: getStatusMessage(data.status, data.progress),
						status: data.status,
						progress: data.progress
					};
					
					// 避免重复添加相同的消息
					const lastMessage = progressDetails.value[progressDetails.value.length - 1];
					const lastMessageText = typeof lastMessage === 'string' ? lastMessage : (lastMessage?.message || '');
					if (!lastMessage || lastMessageText !== progressMessage.message) {
						progressDetails.value.push(progressMessage);
					}
				}
			} catch (e) {
				console.error("SSE消息解析失败", e);
			}
		};

		// 【重要】监听后端发送的 "close" 事件
		eventSource.value.addEventListener('close', (event) => {
			console.log('[SSE] 收到服务端关闭信号:', event.data);
			isClosedByServer = true; // 标记为正常关闭
			close(); // 主动关闭连接
		});

		eventSource.value.onerror = (err) => {
			// 【重要】如果是后端正常关闭，则不报错
			if (isClosedByServer) {
				console.log("[SSE] 连接已由服务端正常关闭。");
				return; // 直接返回，不执行下面的错误处理
			}

			console.error("[SSE] 连接发生错误:", err);
			// 只有任务未完成时才提示错误
			if (!taskRecord.value || (taskRecord.value.status !== 2 && taskRecord.value.status !== 3)) {
				taskErrorMsg.value = "与服务器的实时连接中断，请检查网络或刷新重试。";
				ElMessage.error(taskErrorMsg.value);
			}
			close(); // 连接出错时，清理资源
		};
	};

	const close = () => {
		if (eventSource.value) {
			eventSource.value.close();
			eventSource.value = null;
			console.log("[SSE] 连接已手动关闭。");
		}
	};

	const isConnected = computed(() => !!eventSource.value && eventSource.value.readyState === EventSource.OPEN);

	// 组件卸载时自动关闭连接
	onUnmounted(() => {
		close();
	});

	return {
		taskRecord,
		progressDetails,
		taskErrorMsg,
		isConnected,
		connect,
		close
	};
} 