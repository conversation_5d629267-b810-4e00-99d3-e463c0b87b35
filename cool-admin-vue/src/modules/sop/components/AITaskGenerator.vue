<template>
  <div class="ai-task-generator-layout">
    <!-- 主要内容区域 -->
    <div class="main-content" :class="{ 'has-sidebar': showSsePanel }">
      <el-scrollbar>
        <div class="ai-task-generator ai-task-generator-page">
          <div class="header">
            <div class="icon">🎯</div>
            <div class="title-section">
              <h2>AI智能生成任务</h2>
              <p>基于自然语言描述，智能生成标准作业任务</p>
            </div>
            <!-- SSE面板控制按钮 -->
            <div class="header-actions">
              <el-button
                v-if="progressDetails.length > 0 || asyncTaskGenerating"
                size="small"
                @click="toggleSsePanel"
                :type="showSsePanel ? 'primary' : 'info'"
              >
                <el-icon><ChatDotRound /></el-icon>
                {{ showSsePanel ? '隐藏交互' : '显示交互' }}
              </el-button>
            </div>
          </div>

          <div class="generator-content">
            <!-- 表单视图 -->
            <template v-if="currentView === 'form'">
              <div class="input-section">
                <div class="input-label">
                  <el-icon><Star /></el-icon>
                  <span>描述你需要的作业流程</span>
                </div>
                
                <el-input
                  v-model="formData.taskDescription"
                  type="textarea"
                  :rows="6"
                  placeholder="例如：我需要创建客户满意度调查的工作流程，优先级高，时间周期为下个月..."
                  class="input-textarea"
                  maxlength="1000"
                  show-word-limit
                  @keyup.ctrl.enter="handlePreview"
                />
                
                <!-- 必填项：所属部门 -->
                <div class="required-field">
                  <div class="field-label">
                    <el-icon><OfficeBuilding /></el-icon>
                    <span>所属部门</span>
                    <span class="required-mark">*</span>
                  </div>
                  <!-- 只保留下拉选择，支持内置搜索 -->
                  <AIDepartmentSelector
                    v-model="formData.departmentIds"
                    :options="allDepartments"
                    filterable
                    placeholder="请选择所属部门（可搜索）"
                  />
                </div>
                
                <!-- 场景标签建议 -->
                <div class="scenario-suggestions" v-if="!showAdvanced">
                  <div class="suggestion-label">
                    <el-icon><MagicStick /></el-icon>
                    <span>场景标签</span>
                    <el-switch hidden
                      v-model="aiSmartSuggestion"
                      active-text="AI智能建议"
                      inactive-text="直接填入"
                      class="ai-switch"
                      size="small"
                    />
                    <el-icon v-if="loadingSuggestions" class="is-loading"><Loading /></el-icon>
                  </div>
                  <div class="suggestion-tags" v-if="scenarioTags.length > 0">
                    <el-tag
                      v-for="(scenario, index) in scenarioTags"
                      :key="scenario.id || index"
                      class="scenario-tag"
                      :type="getTagType(scenario)"
                      effect="plain"
                      @click="handleScenarioClick(scenario)"
                      :loading="scenario.loading"
                    >
                      <el-icon v-if="scenario.loading"><Loading /></el-icon>
                      {{ scenario.name }}
                    </el-tag>
                  </div>
                  <div class="no-suggestions" v-else-if="!loadingSuggestions">
                    <p>暂无可用的场景标签</p>
                  </div>
                  
                  <!-- AI生成等待动画 -->
                  <div v-if="aiGenerating" class="ai-generating">
                    <el-icon class="is-loading"><Loading /></el-icon>
                    <span>AI正在生成场景内容...</span>
                  </div>
                </div>

                <div class="advanced-options" v-if="showAdvanced">
                  <el-row :gutter="16">
                    <el-col :span="6">
                      <el-form-item label="优先级">
                        <el-select v-model="formData.priority" placeholder="选择优先级">
                          <el-option label="低" :value="1"></el-option>
                          <el-option label="中低" :value="2"></el-option>
                          <el-option label="中" :value="3"></el-option>
                          <el-option label="高" :value="4"></el-option>
                          <el-option label="紧急" :value="5"></el-option>
                        </el-select>
                      </el-form-item>
                    </el-col>

                    <el-col :span="18">
                      <el-form-item label="执行时间">
                        <el-date-picker
                          v-model="formData.timeRange"
                          type="datetimerange"
                          range-separator="至"
                          start-placeholder="开始时间"
                          end-placeholder="结束时间"
                          format="YYYY-MM-DD HH:mm:ss"
                          value-format="YYYY-MM-DD HH:mm:ss"
                          style="width: 100%"
                        />
                      </el-form-item>
                    </el-col>
                  </el-row>
                  

                  
                  <el-row :gutter="16">
                    <el-col :span="12">
                      <el-form-item>
                        <el-checkbox v-model="formData.useAIEnhancement">
                          使用AI增强功能
                        </el-checkbox>
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item>
                        <el-checkbox v-model="formData.autoAssign">
                          自动分配负责人
                        </el-checkbox>
                      </el-form-item>
                    </el-col>
                  </el-row>
                </div>
              </div>

              <div class="action-section">
                <el-button
                  type="primary"
                  size="large"
                  :loading="generating"
                  :disabled="!formData.taskDescription?.trim() || !formData.departmentIds?.length"
                  @click="handlePreview"
                  class="generate-btn"
                >
                  <el-icon v-if="!generating"><Star /></el-icon>
                  {{ generating ? '正在生成预览...' : '生成预览' }}
                </el-button>

                <el-button
                  size="large"
                  @click="showAdvanced = !showAdvanced"
                  class="advanced-btn"
                >
                  {{ showAdvanced ? '收起高级选项' : '展开高级选项' }}
                </el-button>
              </div>

              <!-- 在表单页面显示任务生成结果 -->
              <div v-if="previewResult && previewResult.tasks && previewResult.tasks.length > 0 && previewResult.mode === 'generate'" class="form-result-section">
                <el-divider content-position="left">
                  <el-icon><Check /></el-icon>
                  <span>任务生成结果</span>
                </el-divider>
                
                <div class="result-summary">
                  <el-alert
                    type="success"
                    :closable="false"
                    show-icon
                    :title="`成功生成 ${previewResult.tasks.length} 个任务`"
                  >
                    <template #default>
                      <p>任务已成功生成并分配，您可以继续生成新的任务或查看历史记录。</p>
                    </template>
                  </el-alert>
                </div>

                <div class="result-actions">
                  <el-button type="primary" @click="router.push('/task/info')">
                    <el-icon><List /></el-icon>
                    查看生成的任务
                  </el-button>
                  <el-button @click="goHistory">
                    <el-icon><Clock /></el-icon>
                    查看历史记录
                  </el-button>
                  <el-button @click="previewResult = null" text>
                    <el-icon><Close /></el-icon>
                    清除结果
                  </el-button>
                </div>
              </div>
            </template>

            <!-- 进度视图 -->
            <template v-if="currentView === 'progress'">
              <div class="progress-container">
                <h3 class="panel-title">AI任务生成进度</h3>
                
                <!-- 进度条 -->
                <div v-if="!isTaskFinished" class="progress-bar-container">
                  <el-progress
                    :percentage="lastAsyncTaskRecord ? lastAsyncTaskRecord.progress || 0 : 0"
                    :stroke-width="15"
                    striped
                    striped-flow
                    :color="progressColor"
                  />
                  <div class="progress-text">{{ progressText }}</div>
                </div>

                <div v-if="taskErrorMsg" class="error-message">
                  <el-alert type="error" :closable="false" show-icon>
                    <p>{{ taskErrorMsg }}</p>
                  </el-alert>
                  <el-button type="warning" plain @click="retryAsyncGenerate" class="retry-button">
                    重试
                  </el-button>
                </div>

                <div v-if="isTaskSuccess" class="success-result">
                  <el-alert type="success" :closable="false" show-icon title="任务已生成">
                    <p>任务已成功生成并分配，您可以在任务列表或下方历史记录中查看。</p>
                  </el-alert>
                </div>
              </div>
            </template>

            <!-- 预览结果视图 -->
            <template v-if="currentView === 'preview'">
              <div class="preview-container">
                <h3 class="panel-title">任务预览</h3>

                <div v-if="previewResult" class="preview-content">
                  <!-- 多部门预览 -->
                  <div v-if="previewResult.multiDepartment && previewResult.departments">
                    <!-- 概览统计 - 紧凑版 -->
                    <div class="overview-compact">
                      <div class="overview-line">
                        <span class="overview-title">📊 任务概览：</span>
                        <div class="overview-stats-inline">
                          <span class="stat-badge">{{ previewResult.summary?.totalDepartments || previewResult.departments.length }}个部门</span>
                          <span class="stat-badge">{{ previewResult.summary?.totalTasks || getTotalTasksCount() }}个任务</span>
                          <span class="stat-badge success">{{ getAssignedTasksCount() }}已分配</span>
                          <span class="stat-badge warning">{{ getUnassignedTasksCount() }}待分配</span>
                        </div>
                      </div>
                      <div v-if="previewResult.summary?.scenario" class="scenario-line">
                        <span class="scenario-title">📋 场景：</span>
                        <span class="scenario-name">{{ previewResult.summary.scenario.name }}</span>
                        <el-tag size="small" type="info">{{ previewResult.summary.scenario.code }}</el-tag>
                      </div>
                    </div>

                    <!-- 部门任务展示 -->
                    <div class="departments-section">
                      <el-tabs v-model="activeTab" type="card" class="department-tabs">
                        <el-tab-pane 
                          v-for="(dept, index) in previewResult.departments" 
                          :key="dept.departmentId || index"
                          :label="getDepartmentTabLabel(dept)"
                          :name="String(dept.departmentId || index)"
                        >
                          <!-- 部门任务列表 -->
                          <div class="department-tasks">
                            <div v-if="dept.tasks && dept.tasks.length > 0" class="tasks-preview">
                              <div class="tasks-header">
                                <h4>{{ getDepartmentName(dept) }} - 任务列表 ({{ dept.tasks.length }}个)</h4>
                                <el-button size="small" @click="toggleAllTasks">
                                  {{ allTasksExpanded ? "收起全部" : "展开全部" }}
                                </el-button>
                              </div>

                              <div class="tasks-list">
                                <el-card
                                  v-for="(task, taskIndex) in dept.tasks"
                                  :key="taskIndex"
                                  class="task-preview-card"
                                  shadow="hover"
                                >
                                  <template #header>
                                    <div class="task-header">
                                      <div class="task-title">
                                        <div class="step-number">{{ taskIndex + 1 }}</div>
                                        <el-icon><Document /></el-icon>
                                        <span>{{ task.taskName || task.name }}</span>
                                      </div>
                                      <div class="task-actions">
                                        <el-tag :type="getPriorityType(task.priority || formData.priority)" size="small">
                                          {{ getPriorityLabel(task.priority || formData.priority) }}
                                        </el-tag>
                                        <el-button size="small" text @click="toggleTaskDetail(taskIndex)">
                                          {{ expandedTasks.includes(taskIndex) ? "收起" : "详情" }}
                                        </el-button>
                                      </div>
                                    </div>
                                  </template>

                                  <div class="task-summary">
                                    <!-- 任务描述 -->
                                    <p class="task-description">
                                      {{ task.description || task.taskDescription || "暂无描述" }}
                                    </p>
                                    
                                    <!-- 关键信息标签组 -->
                                    <div class="task-info-tags">
                                      <!-- 执行人 -->
                                      <div class="info-tag assignee-tag">
                                        <el-icon><User /></el-icon>
                                        <span v-if="task.assigneeName" class="assigned-user">
                                          {{ task.assigneeName }}
                                          <el-tag 
                                            v-if="task.assignmentConfidence" 
                                            :type="getConfidenceType(task.assignmentConfidence)"
                                            size="small"
                                            class="confidence-tag"
                                          >
                                            {{ task.assignmentConfidence }}%
                                          </el-tag>
                                        </span>
                                        <span v-else class="unassigned">未分配</span>
                                      </div>
                                      
                                      <!-- 时间 -->
                                      <div class="info-tag time-tag" v-if="formData.timeRange && formData.timeRange[0]">
                                        <el-icon><Clock /></el-icon>
                                        <span>{{ formatTimeRange(formData.timeRange) }}</span>
                                      </div>
                                      
                                      <!-- 地点 -->
                                      <div class="info-tag location-tag" v-if="task.location">
                                        <el-icon><OfficeBuilding /></el-icon>
                                        <span>{{ task.location }}</span>
                                      </div>
                                      
                                      <!-- 任务内容/角色 -->
                                      <div class="info-tag role-tag" v-if="task.employeeRole">
                                        <el-icon><List /></el-icon>
                                        <span>{{ task.employeeRole }}</span>
                                      </div>
                                      <!-- 分配按钮紧凑同行 -->
                                      <el-button 
                                        size="small" 
                                        type="primary" 
                                        text
                                        class="assign-btn"
                                        @click="showAssignmentDialog(task, taskIndex)"
                                      >
                                        <el-icon><User /></el-icon>
                                        {{ task.assigneeName ? '调整分配' : '手动分配' }}
                                      </el-button>
                                    </div>
                                  </div>

                                  <el-collapse-transition>
                                    <div v-show="expandedTasks.includes(taskIndex)" class="task-details">
                                      <div class="task-detail-content">
                                        <div class="detail-item">
                                          <span class="label">任务描述:</span>
                                          <span class="value">{{ task.description || task.taskDescription || task.name }}</span>
                                        </div>
                                        <div class="detail-item" v-if="task.employeeRole">
                                          <span class="label">执行角色:</span>
                                          <span class="value">{{ task.employeeRole }}</span>
                                        </div>
                                        <div class="detail-item" v-if="task.location">
                                          <span class="label">地点:</span>
                                          <span class="value">{{ task.location }}</span>
                                        </div>
                                        <div class="detail-item" v-if="task.userActivity">
                                          <span class="label">用户活动:</span>
                                          <span class="value">{{ task.userActivity }}</span>
                                        </div>
                                        <div class="detail-item" v-if="task.employeeBehavior">
                                          <span class="label">员工行为:</span>
                                          <span class="value">{{ task.employeeBehavior }}</span>
                                        </div>
                                        <div class="detail-item" v-if="task.workHighlight">
                                          <span class="label">工作亮点:</span>
                                          <span class="value">{{ task.workHighlight }}</span>
                                        </div>
                                        <div class="detail-item" v-if="task.requirement">
                                          <span class="label">任务要求:</span>
                                          <span class="value">{{ task.requirement }}</span>
                                        </div>
                                        <!-- 执行人详情显示 -->
                                        <div v-if="task.assigneeName" class="assignment-details">
                                          <div class="detail-item">
                                            <span class="label">分配执行人:</span>
                                            <span class="value">{{ task.assigneeName }}</span>
                                          </div>
                                          <div class="detail-item" v-if="task.assignmentReason">
                                            <span class="label">分配原因:</span>
                                            <span class="value assignment-reason">{{ task.assignmentReason }}</span>
                                          </div>
                                          <div class="detail-item" v-if="task.assignmentConfidence">
                                            <span class="label">分配置信度:</span>
                                            <span class="value">
                                              <el-progress 
                                                :percentage="task.assignmentConfidence" 
                                                :stroke-width="6"
                                                :color="getConfidenceColor(task.assignmentConfidence)"
                                                :show-text="false"
                                                style="width: 100px; display: inline-block; margin-right: 8px;"
                                              />
                                              {{ task.assignmentConfidence }}%
                                            </span>
                                          </div>
                                          <!-- 候选人列表 -->
                                          <div v-if="task.candidates && task.candidates.length > 0" class="detail-item">
                                            <span class="label">其他候选人:</span>
                                            <div class="candidates-list">
                                              <el-tag 
                                                v-for="candidate in task.candidates.slice(0, 3)" 
                                                :key="candidate.userId || candidate.id"
                                                size="small"
                                                class="candidate-tag"
                                                @click="quickAssign(task, taskIndex, candidate)"
                                              >
                                                {{ candidate.userName || candidate.name }} ({{ candidate.score }}%)
                                              </el-tag>
                                            </div>
                                          </div>
                                        </div>
                                      </div>
                                    </div>
                                  </el-collapse-transition>
                                </el-card>
                              </div>
                            </div>
                            <div v-else class="no-tasks">
                              <el-empty description="该部门暂无任务" />
                            </div>
                          </div>
                        </el-tab-pane>
                      </el-tabs>
                    </div>

                    <!-- 预览操作按钮 -->
                    <div class="preview-actions">
                      <el-button size="large" @click="currentView = 'form'">
                        <el-icon><Close /></el-icon>
                        返回编辑
                      </el-button>
                      <div v-if="hasAnyTasks && previewResult.mode !== 'generate'" class="generate-actions">
                        <el-button 
                          size="large" 
                          type="primary" 
                          @click="handleAsyncGenerate"
                        >
                          <el-icon><Check /></el-icon>
                          接受预览并生成
                        </el-button>
                        <el-button 
                          size="large" 
                          type="warning" 
                          plain
                          @click="handleRegenerateFromScratch"
                        >
                          <el-icon><Refresh /></el-icon>
                          重新生成
                        </el-button>
                      </div>
                    </div>
                  </div>

                  <!-- 单部门预览（统一使用tab布局） -->
                  <div v-else>
                    <!-- 概览统计 - 紧凑版 -->
                    <div class="overview-compact">
                      <div class="overview-line">
                        <span class="overview-title">📊 任务概览：</span>
                        <div class="overview-stats-inline">
                          <span class="stat-badge">1个部门</span>
                          <span class="stat-badge">{{ previewResult.tasks?.length || 0 }}个任务</span>
                          <span class="stat-badge success">{{ getSingleDepartmentAssignedCount() }}已分配</span>
                          <span class="stat-badge warning">{{ getSingleDepartmentUnassignedCount() }}待分配</span>
                        </div>
                      </div>
                      <div v-if="previewResult.scenario" class="scenario-line">
                        <span class="scenario-title">📋 场景：</span>
                        <span class="scenario-name">{{ previewResult.scenario.name }}</span>
                        <el-tag size="small" type="info">{{ previewResult.scenario.code }}</el-tag>
                      </div>
                    </div>

                    <!-- 单部门任务展示 - 使用tab结构保持一致性 -->
                    <div class="department-tasks">
                      <el-tabs v-model="activeTab" type="card">
                        <el-tab-pane 
                          :label="getSingleDepartmentTabLabel()"
                          name="0"
                        >
                          <div class="department-content">
                            <div v-if="previewResult.tasks && previewResult.tasks.length > 0" class="tasks-section">
                              <div class="tasks-header">
                                <h4>任务列表 ({{ previewResult.tasks.length }}个)</h4>
                                <el-button size="small" @click="toggleAllTasks">
                                  {{ allTasksExpanded ? "收起全部" : "展开全部" }}
                                </el-button>
                              </div>

                              <div class="tasks-list">
                                <el-card
                                  v-for="(task, index) in previewResult.tasks"
                                  :key="index"
                                  class="task-preview-card"
                                  shadow="hover"
                                >
                                  <template #header>
                                    <div class="task-header">
                                      <div class="task-title">
                                        <div class="step-number">{{ index + 1 }}</div>
                                        <el-icon><Document /></el-icon>
                                        <span>{{ task.taskName || task.name }}</span>
                                      </div>
                                      <div class="task-actions">
                                        <el-tag :type="getPriorityType(task.priority || formData.priority)" size="small">
                                          {{ getPriorityLabel(task.priority || formData.priority) }}
                                        </el-tag>
                                        <el-button size="small" text @click="toggleTaskDetail(index)">
                                          {{ expandedTasks.includes(index) ? "收起" : "详情" }}
                                        </el-button>
                                      </div>
                                    </div>
                                  </template>

                                  <div class="task-summary">
                                    <!-- 任务描述 -->
                                    <p class="task-description">
                                      {{ task.description || task.taskDescription || "暂无描述" }}
                                    </p>
                                    
                                    <!-- 关键信息标签组 -->
                                    <div class="task-info-tags">
                                      <!-- 执行人 -->
                                      <div class="info-tag assignee-tag">
                                        <el-icon><User /></el-icon>
                                        <span v-if="task.assigneeName" class="assigned-user">
                                          {{ task.assigneeName }}
                                          <el-tag 
                                            v-if="task.assignmentConfidence" 
                                            :type="getConfidenceType(task.assignmentConfidence)"
                                            size="small"
                                            class="confidence-tag"
                                          >
                                            {{ task.assignmentConfidence }}%
                                          </el-tag>
                                        </span>
                                        <span v-else class="unassigned">未分配</span>
                                      </div>
                                      
                                      <!-- 时间 -->
                                      <div class="info-tag time-tag" v-if="formData.timeRange && formData.timeRange[0]">
                                        <el-icon><Clock /></el-icon>
                                        <span>{{ formatTimeRange(formData.timeRange) }}</span>
                                      </div>
                                      
                                      <!-- 地点 -->
                                      <div class="info-tag location-tag" v-if="task.location">
                                        <el-icon><OfficeBuilding /></el-icon>
                                        <span>{{ task.location }}</span>
                                      </div>
                                      
                                      <!-- 任务内容/角色 -->
                                      <div class="info-tag role-tag" v-if="task.employeeRole">
                                        <el-icon><List /></el-icon>
                                        <span>{{ task.employeeRole }}</span>
                                      </div>
                                      <!-- 分配按钮紧凑同行 -->
                                      <el-button 
                                        size="small" 
                                        type="primary" 
                                        text
                                        class="assign-btn"
                                        @click="showAssignmentDialog(task, index)"
                                      >
                                        <el-icon><User /></el-icon>
                                        {{ task.assigneeName ? '调整分配' : '手动分配' }}
                                      </el-button>
                                    </div>
                                  </div>

                                  <el-collapse-transition>
                                    <div v-show="expandedTasks.includes(index)" class="task-details">
                                      <div class="task-detail-content">
                                        <div class="detail-item">
                                          <span class="label">任务描述:</span>
                                          <span class="value">{{ task.description || task.taskDescription || task.name }}</span>
                                        </div>
                                        <div class="detail-item" v-if="task.employeeRole">
                                          <span class="label">执行角色:</span>
                                          <span class="value">{{ task.employeeRole }}</span>
                                        </div>
                                        <div class="detail-item" v-if="task.location">
                                          <span class="label">地点:</span>
                                          <span class="value">{{ task.location }}</span>
                                        </div>
                                        <div class="detail-item" v-if="task.userActivity">
                                          <span class="label">用户活动:</span>
                                          <span class="value">{{ task.userActivity }}</span>
                                        </div>
                                        <div class="detail-item" v-if="task.employeeBehavior">
                                          <span class="label">员工行为:</span>
                                          <span class="value">{{ task.employeeBehavior }}</span>
                                        </div>
                                        <div class="detail-item" v-if="task.workHighlight">
                                          <span class="label">工作亮点:</span>
                                          <span class="value">{{ task.workHighlight }}</span>
                                        </div>
                                        <div class="detail-item" v-if="task.requirement">
                                          <span class="label">任务要求:</span>
                                          <span class="value">{{ task.requirement }}</span>
                                        </div>
                                        <!-- 执行人详情显示 - 区分预览模式和正式生成模式 -->
                                        <div v-if="previewResult?.mode === 'preview' && task.assigneeName" class="assignment-details">
                                          <div class="detail-item">
                                            <span class="label">分配执行人:</span>
                                            <span class="value">{{ task.assigneeName }}</span>
                                          </div>
                                          <div class="detail-item" v-if="task.assignmentReason">
                                            <span class="label">分配原因:</span>
                                            <span class="value assignment-reason">{{ task.assignmentReason }}</span>
                                          </div>
                                          <div class="detail-item" v-if="task.assignmentConfidence">
                                            <span class="label">分配置信度:</span>
                                            <span class="value">
                                              <el-progress 
                                                :percentage="task.assignmentConfidence" 
                                                :stroke-width="6"
                                                :color="getConfidenceColor(task.assignmentConfidence)"
                                                :show-text="false"
                                                style="width: 100px; display: inline-block; margin-right: 8px;"
                                              />
                                              {{ task.assignmentConfidence }}%
                                            </span>
                                          </div>
                                          <!-- 候选人列表 -->
                                          <div v-if="task.candidates && task.candidates.length > 0" class="detail-item">
                                            <span class="label">其他候选人:</span>
                                            <div class="candidates-list">
                                              <el-tag 
                                                v-for="candidate in task.candidates.slice(0, 3)" 
                                                :key="candidate.userId || candidate.id"
                                                size="small"
                                                class="candidate-tag"
                                                @click="quickAssign(task, index, candidate)"
                                              >
                                                {{ candidate.userName || candidate.name }} ({{ candidate.confidence }}%)
                                              </el-tag>
                                              <span v-if="task.candidates.length > 3" class="more-candidates">
                                                等{{ task.candidates.length }}人
                                              </span>
                                            </div>
                                          </div>
                                        </div>
                                        <div v-else-if="previewResult?.mode === 'generate'">
                                          <!-- 正式生成模式的详细信息 -->
                                          <div class="detail-item" v-if="task.assigneeName">
                                            <span class="label">分配执行人:</span>
                                            <span class="value">{{ task.assigneeName }}</span>
                                          </div>
                                          <div class="detail-item" v-if="task.executors && task.executors.length > 0">
                                            <span class="label">执行团队:</span>
                                            <div class="executors-list">
                                              <el-tag 
                                                v-for="executor in task.executors" 
                                                :key="executor.userId"
                                                size="small"
                                                type="success"
                                              >
                                                {{ executor.userName }}
                                              </el-tag>
                                            </div>
                                          </div>
                                        </div>
                                        <div class="detail-item" v-if="task.estimatedDuration">
                                          <span class="label">预估时长:</span>
                                          <span class="value">{{ task.estimatedDuration }}分钟</span>
                                        </div>
                                        <div class="detail-item" v-if="task.priority">
                                          <span class="label">优先级:</span>
                                          <el-tag :type="getPriorityType(task.priority)" size="small">
                                            {{ getPriorityLabel(task.priority) }}
                                          </el-tag>
                                        </div>
                                      </div>
                                    </div>
                                  </el-collapse-transition>
                                </el-card>
                              </div>
                            </div>
                            <div v-else class="no-tasks">
                              <el-empty description="暂无任务" />
                            </div>
                          </div>
                        </el-tab-pane>
                      </el-tabs>
                    </div>
                    
                    <!-- 预览操作按钮 -->
                    <div class="preview-actions">
                      <el-button size="large" @click="currentView = 'form'">
                        <el-icon><Close /></el-icon>
                        返回编辑
                      </el-button>
                      <div v-if="hasAnyTasks && previewResult.mode !== 'generate'" class="generate-actions">
                        <el-button 
                          size="large" 
                          type="primary" 
                          @click="handleAsyncGenerate"
                        >
                          <el-icon><Check /></el-icon>
                          接受预览并生成
                        </el-button>
                        <el-button 
                          size="large" 
                          type="warning" 
                          plain
                          @click="handleRegenerateFromScratch"
                        >
                          <el-icon><Refresh /></el-icon>
                          重新生成
                        </el-button>
                      </div>
                    </div>
                  </div>

                  <div v-if="!hasAnyTasks" class="no-tasks">
                    <el-alert type="warning" :closable="false" show-icon>
                      <p>未生成任何任务，请检查您的描述或返回上一步重新生成。</p>
                    </el-alert>
                  </div>
                </div>
              </div>
            </template>

            <!-- 底部操作按钮 -->
            <div class="bottom-actions">
              <el-button size="large" @click="backToForm">返回表单</el-button>
              <el-button size="large" type="primary" @click="goHistory">查看历史</el-button>
            </div>
          </div>
        </div>
      </el-scrollbar>
    </div>

    <!-- 右侧SSE交互面板 -->
    <div v-if="showSsePanel" class="sse-panel panel-visible">
      <div class="sse-header">
        <div class="header-left">
          <div class="header-icon">🤖</div>
          <div class="header-title">
            <h4>AI智能助手</h4>
            <span class="header-subtitle">实时生成进度</span>
          </div>
        </div>
        <el-button size="small" @click="toggleSsePanel" circle class="close-btn" :icon="Close" />
      </div>
      <div class="sse-content">
        <el-scrollbar>
          <div class="progress-messages">
            <div 
              v-for="(detail, index) in progressDetails" 
              :key="`msg-${index}-${getDetailTime(detail)}`"
              class="progress-message"
              :class="getMessageClass(detail)"
              :style="{ 'animation-delay': `${index * 0.1}s` }"
            >
              <div class="message-indicator">
                <div class="indicator-dot" :class="getIndicatorClass(detail)">
                  <div class="dot-inner"></div>
                  <div class="dot-ripple"></div>
                </div>
                <div class="indicator-line" v-if="index < progressDetails.length - 1"></div>
              </div>
              <div class="message-content-wrapper">
                <div class="message-time">{{ getDetailTime(detail) }}</div>
                <div class="message-content">
                  <div class="message-text">{{ getDetailText(detail) }}</div>
                  <div class="message-decoration"></div>
                </div>
              </div>
            </div>
            
            <!-- 当前任务状态显示 -->
            <div v-if="lastAsyncTaskRecord && progressDetails.length === 0" class="current-status">
              <div class="status-message">
                <div class="message-time">{{ new Date().toLocaleTimeString() }}</div>
                <div class="message-content">{{ progressText }}</div>
              </div>
              <div v-if="lastAsyncTaskRecord.progress !== undefined" class="status-progress">
                <el-progress 
                  :percentage="lastAsyncTaskRecord.progress || 0"
                  :stroke-width="4"
                  :show-text="false"
                  :color="progressColor"
                />
              </div>
            </div>
            
            <!-- 空状态提示 -->
            <div v-if="progressDetails.length === 0 && !lastAsyncTaskRecord" class="empty-messages">
              <div class="empty-animation">
                <div class="loading-dots">
                  <div class="dot"></div>
                  <div class="dot"></div>
                  <div class="dot"></div>
                </div>
              </div>
              <p>AI正在准备生成任务...</p>
            </div>
          </div>
        </el-scrollbar>
      </div>
    </div>

    <!-- 任务分配对话框 -->
    <el-dialog 
      v-model="assignmentDialog.visible" 
      title="任务分配" 
      width="800px"
      :close-on-click-modal="false"
      class="assignment-dialog"
    >
      <div class="assignment-content">
        <div class="task-info">
          <h4>{{ assignmentDialog.task?.taskName || assignmentDialog.task?.name }}</h4>
          <div class="task-meta">
            <el-tag v-if="assignmentDialog.task?.employeeRole" size="small">
              {{ assignmentDialog.task.employeeRole }}
            </el-tag>
            <el-tag v-if="assignmentDialog.task?.stepName" size="small" type="info">
              {{ assignmentDialog.task.stepName }}
            </el-tag>
          </div>
        </div>

        <div class="assignment-section">
          <h5>选择执行人</h5>
          
          <!-- 筛选条件 -->
          <div class="search-filters">
            <div class="filter-row">
              <div class="filter-item">
                <label>部门筛选</label>
                <el-select 
                  v-model="assigneeFilter.department" 
                  placeholder="选择部门" 
                  clearable
                  @change="filterAssignees"
                >
                  <el-option 
                    v-for="dept in departmentOptions" 
                    :key="dept.id" 
                    :label="dept.name" 
                    :value="dept.id"
                  />
                </el-select>
              </div>
              <div class="filter-item">
                <label>角色筛选</label>
                <el-select 
                  v-model="assigneeFilter.role" 
                  placeholder="选择角色" 
                  clearable
                  @change="filterAssignees"
                >
                  <el-option 
                    v-for="role in roleOptions" 
                    :key="role.id" 
                    :label="role.name" 
                    :value="role.id"
                  />
                </el-select>
              </div>
              <div class="filter-item">
                <label>搜索</label>
                <el-input 
                  v-model="assigneeFilter.search" 
                  placeholder="姓名或手机号"
                  clearable
                  @input="filterAssignees"
                >
                  <template #prefix>
                    <el-icon><Search /></el-icon>
                  </template>
                </el-input>
              </div>
            </div>
          </div>
          
          <!-- 执行人列表 -->
          <div class="assignee-list">
            <div class="list-header">
              <span>可选执行人 ({{ availableAssignees.length }}人)</span>
              <el-button size="small" @click="fetchAvailableAssignees()" :loading="assigneeLoading">
                <el-icon><Refresh /></el-icon>
                刷新
              </el-button>
            </div>
            
            <div class="assignee-grid">
              <div 
                v-for="assignee in availableAssignees" 
                :key="assignee.id"
                class="assignee-card"
                :class="{ 'selected': assignmentDialog.selectedAssignee === assignee.id }"
                @click="selectAssignee(assignee)"
              >
                <div class="assignee-avatar">
                  <el-avatar :size="40" :src="assignee.avatar">
                    {{ assignee.name?.charAt(0) }}
                  </el-avatar>
                </div>
                <div class="assignee-info">
                  <div class="assignee-name">{{ assignee.name || assignee.userName || assignee.user_name }}</div>
                  <el-tag
                    v-if="assignee.roleName || assignee.role_name || assignee.role"
                    size="small"
                    type="info"
                    class="assignee-role-tag"
                    style="margin-right: 4px;"
                  >
                    {{ assignee.roleName || assignee.role_name || assignee.role }}
                  </el-tag>
                  <el-tag
                    v-if="assignee.departmentName || assignee.deptName || assignee.department_name || assignee.department"
                    size="small"
                    type="success"
                    class="assignee-dept-tag"
                  >
                    {{ assignee.departmentName || assignee.deptName || assignee.department_name || assignee.department }}
                  </el-tag>
                  <div class="assignee-phone">{{ assignee.phone || assignee.mobile || assignee.phoneNumber }}</div>
                </div>
                <div class="assignee-stats">
                  <div class="stat-item">
                    <span class="stat-label">负载</span>
                    <el-progress 
                      :percentage="assignee.workload || 0" 
                      :stroke-width="4"
                      :show-text="false"
                      :color="getWorkloadColor(assignee.workload)"
                    />
                    <span class="stat-value">{{ assignee.workload || 0 }}%</span>
                  </div>
                  <div class="stat-item">
                    <span class="stat-label">评分</span>
                    <el-rate 
                      v-model="assignee.performanceScore" 
                      :max="5"
                      :allow-half="true"
                      disabled
                      size="small"
                    />
                    <span class="stat-value">{{ assignee.performanceScore || 0 }}</span>
                  </div>
                </div>
                <div class="assignee-actions">
                  <el-checkbox 
                    :model-value="assignmentDialog.selectedAssignee === assignee.id"
                    @change="(val) => val && selectAssignee(assignee)"
                  />
                </div>
              </div>
            </div>
            
            <div v-if="availableAssignees.length === 0" class="no-assignees">
              <el-empty description="没有找到符合条件的执行人" />
            </div>
          </div>
        </div>

        <div class="assignment-reason">
          <h5>分配说明</h5>
          <el-input
            v-model="assignmentDialog.reason"
            type="textarea"
            :rows="3"
            placeholder="请输入分配说明（可选）"
          />
        </div>
      </div>

      <template #footer>
        <el-button @click="assignmentDialog.visible = false">取消</el-button>
        <el-button 
          type="primary" 
          @click="confirmAssignmentChange"
          :disabled="!assignmentDialog.selectedAssignee"
          :loading="assignmentDialog.loading"
        >
          确认分配
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
// 导入必要的依赖
import { ref, reactive, onMounted, nextTick, computed, watch, onUnmounted } from "vue";
import { ElMessage, type FormInstance } from "element-plus";
import {
  Star,
  Check,
  Close,
  Document,
  Refresh,
  Clock,
  User,
  Promotion,
  More,
  Loading,
  ChatDotRound,
  MagicStick,
  OfficeBuilding,
  List,
  Search
} from "@element-plus/icons-vue";
import { useCool } from "/@/cool";
import { useRouter } from "vue-router";
import { useSseTaskStatus } from "../composables/useSseTaskStatus";
import AIDepartmentSelector from "./ai-department-selector.vue";
import { debounce } from "lodash-es";
// 部门选择相关逻辑
// import { getDepartmentList } from "../utils/department"; // 移除无效和重复导入

const { service } = useCool();
const router = useRouter();

defineOptions({
  name: "ai-task-generator"
});

// 类型声明
interface PreviewResult {
  mode?: string;
  multiDepartment?: boolean;
  departments?: Array<{
    departmentId: number;
    departmentName: string;
    scenario: any;
    tasks: any[];
  }>;
  summary?: {
    totalDepartments?: number;
    totalTasks?: number;
    scenario?: any;
  };
}

// 默认时间范围：从今天开始一个月
const getDefaultTimeRange = () => {
  const now = new Date();
  const startTime = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 9, 0, 0);
  const endTime = new Date(now.getFullYear(), now.getMonth() + 1, now.getDate(), 18, 0, 0);

  const formatDateTime = (date: Date) => {
    return (
      date.getFullYear() +
      "-" +
      String(date.getMonth() + 1).padStart(2, "0") +
      "-" +
      String(date.getDate()).padStart(2, "0") +
      " " +
      String(date.getHours()).padStart(2, "0") +
      ":" +
      String(date.getMinutes()).padStart(2, "0") +
      ":" +
      String(date.getSeconds()).padStart(2, "0")
    );
  };

  return [formatDateTime(startTime), formatDateTime(endTime)];
};

// 响应式数据
const formData = reactive({
  taskDescription: "",
  priority: 3,
  timeRange: getDefaultTimeRange(),
  useAIEnhancement: true,
  autoAssign: false,
  departmentId: [] as unknown[],
  departmentIds: [] as number[]
});

// 智能场景建议数据
const scenarioTags = ref<any[]>([]);
const aiSmartSuggestion = ref(false);
const aiGenerating = ref(false);
const loadingSuggestions = ref(false);

const generating = ref(false);
const previewResult = ref<PreviewResult | null>(null);
const showAdvanced = ref(false);

// 预览相关状态
const expandedTasks = ref<number[]>([]);
const allTasksExpanded = ref(false);

// SSE 任务状态
const {
  taskRecord: lastAsyncTaskRecord,
  progressDetails,
  isConnected,
  taskErrorMsg,
  connect: connectSse,
  close: disconnectSse
} = useSseTaskStatus();

const asyncTaskGenerating = ref(false);

// SSE交互面板状态
const showSsePanel = ref(false);

// 切换SSE面板显示状态
const toggleSsePanel = () => {
  showSsePanel.value = !showSsePanel.value;
};

// 重置SSE面板
const resetSsePanel = () => {
  progressDetails.value = [];
};

// 视图状态管理
const currentView = ref<'form' | 'progress' | 'preview'>('form');

// 多部门预览相关状态
const activeTab = ref('0');

// 计算属性
const isTaskFinished = computed(
  () => lastAsyncTaskRecord.value && (lastAsyncTaskRecord.value.status === 2 || lastAsyncTaskRecord.value.status === 3)
);

const isTaskSuccess = computed(
  () => lastAsyncTaskRecord.value && lastAsyncTaskRecord.value.status === 2
);

const progressColor = computed(() => {
  if (taskErrorMsg.value) return "var(--el-color-danger)";
  if (isTaskSuccess.value) return "var(--el-color-success)";
  return "var(--el-color-primary)";
});

const progressText = computed(() => {
  if (!lastAsyncTaskRecord.value) return "准备中...";
  const progress = lastAsyncTaskRecord.value.progress || 0;
  if (progress < 10) return "初始化中...";
  if (progress < 20) return "接收需求中...";
  if (progress < 40) return "AI识别中...";
  if (progress < 60) return "识别完成，准备生成...";
  if (progress < 80) return "任务生成中...";
  if (progress < 100) return "即将完成...";
  return "已完成";
});

// 判断是否有任务的计算属性
const hasAnyTasks = computed(() => {
  if (!previewResult.value) return false;
  
  // 多部门模式：检查departments中是否有任务
  if (previewResult.value.multiDepartment && previewResult.value.departments) {
    return previewResult.value.departments.some((dept: any) => dept.tasks && dept.tasks.length > 0);
  }
  
  // 单部门模式：检查tasks字段
  return previewResult.value.tasks && previewResult.value.tasks.length > 0;
});

// 方法
const handlePreview = async () => {
  generating.value = true;
  asyncTaskGenerating.value = true;
  previewResult.value = null;
  expandedTasks.value = [];
  allTasksExpanded.value = false;
  progressDetails.value = [];
  lastAsyncTaskRecord.value = null;
  taskErrorMsg.value = null;
  showSsePanel.value = true; // 自动显示SSE面板

  const payload = {
    taskDescription: formData.taskDescription,
    priority: formData.priority,
    useAIEnhancement: formData.useAIEnhancement,
    autoAssign: formData.autoAssign,
    startTime: formData.timeRange?.[0] || null,
    endTime: formData.timeRange?.[1] || null,
    departmentIds: formData.departmentIds || []
  };

  try {
    const taskId = await service.request({
      url: "admin/sop/ai-task-generator/preview",
      method: "POST",
      data: payload
    });

    const realTaskId = typeof taskId === 'object' && taskId !== null ? taskId.id : taskId;
    if (realTaskId) {
      connectSse(realTaskId);
    } else {
      throw new Error("未能获取任务ID，无法启动预览。");
    }
  } catch (err: any) {
    ElMessage.error(err.message || "预览生成失败");
    generating.value = false;
    asyncTaskGenerating.value = false;
    taskErrorMsg.value = err.message || "预览生成失败";
  }

  // 不切换视图，保持在表单页面，只显示实时交互面板
  showSsePanel.value = true;
};

const handleAsyncGenerate = async () => {
  // --- 自动补全 assignmentType 字段 ---
  if (previewResult.value) {
    if (previewResult.value.multiDepartment && previewResult.value.departments) {
      previewResult.value.departments.forEach(dept => {
        dept.tasks.forEach(task => {
          if (task.assigneeId && !task.assignmentType) {
            task.assignmentType = 'AI'; // 自动补全AI分配类型
          }
          // 未分配的任务 assignmentType 保持 null
        });
      });
    } else if (previewResult.value.tasks) {
      previewResult.value.tasks.forEach(task => {
        if (task.assigneeId && !task.assignmentType) {
          task.assignmentType = 'AI'; // 自动补全AI分配类型
        }
        // 未分配的任务 assignmentType 保持 null
      });
    }
  }
  // --- 补全结束 ---
  if (!lastAsyncTaskRecord.value || !isTaskSuccess.value) {
    ElMessage.warning("请先生成有效的预览并等待其完成");
    return;
  }

  try {
    // 获取预览记录ID
    const previewRecordId = lastAsyncTaskRecord.value.id;
    if (!previewRecordId) {
      ElMessage.error("无法找到预览记录ID，无法基于预览结果生成任务。");
      return;
    }

    resetAsyncPanel();
    await nextTick();
    asyncTaskGenerating.value = true;
    showSsePanel.value = true; // 自动显示SSE面板

    // 使用接受预览的接口，基于预览结果生成正式任务
    const taskId = await service.request({
      url: `admin/sop/ai-task-generator/accept-preview/${previewRecordId}`,
      method: "POST"
    });

    const realTaskId = typeof taskId === 'object' && taskId !== null ? taskId.id : taskId;
    if (realTaskId) {
      connectSse(realTaskId);
    } else {
      throw new Error("未能获取任务ID，无法启动生成。");
    }

    // 不切换视图，保持在表单页面，只显示实时交互面板
    showSsePanel.value = true;
  } catch (err: any) {
    ElMessage.error(err.message || "任务生成失败");
    asyncTaskGenerating.value = false;
    taskErrorMsg.value = err.message || "任务生成失败";
  }
};

// 重新生成（从头开始，不基于预览）
const handleRegenerateFromScratch = async () => {
  if (!lastAsyncTaskRecord.value) {
    ElMessage.warning("没有可用的请求参数");
    return;
  }

  try {
    const paramsJson = lastAsyncTaskRecord.value.params;
    if (!paramsJson) {
      ElMessage.error("无法找到请求参数，无法重新生成。");
      return;
    }
    const payload = JSON.parse(paramsJson);

    resetAsyncPanel();
    await nextTick();
    asyncTaskGenerating.value = true;
    showSsePanel.value = true;

    // 直接调用generate接口，从头开始生成
    const taskId = await service.request({
      url: "admin/sop/ai-task-generator/generate",
      method: "POST",
      data: payload
    });

    const realTaskId = typeof taskId === 'object' && taskId !== null ? taskId.id : taskId;
    if (realTaskId) {
      connectSse(realTaskId);
    } else {
      throw new Error("未能获取任务ID，无法启动重新生成。");
    }

    showSsePanel.value = true;
  } catch (err: any) {
    ElMessage.error(err.message || "重新生成失败");
    asyncTaskGenerating.value = false;
    taskErrorMsg.value = err.message || "重新生成失败";
  }
};

const resetAsyncPanel = () => {
  asyncTaskGenerating.value = false;
  lastAsyncTaskRecord.value = null;
  progressDetails.value = [];
  disconnectSse();
};

const retryAsyncGenerate = () => {
  resetAsyncPanel();
  nextTick(() => {
    handleAsyncGenerate();
  });
};

const backToForm = () => {
  currentView.value = 'form';
  previewResult.value = null;
  generating.value = false;
  asyncTaskGenerating.value = false;
  expandedTasks.value = [];
  allTasksExpanded.value = false;
  progressDetails.value = [];
  lastAsyncTaskRecord.value = null;
  taskErrorMsg.value = null;
  disconnectSse();
};

// 获取场景标签
const getScenarioTags = async () => {
  loadingSuggestions.value = true;
  try {
    const response = await service.request({
      url: "admin/sop/ai-task-generator/scenario-tags",
      method: "GET"
    });

    if (response && Array.isArray(response)) {
      scenarioTags.value = response.map((item: any) => ({
        id: item.id,
        name: item.name,
        code: item.code,
        description: item.description,
        industryName: item.industryName,
        moduleName: item.moduleName,
        totalSteps: item.totalSteps,
        estimatedDuration: item.estimatedDuration,
        difficultyLevel: item.difficultyLevel,
        loading: false
      }));
    } else {
      scenarioTags.value = [];
    }
  } catch (error) {
    console.error("获取场景标签失败:", error);
    scenarioTags.value = [];
  } finally {
    loadingSuggestions.value = false;
  }
};

// 根据难度级别获取标签类型
const getTagType = (scenario: any) => {
  const level = scenario.difficultyLevel || 1;
  const types: Record<number, "primary" | "success" | "warning" | "info" | "danger"> = {
    1: "success",  // 简单
    2: "primary",  // 中等
    3: "warning",  // 困难
    4: "danger",   // 非常困难
    5: "danger"    // 极其困难
  };
  return types[level] || "primary";
};

// 处理场景标签点击
const handleScenarioClick = async (scenario: any) => {
  if (aiSmartSuggestion.value) {
    // AI智能建议模式
    scenario.loading = true;
    aiGenerating.value = true;
    
    try {
      const response = await service.request({
        url: "admin/sop/ai-task-generator/ai-generate-scenario-content",
        method: "POST",
        data: {
          scenarioName: scenario.name,
          scenarioDescription: "我需要创建${scenario.name}的工作流程，优先级高，开始日期:xxxx-xx-xx,结束日期:xxxx-xx-xx",
        }
      });

      if (response && response.aiGeneratedContent) {
        // 处理AI生成的内容，确保格式简洁
        let content = response.aiGeneratedContent;
        
        // 如果AI生成的内容过长，进行简化处理
        if (content.length > 200) {
          // 提取关键信息
          const lines = content.split('\n').filter(line => line.trim());
          const keyInfo = lines.slice(0, 4).join('\n'); // 只保留前4行关键信息
          content = keyInfo;
        }
        
        formData.taskDescription = content;
        
        // 尝试从AI生成的内容中提取优先级信息
        if (scenario.difficultyLevel) {
          const priorityMap = {
            1: 2, 2: 3, 3: 4, 4: 5, 5: 5
          };
          formData.priority = priorityMap[scenario.difficultyLevel] || 3;
        }
        
        ElMessage.success(`AI已生成场景内容：${scenario.name}`);
      } else {
        throw new Error("AI生成内容为空");
      }
    } catch (error) {
      console.error("AI生成场景内容失败:", error);
      ElMessage.error("AI生成失败，已切换为直接填入模式");
      // 失败时回退到直接填入模式
      applyScenarioDirectly(scenario);
    } finally {
      scenario.loading = false;
      aiGenerating.value = false;
    }
  } else {
    // 直接填入模式
    applyScenarioDirectly(scenario);
  }
};

// 直接填入场景内容
const applyScenarioDirectly = (scenario: any) => {
  // 构建简洁的描述文本
  let description = `需求：我要启动${scenario.name}的工作流程`;
  
  if (scenario.description) {
    description += `\n描述：${scenario.description}`;
  }
  
  // 设置预计时间
  if (scenario.estimatedDuration) {
    description += `\n预计耗时：${scenario.estimatedDuration}h`;
  }

  // 设置开始与结束时间 - 使用实际时间范围，格式化为可读的日期时间
  const formatDateTime = (dateStr: string) => {
    if (!dateStr) return '';
    const date = new Date(dateStr);
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const startTime = formData.timeRange?.[0] ? formatDateTime(formData.timeRange[0]) : '待定';
  const endTime = formData.timeRange?.[1] ? formatDateTime(formData.timeRange[1]) : '待定';
  description += `\n时间范围：${startTime} 至 ${endTime}`;
  
  // 设置优先级（基于难度级别）
  if (scenario.difficultyLevel) {
    const priorityMap = {
      1: { level: 2, text: "中低" },
      2: { level: 3, text: "中等" },
      3: { level: 4, text: "中高" },
      4: { level: 5, text: "高" },
      5: { level: 5, text: "紧急" }
    };
    const priority = priorityMap[scenario.difficultyLevel] || { level: 3, text: "中等" };
    description += `\n优先级：${priority.text}`;
    formData.priority = priority.level;
  }

  // 直接设置描述文本
  formData.taskDescription = description;
  
  ElMessage.success(`已应用场景：${scenario.name}`);
};

// 初始化时获取场景标签和分配人员数据
onMounted(() => {
  getScenarioTags();
  // 预先获取分配人员数据，以便在分配对话框中显示筛选选项
  fetchAvailableAssignees();
  fetchDepartments();
});

const goHistory = () => {
  router.push("/sop/ai-task-history");
};

// 多部门预览相关方法
const getTotalTasksCount = () => {
  if (!previewResult.value?.departments) return 0;
  return previewResult.value.departments.reduce((sum, dept) => sum + (dept.tasks?.length || 0), 0);
};

const getAssignedTasksCount = () => {
  if (!previewResult.value?.departments) return 0;
  return previewResult.value.departments.reduce((sum, dept) => {
    return sum + (dept.tasks?.filter((task: any) => task.assigneeName).length || 0);
  }, 0);
};

const getUnassignedTasksCount = () => {
  if (!previewResult.value?.departments) return 0;
  return previewResult.value.departments.reduce((sum, dept) => {
    return sum + (dept.tasks?.filter((task: any) => !task.assigneeName).length || 0);
  }, 0);
};

const getDepartmentName = (dept: any) => {
  return dept.departmentName || dept.name || '未命名部门';
};

const getDepartmentTabLabel = (dept: any) => {
  const taskCount = dept.tasks?.length || 0;
  const assignedCount = dept.tasks?.filter((task: any) => task.assigneeName).length || 0;
  return `${getDepartmentName(dept)} (${assignedCount}/${taskCount})`;
};

// 单部门相关统计方法
const getSingleDepartmentAssignedCount = () => {
  if (!previewResult.value?.departments) return 0;
  return previewResult.value.departments.reduce((sum, dept) => {
    return sum + (dept.tasks?.filter((task: any) => task.assigneeName).length || 0);
  }, 0);
};

const getSingleDepartmentUnassignedCount = () => {
  if (!previewResult.value?.departments) return 0;
  return previewResult.value.departments.reduce((sum, dept) => {
    return sum + (dept.tasks?.filter((task: any) => !task.assigneeName).length || 0);
  }, 0);
};

// 时间格式化方法
const formatTimeRange = (timeRange: any[]) => {
  if (!timeRange || timeRange.length < 2) return '时间待定';
  
  const startTime = new Date(timeRange[0]);
  const endTime = new Date(timeRange[1]);
  
  const formatTime = (date: Date) => {
    return date.toLocaleString('zh-CN', {
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  };
  
  return `${formatTime(startTime)} - ${formatTime(endTime)}`;
};

const getSingleDepartmentTabLabel = () => {
  const taskCount = previewResult.value?.tasks?.length || 0;
  const assignedCount = getSingleDepartmentAssignedCount();
  
  // 获取部门名称
  let departmentName = '任务列表';
  if (formData.departmentIds.length === 1) {
    // 从预览结果中获取部门名称
    if (previewResult.value?.departmentName) {
      departmentName = previewResult.value.departmentName;
    } else {
      // 使用部门ID显示
      departmentName = `部门${formData.departmentIds[0]}`;
    }
  } else if (formData.departmentIds.length > 1) {
    departmentName = `${formData.departmentIds.length}个部门`;
  }
  
  return `${departmentName} (${assignedCount}/${taskCount})`;
};

// 确认生成任务
const confirmGenerate = async () => {
  if (!previewResult.value) return;
  
  generating.value = true;
  try {
    // 这里应该调用确认生成的API
    ElMessage.success("任务生成功能开发中...");
  } catch (error: any) {
    ElMessage.error("生成失败: " + error.message);
  } finally {
    generating.value = false;
  }
};

const toggleTaskDetail = (index: number) => {
  const idx = expandedTasks.value.indexOf(index);
  if (idx > -1) {
    expandedTasks.value.splice(idx, 1);
  } else {
    expandedTasks.value.push(index);
  }
};

const toggleAllTasks = () => {
  if (allTasksExpanded.value) {
    expandedTasks.value = [];
  } else if (previewResult.value && previewResult.value.tasks) {
    expandedTasks.value = previewResult.value.tasks.map((_, index) => index);
  }
  allTasksExpanded.value = !allTasksExpanded.value;
};

const getPriorityLabel = (priority: number) => {
  const labels: Record<number, string> = {
    1: "低",
    2: "中低",
    3: "中",
    4: "高",
    5: "紧急"
  };
  return labels[priority] || "中";
};

const getPriorityType = (priority: number) => {
  const types: Record<number, "primary" | "success" | "warning" | "info" | "danger"> = {
    1: "info",
    2: "success",
    3: "primary",
    4: "warning",
    5: "danger"
  };
  return types[priority] || "primary";
};

const getTimelineItemType = (index: number) => {
  if (index === progressDetails.value.length - 1) {
    if (isTaskSuccess.value) return "success";
    if (taskErrorMsg.value) return "danger";
  }
  return "primary";
};

const getTimelineItemIcon = (index: number) => {
  if (index === progressDetails.value.length - 1) {
    if (isTaskSuccess.value) return Check;
    if (taskErrorMsg.value) return Close;
    return Loading;
  }
  return More;
};

const getTimelineItemColor = (index: number) => {
  if (index === progressDetails.value.length - 1) {
    if (isTaskSuccess.value) return "var(--el-color-success)";
    if (taskErrorMsg.value) return "var(--el-color-danger)";
    return "var(--el-color-primary)";
  }
  return "var(--el-text-color-placeholder)";
};

const getTimelineMessageClass = (message: string) => {
  if (message.includes("AI识别完成")) return "recognition-message";
  if (message.includes("用户需求")) return "requirement-message";
  if (message.includes("失败") || message.includes("错误")) return "error-message";
  return "normal-message";
};

const isRecognitionResult = (message: string) => {
  return message.includes("AI识别完成");
};

// 处理进度详情数据的辅助方法
const getDetailText = (detail: any) => {
  if (typeof detail === 'string') {
    return detail;
  }
  if (typeof detail === 'object' && detail !== null) {
    return detail.message || detail.text || detail.content || JSON.stringify(detail);
  }
  return String(detail);
};

const getDetailTime = (detail: any) => {
  if (typeof detail === 'object' && detail !== null && detail.timestamp) {
    // 如果timestamp是时间字符串格式（如 "13:28:00"），直接返回
    if (typeof detail.timestamp === 'string' && detail.timestamp.includes(':')) {
      return detail.timestamp;
    }
    // 如果是数字时间戳，转换为时间格式
    if (typeof detail.timestamp === 'number') {
      return new Date(detail.timestamp).toLocaleTimeString();
    }
    // 尝试解析字符串时间戳
    const parsedTime = new Date(detail.timestamp);
    if (!isNaN(parsedTime.getTime())) {
      return parsedTime.toLocaleTimeString();
    }
  }
  return new Date().toLocaleTimeString();
};

// 获取消息类型样式
const getMessageClass = (detail: any) => {
  const text = getDetailText(detail);
  if (text.includes('失败') || text.includes('错误')) return 'error';
  if (text.includes('成功') || text.includes('完成')) return 'success';
  if (text.includes('中') || text.includes('进行')) return 'processing';
  return 'info';
};

// 获取指示器样式
const getIndicatorClass = (detail: any) => {
  const text = getDetailText(detail);
  if (text.includes('失败') || text.includes('错误')) return 'error';
  if (text.includes('成功') || text.includes('完成')) return 'success';
  if (text.includes('中') || text.includes('进行')) return 'processing';
  return 'info';
};

// 任务分配对话框状态
const assignmentDialog = reactive({
  visible: false,
  task: null as any,
  taskIndex: -1,
  selectedAssignee: '',
  reason: '',
  loading: false
});

// 分配相关的数据
const availableAssignees = ref<any[]>([]);
const departmentOptions = ref<any[]>([]); // 只用于下拉展示
const roleOptions = ref<any[]>([]); // 只用于下拉展示

// 分配人筛选条件
const assigneeFilter = reactive({
  department: '',
  role: '',
  search: ''
});

// 可选分配人列表
const assigneeLoading = ref(false);

// 获取可用分配人（接口方式，带防抖）
const fetchAvailableAssignees = debounce(async () => {
  assigneeLoading.value = true;
  try {
    const params: any = {};
    if (assigneeFilter.department) {
      const dep = assigneeFilter.department as any;
      params.departmentIds = Array.isArray(dep) ? dep.join(',') : dep;
    }
    if (assigneeFilter.role) {
      const role = assigneeFilter.role as any;
      params.roleIds = Array.isArray(role) ? role.join(',') : role;
    }
    if (assigneeFilter.search) {
      params.keyword = assigneeFilter.search.trim();
    }
    params.status = 1;
    params.excludeAdmin = true;
    params.includeRoles = true;
    params.includeDepartment = true;
    const response = await service.request({
      url: '/admin/base/user-query/available-assignees',
      method: 'GET',
      params,
    });
    availableAssignees.value = response || [];
  } catch (e) {
    availableAssignees.value = [];
  } finally {
    assigneeLoading.value = false;
  }
}, 300);

// 监听筛选条件变化，自动请求接口
watch(
  () => [assigneeFilter.department, assigneeFilter.role, assigneeFilter.search],
  () => {
    fetchAvailableAssignees();
  }
);

// 弹窗打开时初始化一次
watch(
  () => assignmentDialog.visible,
  async (val) => {
    if (val) {
      await Promise.all([fetchDepartments(), fetchRoles()]);
      fetchAvailableAssignees();
    }
  }
);

// 过滤后的分配人列表（已废弃本地过滤，直接用接口返回）
// const filteredAssignees = computed(() => { ... });

// 筛选执行人（触发过滤）
const filterAssignees = () => {
  // 由于使用了computed，过滤会自动触发
  // 这个方法主要用于模板中的事件绑定
};

// 保存预览结果到后端
const savePreviewResult = async () => {
  if (!lastAsyncTaskRecord.value?.id || !previewResult.value) return;
  try {
    await service.request({
      url: `admin/sop/ai-task-generator/save-preview/${lastAsyncTaskRecord.value.id}`,
      method: "POST",
      data: previewResult.value // 直接传递最新的预览数据
    });
  } catch (e) {
    // 可选：提示保存失败
    console.warn('保存预览结果失败', e);
  }
};

// 确认分配更改
const confirmAssignmentChange = async () => {
  if (!assignmentDialog.selectedAssignee) {
    ElMessage.error('请选择执行人');
    return;
  }
  assignmentDialog.loading = true;
  try {
    const selectedAssignee = availableAssignees.value.find(
      assignee => assignee.id === assignmentDialog.selectedAssignee
    );
    if (!selectedAssignee) {
      ElMessage.error('选择的执行人无效');
      return;
    }
    // 更新任务分配信息
    if (previewResult.value) {
      if (previewResult.value.multiDepartment && previewResult.value.departments) {
        // 多部门模式
        const department = previewResult.value.departments.find(dept => 
          dept.tasks.some((_: any, index: number) => index === assignmentDialog.taskIndex)
        );
        if (department) {
          const task = department.tasks[assignmentDialog.taskIndex];
          if (task) {
            task.assigneeId = selectedAssignee.id;
            task.assigneeName = selectedAssignee.name;
            task.assignmentConfidence = 100;
            task.assignmentReason = assignmentDialog.reason || `手动分配给${selectedAssignee.name}`;
            task.assignmentType = 'MANUAL'; // 只要手动分配，强制标记为MANUAL
          }
        }
      } else if (previewResult.value.tasks) {
        // 单部门模式
        const targetTask = previewResult.value.tasks[assignmentDialog.taskIndex];
        if (targetTask) {
          targetTask.assigneeId = selectedAssignee.id;
          targetTask.assigneeName = selectedAssignee.name;
          targetTask.assignmentConfidence = 100;
          targetTask.assignmentReason = assignmentDialog.reason || `手动分配给${selectedAssignee.name}`;
          targetTask.assignmentType = 'MANUAL'; // 只要手动分配，强制标记为MANUAL
        }
      }
    }
    // --- 新增：手动分配后自动保存预览 ---
    await savePreviewResult();
    // --- 结束 ---
    ElMessage.success(`已分配给 ${selectedAssignee.name}`);
    assignmentDialog.visible = false;
  } catch (error) {
    console.error('分配失败:', error);
    ElMessage.error('分配失败');
  } finally {
    assignmentDialog.loading = false;
  }
};

// 选择执行人
const selectAssignee = (assignee: any) => {
  assignmentDialog.selectedAssignee = assignee.id;
};

// 刷新执行人列表
const refreshAssignees = async () => {
  await fetchAvailableAssignees();
};

// 获取工作负载颜色
const getWorkloadColor = (workload: number) => {
  if (workload < 50) return 'success';
  if (workload < 75) return 'warning';
  return 'danger';
};

// 获取置信度类型
const getConfidenceType = (confidence: number) => {
  if (confidence >= 80) return 'success';
  if (confidence >= 60) return 'warning';
  return 'danger';
};

// 获取置信度颜色
const getConfidenceColor = (confidence: number) => {
  if (confidence >= 80) return 'var(--el-color-success)';
  if (confidence >= 60) return 'var(--el-color-warning)';
  return 'var(--el-color-danger)';
};

// 显示分配对话框
const showAssignmentDialog = async (task: any, index: number) => {
  assignmentDialog.task = task;
  assignmentDialog.taskIndex = index;
  assignmentDialog.selectedAssignee = task.assigneeId || '';
  assignmentDialog.reason = '';
  assignmentDialog.visible = true;
  
  // 获取可用的分配人员
  await fetchAvailableAssignees();
};

// 快速分配
const quickAssign = async (task: any, taskIndex: number, candidate: any) => {
  try {
    // 更新任务的分配信息
    if (previewResult.value && previewResult.value.tasks) {
      const targetTask = previewResult.value.tasks[taskIndex];
      if (targetTask) {
        targetTask.assigneeId = candidate.userId || candidate.id;
        targetTask.assigneeName = candidate.userName || candidate.name;
        targetTask.assignmentConfidence = candidate.confidence;
        targetTask.assignmentReason = `快速分配给${candidate.userName || candidate.name}`;
      }
    }
    
    ElMessage.success(`已分配给 ${candidate.userName || candidate.name}`);
  } catch (error) {
    console.error('快速分配失败:', error);
    ElMessage.error('快速分配失败');
  }
};

// 监听SSE任务记录的变化
watch(
  () => lastAsyncTaskRecord.value,
  (record) => {
    if (!record) return;

    // 当任务成功时
    if (record.status === 2) {
      if (record.result) {
        try {
          const raw = JSON.parse(record.result);
          console.log('[原始数据]', raw);
          
          // 根据模式处理结果
          if (raw.mode === 'generate') {
            // 正式生成模式 - 显示生成成功信息，保持在表单页面
            if (raw.multiDepartment && raw.departments) {
              // 多部门生成模式
              const totalTasks = raw.departments.reduce((sum, dept) => sum + (dept.tasks?.length || 0), 0);
              ElMessage.success(`任务生成成功！共为${raw.departments.length}个部门生成${totalTasks}个任务`);
              previewResult.value = {
                ...raw,
                mode: 'generate'
              };
            } else if (raw.tasks && raw.tasks.length > 0) {
              // 单部门生成模式
              previewResult.value = {
                scenario: raw.scenario || {},
                tasks: Array.isArray(raw.tasks) ? raw.tasks : [],
                ...raw
              };
              ElMessage.success(`任务生成成功！已创建${raw.workOrdersCreated || 0}个工单，共${raw.totalTasks || 0}个任务`);
            } else {
              // 没有任务数据，显示统计信息
              ElMessage.success(`任务生成成功！已创建${raw.workOrdersCreated || 0}个工单，共${raw.totalTasks || 0}个任务`);
            }
            // 保持在表单页面，不切换视图
            currentView.value = 'form';
          } else {
            // 预览模式 - 显示预览数据
            if (raw.multiDepartment && raw.departments) {
              // 多部门预览模式 - 保持完整的多部门结构
              previewResult.value = {
                ...raw,
                mode: 'preview'
              };
              console.log('[多部门预览数据]', previewResult.value);
            } else {
              // 单部门预览模式 - 保持向后兼容
              previewResult.value = {
                scenario: raw.scenario || {},
                tasks: Array.isArray(raw.tasks) ? raw.tasks : [],
                ...raw
              };
              console.log('[单部门预览数据]', previewResult.value);
            }
            currentView.value = 'preview';
          }
          
          console.log('[任务结果处理]', { mode: raw.mode, multiDepartment: raw.multiDepartment, result: previewResult.value });
        } catch (e) {
          console.error("解析任务结果失败", e);
          taskErrorMsg.value = "解析任务结果失败，请检查返回的数据结构。";
        } finally {
          asyncTaskGenerating.value = false;
          generating.value = false;
          console.log('[currentView]', currentView.value);
        }
      } else {
        generating.value = false;
        asyncTaskGenerating.value = false;
        ElMessage.success("任务已成功生成！");
        currentView.value = 'form';
      }
    }
    // 当任务失败时
    else if (record.status === 3) {
      generating.value = false;
      asyncTaskGenerating.value = false;
      taskErrorMsg.value = record.failReason || "任务生成失败，未提供具体原因。";
      if (taskErrorMsg.value) {
        ElMessage.error(taskErrorMsg.value);
      }
      currentView.value = 'form';
    }
  },
  { deep: true }
);

// 部门选择相关逻辑
// import { getDepartmentList } from "../utils/department"; // 移除无效和重复导入

const allDepartments = ref<any[]>([]);

// 分配人筛选条件重置时自动刷新
watch(
  () => assignmentDialog.visible,
  async (val) => {
    if (val) {
      await Promise.all([fetchDepartments(), fetchRoles()]);
      fetchAvailableAssignees();
    }
  }
);

// 获取全部部门（全局）
const fetchDepartments = async () => {
  try {
    const res = await service.base.sys.department.list();
    allDepartments.value = res || [];
    departmentOptions.value = res || [];
  } catch (e) {
    allDepartments.value = [];
    departmentOptions.value = [];
  }
};

// 获取全部角色（全局）
const fetchRoles = async () => {
  try {
    const res = await service.base.sys.role.list();
    roleOptions.value = res || [];
  } catch (e) {
    roleOptions.value = [];
  }
};
</script>

<style lang="scss">
/* AI任务生成器样式 */
.ai-task-generator-layout {
  display: flex;
  height: 100vh;
  background-color: var(--el-bg-color-page);
}

.main-content {
  flex: 1;
  transition: all 0.3s ease;
  
  &.has-sidebar {
    margin-right: 400px;
  }
}

.ai-task-generator {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
  background-color: transparent;
  box-sizing: border-box;
}

.header {
  text-align: center;
  margin-bottom: 40px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
  position: relative;
}

.header-actions {
  position: absolute;
  top: 0;
  right: 0;
}

.icon {
  font-size: 48px;
  background: linear-gradient(135deg, var(--el-color-primary), var(--el-color-primary-dark-2));
  border-radius: 50%;
  width: 80px;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  box-shadow: 0 8px 20px var(--el-color-primary-light-7);
  transition: all 0.3s ease;
}

.title-section h2 {
  margin: 0;
  font-size: 28px;
  color: var(--el-text-color-primary);
  font-weight: 600;
}

.title-section p {
  margin: 8px 0 0 0;
  color: var(--el-text-color-regular);
  font-size: 16px;
}

.input-section {
  margin-bottom: 30px;
  background-color: var(--el-bg-color);
  border-radius: 12px;
  padding: 24px;
  border: 1px solid var(--el-border-color-lighter);
  transition: all 0.3s ease;
}

.input-label {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
  font-weight: 500;
  color: var(--el-text-color-primary);
}

.input-textarea {
  margin-bottom: 20px;
}

/* 必填字段样式 */
.required-field {
  margin: 20px 0;
}

.field-label {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
  font-weight: 500;
  color: var(--el-text-color-primary);
}

.required-mark {
  color: var(--el-color-danger);
  font-weight: bold;
}

/* 智能场景建议样式 */
.scenario-suggestions {
  margin: 16px 0;
  padding: 16px;
  background: linear-gradient(135deg, var(--el-fill-color-extra-light) 0%, var(--el-fill-color-light) 100%);
  border-radius: 8px;
  border: 1px solid var(--el-border-color-light);
}

.suggestion-label {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  
  .el-icon {
    margin-right: 8px;
    color: var(--el-color-primary);
  }
}

.ai-switch {
  margin-left: auto;
}

.suggestion-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.scenario-tag {
  cursor: pointer;
  transition: all 0.2s;
}

.scenario-tag:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.ai-generating {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 16px;
  background: var(--el-color-primary-light-9);
  border-radius: 8px;
  margin-top: 12px;
  color: var(--el-color-primary);
  font-size: 14px;
  
  .el-icon {
    animation: rotating 2s linear infinite;
  }
}

.scenario-card {
  border: 1px solid var(--el-border-color-light);
  border-radius: 8px;
  padding: 12px;
  cursor: pointer;
  transition: all 0.2s;
  background: var(--el-bg-color-page);
}

.scenario-card:hover {
  border-color: var(--el-color-primary);
  background: var(--el-color-primary-light-9);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px var(--el-color-primary-light-8);
}

.scenario-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.scenario-tag {
  font-size: 14px;
  font-weight: 500;
}

.match-score {
  font-size: 12px;
  color: var(--el-text-color-secondary);
  background: var(--el-fill-color-light);
  padding: 2px 6px;
  border-radius: 4px;
}

.scenario-details {
  margin-top: 8px;
}

.scenario-description {
  font-size: 13px;
  color: var(--el-text-color-regular);
  margin: 0 0 8px 0;
  line-height: 1.4;
}

.scenario-meta {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: var(--el-text-color-secondary);
}

.meta-item .el-icon {
  font-size: 12px;
}

.no-suggestions {
  text-align: center;
  color: var(--el-text-color-secondary);
  font-size: 13px;
  padding: 16px;
  margin-top: 8px;
}

.suggestion-label .is-loading {
  animation: rotating 2s linear infinite;
}

@keyframes rotating {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.advanced-options {
  background: var(--el-fill-color-lighter);
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  border: 1px solid var(--el-border-color-light);
}

.action-section {
  display: flex;
  gap: 16px;
  justify-content: center;
  margin-bottom: 30px;
}

.generate-btn {
  min-width: 180px;
  height: 48px;
  font-size: 16px;
  font-weight: 600;
}

.advanced-btn {
  height: 48px;
  font-size: 14px;
}

.progress-container {
  text-align: center;
  padding: 40px 20px;
}

.panel-title {
  font-size: 20px;
  color: var(--el-text-color-primary);
  margin-bottom: 30px;
  font-weight: 600;
}

.progress-bar-container {
  margin-bottom: 30px;
}

.progress-text {
  margin-top: 12px;
  font-size: 14px;
  color: var(--el-text-color-regular);
}

.preview-container {
  padding: 20px;
}

.scenario-info {
  margin-bottom: 30px;
  padding: 20px;
  background: var(--el-bg-color);
  border-radius: 8px;
  border: 1px solid var(--el-border-color-light);
}

.scenario-info h4 {
  margin: 0 0 12px 0;
  color: var(--el-text-color-primary);
  font-size: 16px;
}

.tasks-preview {
  margin-bottom: 30px;
}

.tasks-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.tasks-header h4 {
  margin: 0;
  color: var(--el-text-color-primary);
  font-size: 18px;
}

.tasks-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.task-preview-card {
  border: 1px solid var(--el-border-color-light);
  border-radius: 8px;
  transition: all 0.3s ease;
}

.task-preview-card:hover {
  border-color: var(--el-color-primary-light-5);
  box-shadow: 0 2px 8px var(--el-color-primary-light-9);
}

.task-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.task-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
  color: var(--el-text-color-primary);
}

.step-number {
  background: var(--el-color-primary);
  color: white;
  width: 24px;
  height: 24px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 600;
}

.task-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.task-summary {
  margin-top: 12px;
}

.task-description {
  margin: 0 0 12px 0;
  color: var(--el-text-color-regular);
  line-height: 1.5;
}

/* 任务信息标签组 */
.task-info-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  margin-top: 16px;
}

.info-tag {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  background: var(--el-fill-color-extra-light);
  border: 1px solid var(--el-border-color-lighter);
  border-radius: 8px;
  font-size: 13px;
  color: var(--el-text-color-regular);
  transition: all 0.3s ease;
}

.info-tag:hover {
  background: var(--el-fill-color-light);
  border-color: var(--el-border-color-light);
}

.info-tag .el-icon {
  font-size: 16px;
  color: var(--el-text-color-secondary);
}

/* 不同类型的标签颜色 */
.assignee-tag {
  border-color: var(--el-color-primary-light-7);
  background: var(--el-color-primary-light-9);
}

.assignee-tag .el-icon {
  color: var(--el-color-primary);
}

.time-tag {
  border-color: var(--el-color-success-light-7);
  background: var(--el-color-success-light-9);
}

.time-tag .el-icon {
  color: var(--el-color-success);
}

.location-tag {
  border-color: var(--el-color-warning-light-7);
  background: var(--el-color-warning-light-9);
}

.location-tag .el-icon {
  color: var(--el-color-warning);
}

.role-tag {
  border-color: var(--el-color-info-light-7);
  background: var(--el-color-info-light-9);
}

.role-tag .el-icon {
  color: var(--el-color-info);
}

.confidence-tag {
  margin-left: 8px;
}

.unassigned {
  color: var(--el-text-color-placeholder);
  font-style: italic;
}

.assigned-user {
  font-weight: 500;
  color: var(--el-color-primary);
}

/* 操作按钮行 */
.task-actions-row {
  margin-top: 16px;
  padding-top: 12px;
  border-top: 1px solid var(--el-border-color-extra-light);
  display: flex;
  justify-content: flex-end;
}

.task-meta {
  display: flex;
  gap: 16px;
  font-size: 12px;
  color: var(--el-text-color-secondary);
}

.task-details {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid var(--el-border-color-lighter);
}

.task-detail-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
}

.detail-item .label {
  font-weight: 500;
  color: var(--el-text-color-primary);
  min-width: 80px;
}

.detail-item .value {
  color: var(--el-text-color-regular);
}

.assignment-reason {
  margin-top: 4px;
  padding: 4px 8px;
  background: var(--el-color-info-light-9);
  border-radius: 4px;
  font-size: 12px;
  line-height: 1.4;
}

.no-tasks {
  text-align: center;
  padding: 40px 20px;
}

.preview-actions {
  display: flex;
  gap: 16px;
  justify-content: center;
  margin-top: 30px;
}

.generate-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.bottom-actions {
  display: flex;
  gap: 16px;
  justify-content: center;
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid var(--el-border-color-lighter);
}

.error-message {
  text-align: center;
  margin-top: 20px;
}

.retry-button {
  margin-top: 16px;
}

.success-result {
  text-align: center;
  margin-top: 20px;
}

/* SSE交互面板样式 */
.sse-panel {
  position: fixed;
  top: 0;
  right: 0;
  width: 420px;
  height: 100vh;
  background: var(--el-bg-color);
  border-left: 1px solid var(--el-border-color-light);
  box-shadow: -8px 0 32px rgba(0, 0, 0, 0.12);
  z-index: 1000;
  display: flex;
  flex-direction: column;
  transform: translateX(100%);
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(10px);
  
  &.panel-visible {
    transform: translateX(0);
  }
}

/* 深色主题适配 */
.dark .sse-panel {
  background: var(--el-bg-color);
  border-left-color: var(--el-border-color);
  box-shadow: -8px 0 32px rgba(0, 0, 0, 0.3);
}

.sse-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid var(--el-border-color-lighter);
  background: linear-gradient(135deg, var(--el-color-primary) 0%, var(--el-color-primary-dark-2) 100%);
  color: white;
  position: relative;
}

.sse-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, var(--el-color-primary) 0%, var(--el-color-primary-dark-2) 100%);
  backdrop-filter: blur(10px);
  opacity: 0.9;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 12px;
  position: relative;
  z-index: 1;
}

.header-icon {
  font-size: 24px;
  animation: bounce 2s infinite;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

.header-title h4 {
  margin: 0;
  font-size: 18px;
  font-weight: 700;
  color: white;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.header-subtitle {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.8);
  margin-top: 2px;
  display: block;
}

.close-btn {
  position: relative;
  z-index: 2;
  background: rgba(255, 255, 255, 0.2) !important;
  border: 1px solid rgba(255, 255, 255, 0.3) !important;
  color: white !important;
  backdrop-filter: blur(5px);
  transition: all 0.3s ease;
  
  &:hover {
    background: rgba(255, 255, 255, 0.3) !important;
    transform: scale(1.05);
  }
}

.sse-content {
  flex: 1;
  overflow: hidden;
  padding: 20px 24px;
}

.progress-messages {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.progress-message {
  display: flex;
  align-items: flex-start;
  margin-bottom: 8px; /* 减少间距从16px到8px */
  position: relative;
}

.message-indicator {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-right: 12px;
  z-index: 1;
}

.indicator-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: var(--el-color-primary);
  border: 2px solid var(--el-color-primary-light-5);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 4px; /* 减少间距 */
}

.indicator-line {
  width: 2px;
  height: 50px; /* 减少连接线高度 */
  background: linear-gradient(to bottom, var(--el-color-info-light-3), var(--el-color-info-light-7));
  margin-top: 2px; /* 减少顶部间距 */
  position: relative;
}

.message-content {
  flex: 1;
  background: var(--el-bg-color-page);
  border-radius: 8px;
  padding: 8px 12px; /* 减少内边距 */
  border: 1px solid var(--el-border-color-lighter);
  font-size: 13px; /* 稍微减小字体 */
  line-height: 1.4; /* 减少行高 */
  color: var(--el-text-color-regular);
  box-shadow: 0 1px 2px var(--el-box-shadow-light);
}

.message-timestamp {
  font-size: 11px; /* 减小时间戳字体 */
  color: var(--el-text-color-placeholder);
  margin-top: 2px; /* 减少顶部间距 */
}

.message-content-wrapper {
  flex: 1;
  min-width: 0;
}

.message-time {
  font-size: 11px;
  color: var(--el-text-color-secondary);
  margin-bottom: 6px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.message-content::before {
  content: '';
  position: absolute;
  left: -8px;
  top: 16px;
  width: 0;
  height: 0;
  border-top: 6px solid transparent;
  border-bottom: 6px solid transparent;
  border-right: 8px solid var(--el-bg-color-overlay);
}

.message-text {
  position: relative;
  z-index: 1;
}

.message-decoration {
  position: absolute;
  top: 0;
  right: 0;
  width: 4px;
  height: 100%;
  background: linear-gradient(to bottom, var(--el-color-primary), transparent);
  border-radius: 0 12px 12px 0;
  opacity: 0.3;
}

.progress-message.success .message-content {
  background: var(--el-color-success-light-9);
  border-color: var(--el-color-success-light-5);
}

.progress-message.success .message-content::before {
  border-right-color: var(--el-color-success-light-9);
}

.progress-message.success .message-decoration {
  background: linear-gradient(to bottom, var(--el-color-success), transparent);
}

.progress-message.error .message-content {
  background: var(--el-color-error-light-9);
  border-color: var(--el-color-error-light-5);
}

.progress-message.error .message-content::before {
  border-right-color: var(--el-color-error-light-9);
}

.progress-message.error .message-decoration {
  background: linear-gradient(to bottom, var(--el-color-error), transparent);
}

.progress-message.processing .message-content {
  background: var(--el-color-warning-light-9);
  border-color: var(--el-color-warning-light-5);
}

.progress-message.processing .message-content::before {
  border-right-color: var(--el-color-warning-light-9);
}

.progress-message.processing .message-decoration {
  background: linear-gradient(to bottom, var(--el-color-warning), transparent);
}

.progress-message.info .message-content {
  background: var(--el-color-info-light-9);
  border-color: var(--el-color-info-light-5);
}

.progress-message.info .message-content::before {
  border-right-color: var(--el-color-info-light-9);
}

.progress-message.info .message-decoration {
  background: linear-gradient(to bottom, var(--el-color-info), transparent);
}

.current-status {
  padding: 12px;
  background: var(--el-color-primary-light-9);
  border-radius: 6px;
  margin-bottom: 12px;
  border: 1px solid var(--el-color-primary-light-7);
}

.status-message {
  margin-bottom: 8px;
}

.status-progress {
  margin-top: 8px;
}

.empty-messages {
  text-align: center;
  padding: 60px 20px;
  color: var(--el-text-color-secondary);
}

.empty-animation {
  margin-bottom: 20px;
}

.loading-dots {
  display: flex;
  justify-content: center;
  gap: 8px;
  margin-bottom: 16px;
}

.loading-dots .dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--el-color-primary) 0%, var(--el-color-primary-dark-2) 100%);
  animation: loading 1.4s infinite ease-in-out;
  box-shadow: 0 0 10px rgba(64, 158, 255, 0.3);
}

.loading-dots .dot:nth-child(1) {
  animation-delay: -0.32s;
}

.loading-dots .dot:nth-child(2) {
  animation-delay: -0.16s;
}

.loading-dots .dot:nth-child(3) {
  animation-delay: 0s;
}

@keyframes loading {
  0%, 80%, 100% {
    transform: scale(0);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

.empty-messages p {
  margin: 0;
  font-size: 14px;
  color: var(--el-text-color-regular);
  font-weight: 500;
}

/* 表单页面任务生成结果样式 */
.form-result-section {
  margin-top: 30px;
  padding: 20px;
  background: var(--el-fill-color-extra-light);
  border-radius: 8px;
  border: 1px solid var(--el-border-color-light);
}

.result-summary {
  margin-bottom: 20px;
}

.result-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
  flex-wrap: wrap;
}

.result-actions .el-button {
  min-width: 120px;
}

/* 概览紧凑样式 */
.overview-compact {
  margin-bottom: 20px;
  padding: 12px 16px;
  background: linear-gradient(135deg, var(--el-fill-color-extra-light) 0%, var(--el-fill-color-light) 100%);
  border: 1px solid var(--el-border-color-light);
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.overview-line {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 8px;
}

.overview-line:last-child {
  margin-bottom: 0;
}

.overview-title {
  font-weight: 600;
  color: var(--el-text-color-primary);
  font-size: 14px;
  white-space: nowrap;
}

.scenario-title {
  font-weight: 600;
  color: var(--el-text-color-primary);
  font-size: 14px;
  white-space: nowrap;
}

.scenario-name {
  font-weight: 500;
  color: var(--el-text-color-regular);
  margin-right: 8px;
}

.overview-stats-inline {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
  align-items: center;
}

.stat-badge {
  display: inline-flex;
  align-items: center;
  padding: 4px 8px;
  background: var(--el-color-primary-light-9);
  color: var(--el-color-primary);
  border: 1px solid var(--el-color-primary-light-7);
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  white-space: nowrap;
}

.stat-badge.success {
  background: var(--el-color-success-light-9);
  color: var(--el-color-success);
  border-color: var(--el-color-success-light-7);
}

.stat-badge.warning {
  background: var(--el-color-warning-light-9);
  color: var(--el-color-warning);
  border-color: var(--el-color-warning-light-7);
}

.scenario-line {
  display: flex;
  align-items: center;
  gap: 12px;
}

/* 响应式适配 */
@media (max-width: 768px) {
  .overview-stats {
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
  }
  
  .stat-number {
    font-size: 24px;
  }
  
  .stat-label {
    font-size: 12px;
  }
}

/* 暗色主题适配 */
[data-theme="dark"] {
  .ai-task-generator-layout {
    background-color: var(--el-bg-color-page);
  }
  
  .form-result-section {
    background: var(--el-fill-color-extra-light);
    border-color: var(--el-border-color-light);
  }
  
  .sse-panel {
    background: var(--el-bg-color);
    border-left-color: var(--el-border-color);
    box-shadow: -2px 0 8px rgba(0, 0, 0, 0.3);
  }
  
  .sse-header {
    background: var(--el-bg-color-page);
    border-bottom-color: var(--el-border-color);
  }
  
  .timeline-message {
    &.recognition-message {
      background: var(--el-color-primary-light-9);
      border-left-color: var(--el-color-primary);
      color: var(--el-color-primary-light-3);
    }
    
    &.requirement-message {
      background: var(--el-color-info-light-9);
      border-left-color: var(--el-color-info);
      color: var(--el-color-info-light-3);
    }
    
    &.error-message {
      background: var(--el-color-danger-light-9);
      border-left-color: var(--el-color-danger);
      color: var(--el-color-danger-light-3);
    }
  }
}

/* 任务分配对话框样式 */
.assignment-dialog {
  .assignment-content {
    .task-info {
      .task-meta {
        margin-top: 12px;
      }
    }
    .assignment-section {
      .search-filters {
        .filter-row {
          display: flex;
          gap: 16px;
          margin-bottom: 16px;
          .filter-item {
            flex: 1;
            label {
              font-weight: 500;
              margin-bottom: 8px;
            }
            .el-select, .el-input {
              width: 100%;
            }
          }
        }
      }
      .assignee-list {
        .list-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 16px;
          .el-button {
            min-width: 100px;
          }
        }
        .assignee-grid {
          display: flex;
          flex-wrap: wrap;
          gap: 16px;
          .assignee-card {
            flex: 1;
            background: var(--el-bg-color);
            border: 1px solid var(--el-border-color-light);
            border-radius: 8px;
            padding: 16px;
            cursor: pointer;
            transition: all 0.3s;
            &:hover {
              border-color: var(--el-color-primary);
              transform: translateY(-2px);
              box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            }
            .assignee-avatar {
              text-align: center;
              margin-bottom: 16px;
              .el-avatar {
                vertical-align: middle;
              }
            }
            .assignee-info {
              text-align: center;
              .assignee-name, .assignee-role, .assignee-department, .assignee-phone {
                margin-bottom: 8px;
                font-size: 14px;
                color: var(--el-text-color-primary);
              }
            }
            .assignee-stats {
              display: flex;
              justify-content: space-between;
              align-items: center;
              margin-top: 16px;
              .stat-item {
                text-align: center;
                .stat-label {
                  font-size: 12px;
                  color: var(--el-text-color-secondary);
                }
                .el-progress {
                  width: 100px;
                  margin: 0 auto;
                  margin-top: 4px;
                }
                .stat-value {
                  display: block;
                  margin-top: 4px;
                  font-size: 12px;
                  color: var(--el-text-color-regular);
                }
              }
            }
            .assignee-actions {
              text-align: center;
              margin-top: 16px;
              .el-checkbox {
                .el-checkbox__label {
                  font-weight: 500;
                }
              }
            }
          }
        }
        .no-assignees {
          text-align: center;
          margin-top: 16px;
          color: var(--el-text-color-secondary);
        }
      }
      .assignment-reason {
        margin-top: 24px;
        label {
          font-weight: 500;
          margin-bottom: 8px;
        }
        .el-input {
          width: 100%;
        }
      }
    }
  }
}

.assign-btn {
  margin-left: 8px;
  vertical-align: middle;
}
</style> 