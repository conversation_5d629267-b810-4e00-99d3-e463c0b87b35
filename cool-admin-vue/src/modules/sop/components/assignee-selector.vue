<template>
  <!-- 组件根节点现在是对话框本身 -->
    <el-dialog
      v-model="dialogVisible"
      title="选择执行人"
      width="800px"
      :close-on-click-modal="false"
    class="assignee-selector-dialog"
    >
      <!-- 搜索和筛选区域 -->
      <div class="selector-filters">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-input
              v-model="searchKeyword"
              placeholder="搜索姓名或手机号"
              :prefix-icon="Search"
              clearable
              @input="handleSearch"
            />
          </el-col>
          <el-col :span="8">
            <el-select
              v-model="selectedDepartment"
              placeholder="选择部门"
              clearable
              @change="handleDepartmentChange"
              style="width: 100%"
            >
              <el-option
                v-for="dept in departments"
                :key="dept.id"
                :label="dept.name"
                :value="dept.id"
              />
            </el-select>
          </el-col>
          <el-col :span="8">
            <el-select
              v-model="selectedRole"
              placeholder="选择角色"
              clearable
              @change="handleRoleChange"
              style="width: 100%"
            >
              <el-option
                v-for="role in roles"
                :key="role.id"
                :label="role.name"
                :value="role.id"
              />
            </el-select>
          </el-col>
        </el-row>
      </div>

      <!-- 用户列表 -->
      <div class="user-list">
        <el-table
          ref="userTableRef"
          :data="filteredUsers"
          @selection-change="handleSelectionChange"
          :row-class-name="getRowClassName"
          max-height="400"
          v-loading="loading"
        >
          <el-table-column type="selection" width="55" :selectable="isRowSelectable" />
          <el-table-column label="头像" width="80">
            <template #default="{ row }">
              <el-avatar :size="40" :src="row.headImg">
                <span>{{ row.name?.charAt(0) }}</span>
              </el-avatar>
            </template>
          </el-table-column>
          <el-table-column prop="name" label="姓名" width="120" />
          <el-table-column prop="phone" label="手机号" width="130" />
          <el-table-column prop="departmentName" label="部门" width="120" />
          <el-table-column prop="roleName" label="角色" width="100" />
          <el-table-column prop="status" label="状态" width="120">
            <template #default="{ row }">
              <el-tag 
                v-if="isUserDisabled(row)" 
                :type="getDisabledStatusType(row)" 
                size="small"
              >
                {{ getDisabledStatusText(row) }}
              </el-tag>
              <el-tag 
                v-else 
                :type="row.status === 1 ? 'success' : 'danger'" 
                size="small"
              >
                {{ row.status === 1 ? '正常' : '禁用' }}
              </el-tag>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <el-pagination
          v-if="total > 0"
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :total="total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="loadUsers"
          @current-change="loadUsers"
          class="pagination"
        />
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmSelection">
            确定选择 ({{ tempSelectedUsers.length }})
          </el-button>
        </div>
      </template>
    </el-dialog>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted, watch, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { Plus, Search } from '@element-plus/icons-vue'
import { useCool } from '/@/cool'

const { service } = useCool()

// Props & Emits
const props = defineProps<{
  modelValue: any[]
}>()

const emit = defineEmits<{
  'update:modelValue': [value: any[]]
  'confirm': [value: any[]]
}>()

// 响应式数据
const dialogVisible = ref(false)
const loading = ref(false)
const searchKeyword = ref('')
const selectedDepartment = ref<number | ''>('')
const selectedRole = ref<number | ''>('')
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)

const users = ref<any[]>([])
const departments = ref<any[]>([])
const roles = ref<any[]>([])
const tempSelectedUsers = ref<any[]>([])
const userTableRef = ref()

// 计算属性
const selectedAssignees = computed(() => props.modelValue || [])

const filteredUsers = computed(() => {
  console.log('filteredUsers 计算，原始用户数据:', users.value)
  console.log('当前筛选条件:', {
    searchKeyword: searchKeyword.value,
    selectedDepartment: selectedDepartment.value,
    selectedRole: selectedRole.value
  })
  
  // 由于后端已经按角色和部门进行了过滤，这里直接返回users.value
  // 只在前端做关键字的二次过滤（以防后端关键字过滤不够精确）
  let result = users.value

  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    result = result.filter(user => 
      user.name?.toLowerCase().includes(keyword) ||
      user.phone?.includes(keyword)
    )
  }

  // 部门和角色筛选已在后端完成，这里不再进行前端过滤
  // 如果需要前端再次过滤，需要检查正确的字段名
  // 从接口返回可以看到：roleIdList 是数组，departmentId 是数字

  console.log('过滤后的用户数据:', result)
  return result
})

// 禁用的用户信息（包含状态）
const disabledUserInfo = ref<Map<number, string>>(new Map())

// 方法
const openSelector = async (options?: { disabledUserIds?: number[], disabledUserStatuses?: Map<number, string> }) => {
  dialogVisible.value = true
  
  // 设置禁用的用户信息
  if (options?.disabledUserStatuses) {
    disabledUserInfo.value = new Map(options.disabledUserStatuses)
  } else if (options?.disabledUserIds) {
    // 兼容旧的接口，默认状态为"已开始执行"
    disabledUserInfo.value = new Map()
    options.disabledUserIds.forEach(id => {
      disabledUserInfo.value.set(id, 'IN_PROGRESS')
    })
  } else {
    disabledUserInfo.value = new Map()
  }
  
  await Promise.all([
    loadUsers(),
    loadDepartments(),
    loadRoles()
  ])

  // 等待表格渲染完成后设置预选
  await nextTick()
  setPreSelectedUsers()
}

// 设置预选用户
const setPreSelectedUsers = () => {
  if (!userTableRef.value || !selectedAssignees.value.length) {
    console.log('无法设置预选用户:', {
      hasTableRef: !!userTableRef.value,
      selectedCount: selectedAssignees.value.length
    })
    return
  }

  console.log('开始设置预选用户:', {
    selectedAssignees: selectedAssignees.value,
    users: users.value.slice(0, 3) // 只打印前3个用户
  })

  // 清除之前的选择
  userTableRef.value.clearSelection()

  // 预选已有的执行人
  const preSelectedUsers: any[] = []
  users.value.forEach(user => {
    const isSelected = selectedAssignees.value.some(assignee => {
      // 确保ID类型匹配
      const assigneeId = typeof assignee.id === 'string' ? parseInt(assignee.id) : assignee.id
      const userId = typeof user.id === 'string' ? parseInt(user.id) : user.id
      return assigneeId === userId
    })

    if (isSelected) {
      console.log('预选用户:', user.name, user.id)
      userTableRef.value.toggleRowSelection(user, true)
      preSelectedUsers.push(user)
    }
  })

  // 设置临时选择的用户
  tempSelectedUsers.value = preSelectedUsers
}

const loadUsers = async () => {
  try {
    loading.value = true
    const response = await service.base.sys.user.page({
      page: currentPage.value,
      size: pageSize.value,
      status: 1, // 只加载正常状态的用户
      // 关键字（姓名 / 手机号 模糊匹配）
      keyword: searchKeyword.value || undefined,
      // 部门筛选
      departmentIds: selectedDepartment.value !== '' ? [Number(selectedDepartment.value)] : undefined,
      // 角色筛选
      roleIds: selectedRole.value !== '' ? [Number(selectedRole.value)] : undefined
    })

    users.value = response.list || []
    total.value = response.pagination?.total || 0

    // 加载完用户后设置预选
    await nextTick()
    setPreSelectedUsers()
  } catch (error) {
    console.error('加载用户列表失败:', error)
    ElMessage.error('加载用户列表失败')
  } finally {
    loading.value = false
  }
}

const loadDepartments = async () => {
  try {
    const response = await service.base.sys.department.list()
    departments.value = response || []
  } catch (error) {
    console.error('加载部门列表失败:', error)
  }
}

const loadRoles = async () => {
  try {
    const response = await service.base.sys.role.list()
    roles.value = response || []
  } catch (error) {
    console.error('加载角色列表失败:', error)
  }
}

const handleSearch = () => {
  currentPage.value = 1
  loadUsers()
}

const handleDepartmentChange = () => {
  currentPage.value = 1
  loadUsers()
}

const handleRoleChange = () => {
  currentPage.value = 1
  loadUsers()
}

// 判断用户是否被禁用
const isUserDisabled = (row: any) => {
  const userId = typeof row.id === 'string' ? parseInt(row.id) : row.id
  return disabledUserInfo.value.has(userId)
}

// 判断行是否可选择
const isRowSelectable = (row: any) => {
  return !isUserDisabled(row)
}

// 获取行的CSS类名
const getRowClassName = ({ row }: { row: any }) => {
  const userId = typeof row.id === 'string' ? parseInt(row.id) : row.id
  return disabledUserInfo.value.has(userId) ? 'disabled-row' : ''
}

// 获取禁用状态的类型
const getDisabledStatusType = (row: any) => {
  const userId = typeof row.id === 'string' ? parseInt(row.id) : row.id
  const status = disabledUserInfo.value.get(userId)
  switch (status) {
    case 'ASSIGNED':
      return 'info'
    case 'ACCEPTED':
      return 'warning'
    case 'IN_PROGRESS':
      return 'primary'
    case 'COMPLETED':
      return 'success'
    case 'CANCELLED':
    case 'REJECTED':
      return 'danger'
    default:
      return 'info'
  }
}

// 获取禁用状态的文本
const getDisabledStatusText = (row: any) => {
  const userId = typeof row.id === 'string' ? parseInt(row.id) : row.id
  const status = disabledUserInfo.value.get(userId)
  switch (status) {
    case 'ASSIGNED':
      return '已分配'
    case 'ACCEPTED':
      return '已接受'
    case 'IN_PROGRESS':
      return '执行中'
    case 'COMPLETED':
      return '已完成'
    case 'CANCELLED':
      return '已取消'
    case 'REJECTED':
      return '已拒绝'
    default:
      return '不可调整'
  }
}

const handleSelectionChange = (selection: any[]) => {
  console.log('选择变更:', selection.map(u => ({ id: u.id, name: u.name })))
  tempSelectedUsers.value = selection
}

const confirmSelection = () => {
  // 直接使用当前选择的用户作为新的执行人列表
  const newAssignees = tempSelectedUsers.value.map(user => ({
    id: user.id,
    name: user.name,
    phone: user.phone,
    headImg: user.headImg,
    departmentName: user.departmentName,
    roleName: user.roleName
  }))

  console.log('确认选择:', newAssignees)

  // 触发选择确认事件
  emit('update:modelValue', newAssignees)
  emit('confirm', newAssignees)

  dialogVisible.value = false
  tempSelectedUsers.value = []

  ElMessage.success(`已选择 ${newAssignees.length} 个执行人`)
}

// 监听对话框打开状态
watch(dialogVisible, async (newVal) => {
  if (newVal) {
    console.log('对话框打开，开始加载数据...')
    // 对话框打开时自动加载数据
    await Promise.all([
      loadUsers(),
      loadDepartments(),
      loadRoles()
    ])

    console.log('数据加载完成，设置预选用户...')
    // 等待表格渲染完成后设置预选
    await nextTick()
    setPreSelectedUsers()
  } else {
    // 对话框关闭时重置临时选择
    tempSelectedUsers.value = []
  }
})

onMounted(() => {
  // 组件挂载时可以预加载一些数据
})

// 暴露方法给父组件
defineExpose({
  openSelector
})
</script>

<style lang="scss" scoped>
.assignee-selector-dialog .selector-filters {
  margin-bottom: 20px;
}

.assignee-selector-dialog .user-list {
  margin-bottom: 20px;
}

.assignee-selector-dialog .pagination {
    margin-top: 20px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

// 禁用行的样式
:deep(.disabled-row) {
  background-color: var(--el-fill-color-light) !important;
  color: var(--el-text-color-placeholder) !important;
  opacity: 0.7;
  
  td {
    background-color: var(--el-fill-color-light) !important;
    color: var(--el-text-color-placeholder) !important;
  }
  
  .el-checkbox {
    pointer-events: none;
    opacity: 0.5;
  }
}

:deep(.disabled-row:hover) {
  background-color: var(--el-fill-color-light) !important;
  
  td {
    background-color: var(--el-fill-color-light) !important;
  }
}

// 确保在暗色主题下也能正常显示
html.dark {
  .assignee-tag .tag-role {
    background: var(--el-fill-color-dark);
    color: var(--el-text-color-regular);
  }
  
  :deep(.disabled-row) {
    background-color: var(--el-fill-color-darker) !important;
    color: var(--el-text-color-placeholder) !important;
    
    td {
      background-color: var(--el-fill-color-darker) !important;
      color: var(--el-text-color-placeholder) !important;
    }
  }
  
  :deep(.disabled-row:hover) {
    background-color: var(--el-fill-color-darker) !important;
    
    td {
      background-color: var(--el-fill-color-darker) !important;
    }
  }
}
</style>
