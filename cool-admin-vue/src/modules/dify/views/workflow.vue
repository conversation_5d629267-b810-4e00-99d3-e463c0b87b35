<template>
	<cl-crud ref="Crud">
		<cl-row>
			<!-- 刷新按钮 -->
			<cl-refresh-btn />
			<!-- 新增按钮 -->
			<cl-add-btn />
			<!-- 删除按钮 -->
			<cl-multi-delete-btn />
			<cl-flex1 />
			<!-- 条件搜索 -->
			<cl-search ref="Search" />
		</cl-row>

		<cl-row>
			<!-- 数据表格 -->
			<cl-table ref="Table" />
		</cl-row>

		<cl-row>
			<cl-flex1 />
			<!-- 分页控件 -->
			<cl-pagination />
		</cl-row>

		<!-- 新增、编辑 -->
		<cl-upsert ref="Upsert" />
	</cl-crud>
</template>

<script lang="ts" setup>
defineOptions({
	name: "dify-workflow-admin-dify-workflow",
});

import { useCrud, useTable, useUpsert, useSearch } from "@cool-vue/crud";
import { useCool } from "/@/cool";
import { useI18n } from "vue-i18n";

const { service } = useCool();
const { t } = useI18n();

// 1. 状态字典
const statusDict = [
	{ label: "启用", value: 1, type: "success" },
	{ label: "禁用", value: 0, type: "danger" }
];

// cl-upsert
const Upsert = useUpsert({
	items: [
		{
			label: t("英文名称"),
			prop: "name",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
		},
		{
			label: t("描述"),
			prop: "description",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
		},
		{
			label: t("url"),
			prop: "url",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
		},
		{
			label: t("apiKey"),
			prop: "apiKey",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
		},
		{
			label: t("入参"),
			prop: "inputParams",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
		},
		{
			label: t("输出说明"),
			prop: "outputDesc",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
		},
		{
			label: t("启用状态"),
			prop: "status",
			component: {
				name: "cl-select",
				props: {
					options: statusDict
				}
			},
			span: 12
		},
	],
});

// cl-table
const Table = useTable({
	columns: [
		{ type: "selection" },
		{ label: t("英文名称"), prop: "name", minWidth: 120 },
		{ label: t("描述"), prop: "description", minWidth: 120 },
		{ label: t("地址"), prop: "url", minWidth: 120 },
		{ label: t("API"), prop: "apiKey", minWidth: 120 },
		{ label: t("入参"), prop: "inputParams", minWidth: 120 },
		{ label: t("输出说明"), prop: "outputDesc", minWidth: 120 },
		{
			label: t("启用状态"),
			prop: "status",
			dict: statusDict,
			dictColor: true,
			minWidth: 120
		},
		{
			label: t("创建时间"),
			prop: "createTime",
			minWidth: 170,
			sortable: "desc",
			component: { name: "cl-date-text" },
		},
		{
			label: t("更新时间"),
			prop: "updateTime",
			minWidth: 170,
			sortable: "custom",
			component: { name: "cl-date-text" },
		},
		{ type: "op", buttons: ["edit", "delete"] },
	],
});

// cl-search
const Search = useSearch();

// cl-crud
const Crud = useCrud(
	{
		service: service.dify.workflow,
	},
	(app) => {
		app.refresh();
	},
);

// 刷新
function refresh(params?: any) {
	Crud.value?.refresh(params);
}
</script>
