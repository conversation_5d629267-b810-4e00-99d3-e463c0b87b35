import { useStore } from '../store';
import { isObject } from 'lodash-es';

function parse(value: any) {
	const { menu } = useStore();

	if (typeof value == 'string') {
		return value ? menu.perms.some((e: any) => e.includes(value.replace(/\s/g, ''))) : false;
	} else {
		return Boolean(value);
	}
}

export function checkPerm(value: string | { or?: string[]; and?: string[] }) {
	if (!value) {
		return false;
	}

	if (isObject(value)) {
		if (value.or) {
			return value.or.some(parse);
		}

		if (value.and) {
			return value.and.some((e: any) => !parse(e)) ? false : true;
		}
	}

	return parse(value);
}

/**
 * 双维度权限检查
 * @param permission 权限点
 * @param options 选项
 */
export function checkOrganizationPerm(
	permission: string | { or?: string[]; and?: string[] },
	options?: {
		mode?: string; // 指定组织模式
		projectId?: number; // 项目ID（项目模式下使用）
		fallback?: boolean; // 是否回退到基础权限检查
	}
) {
	// 如果强制回退或无法获取组织模块，使用基础权限检查
	if (options?.fallback) {
		return checkPerm(permission);
	}

	// 安全地获取组织store
	let organizationStore = null;
	try {
		if (typeof window !== 'undefined' && window.__ORGANIZATION_STORE__) {
			organizationStore = window.__ORGANIZATION_STORE__;
		}
	} catch (error) {
		// 静默处理错误，避免控制台警告
	}

	// 如果没有组织模块，回退到基础权限检查
	if (!organizationStore) {
		return checkPerm(permission);
	}

	// 检查组织模式权限
	let currentMode = null;
	try {
		currentMode = options?.mode || organizationStore.currentMode;
	} catch (error) {
		// 静默处理错误，回退到基础权限检查
		return checkPerm(permission);
	}

	// 如果是项目模式且需要项目上下文
	if (currentMode === 'PROJECT' && options?.projectId) {
		// 这里可以添加项目级权限检查逻辑
		// 暂时使用基础权限检查
		return checkPerm(permission);
	}

	// 使用基础权限检查
	return checkPerm(permission);
}
