<template>
	<cl-crud ref="Crud">
		<cl-row>
			<cl-refresh-btn />
			<cl-flex1 />
			<cl-search-key />
		</cl-row>

		<cl-row>
			<cl-table ref="Table" />
		</cl-row>

		<cl-row>
			<cl-flex1 />
			<cl-pagination />
		</cl-row>
	</cl-crud>
</template>

<script setup lang="ts">
import { useCrud, useTable } from "@cool-vue/crud";
import { useCool } from "/@/cool";
import { h } from "vue";
import TaskPackageList from "../components/task-package-list.vue";

defineOptions({
	name: "work-order"
});

const { service } = useCool();

const Crud = useCrud(
	{
		// 关联服务，很可能需要等后端EPS生成后进行调整
		service: service.work.order
	},
	(app) => {
		app.refresh();
	}
);

const Table = useTable({
	columns: [
		{
			type: "expand",
			width: 60,
			component: {
				render: (scope) => {
					// 渲染任务包子组件
					return h(TaskPackageList, { workOrderId: scope.row.id });
				}
			}
		},
		{
			label: "工单名称",
			prop: "name",
			minWidth: 250
		},
		{
			label: "状态",
			prop: "status",
			minWidth: 120
		},
		{
			label: "创建时间",
			prop: "createTime",
			minWidth: 170,
			sortable: "custom"
		},
		{
			label: "更新时间",
			prop: "updateTime",
			minWidth: 170,
			sortable: "custom"
		}
	]
});
</script> 