<template>
	<div class="task-list">
		<div v-if="!tasks || tasks.length === 0">
			<el-empty description="暂无任务" :image-size="80" />
		</div>
		<el-table :data="tasks" border size="small" v-else>
			<el-table-column prop="name" label="任务名称" min-width="200" />
			<el-table-column
				prop="description"
				label="描述"
				min-width="250"
				show-overflow-tooltip
			/>
			<el-table-column prop="status" label="状态" min-width="100" />
			<el-table-column prop="executorName" label="执行人" min-width="120" />
			<el-table-column prop="createTime" label="创建时间" min-width="160" />
		</el-table>
	</div>
</template>

<script setup lang="ts">
defineProps({
	tasks: {
		type: Array,
		default: () => []
	}
});
</script>

<style lang="scss" scoped>
.task-list {
	padding: 10px 20px;
	background-color: #fafafa;
}

.dark {
	.task-list {
		background-color: var(--el-bg-color-overlay);
	}
}
</style> 