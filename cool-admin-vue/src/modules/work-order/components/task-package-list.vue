<template>
	<div class="task-package-list">
		<el-skeleton :rows="3" animated v-if="loading" />

		<div v-else-if="list.length > 0">
			<el-table :data="list" border size="small">
				<el-table-column type="expand" width="55">
					<template #default="props">
						<task-list :tasks="props.row.tasks" />
					</template>
				</el-table-column>
				<el-table-column prop="name" label="任务包名称" min-width="200" />
				<el-table-column
					prop="description"
					label="描述"
					min-width="250"
					show-overflow-tooltip
				/>
				<el-table-column prop="status" label="状态" min-width="100" />
				<el-table-column prop="createTime" label="创建时间" min-width="160" />
			</el-table>
		</div>

		<el-empty description="暂无任务包" v-else />
	</div>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import { useCool } from "/@/cool";
import type { ElTable } from "element-plus";
import TaskList from "./task-list.vue";

const props = defineProps({
	workOrderId: {
		type: Number,
		required: true
	}
});

const { service } = useCool();
const loading = ref(true);
const list = ref<any[]>([]);

onMounted(() => {
	// 使用 'info' 服务，它应映射到后端的 detail 端点
	service.work.order
		.info({ id: props.workOrderId })
		.then((res: any) => {
			list.value = res.taskPackages || [];
		})
		.finally(() => {
			loading.value = false;
		});
});
</script>

<style lang="scss" scoped>
.task-package-list {
	padding: 10px 50px;
	background-color: var(--el-bg-color-page);
}
</style> 