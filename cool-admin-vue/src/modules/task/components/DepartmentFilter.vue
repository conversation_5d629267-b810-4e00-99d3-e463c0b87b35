<template>
  <div class="department-filter">
    <el-select
      v-model="selectedDepartments"
      multiple
      placeholder="选择部门筛选"
      collapse-tags
      collapse-tags-tooltip
      clearable
      filterable
      @change="handleChange"
      :max-collapse-tags="2"
    >
      <template #prefix>
        <el-icon><OfficeBuilding /></el-icon>
      </template>
      
      <!-- 快捷选项 -->
      <el-option-group label="🔥 快捷选项" v-if="showQuickOptions">
        <el-option
          label="我的部门"
          :value="MY_DEPARTMENT_KEY"
        >
          <div class="quick-option">
            <el-icon><UserFilled /></el-icon>
            <span>我的部门</span>
            <el-tag type="success" size="small">{{ myDepartmentTaskCount }}</el-tag>
          </div>
        </el-option>
        
        <el-option
          label="全部有权限部门"
          :value="ALL_DEPARTMENTS_KEY"
        >
          <div class="quick-option">
            <el-icon><Grid /></el-icon>
            <span>全部有权限部门</span>
            <el-tag type="primary" size="small">{{ totalTaskCount }}</el-tag>
          </div>
        </el-option>
      </el-option-group>
      
      <!-- 具体部门列表 -->
      <el-option-group label="🏢 具体部门">
        <el-option
          v-for="dept in sortedDepartments"
          :key="dept.id"
          :label="dept.name"
          :value="dept.id"
        >
          <div class="department-option">
            <div class="dept-info">
              <span class="dept-name">{{ dept.name }}</span>
              <span v-if="dept.parentName" class="dept-path">{{ dept.parentName }}</span>
            </div>
            <div class="dept-stats">
              <el-tag
                :type="getDepartmentTagType(dept)"
                size="small"
              >
                {{ getDepartmentTaskCount(dept.id) }}
              </el-tag>
              <el-icon 
                v-if="dept.permissionLevel === 'full'" 
                color="#67c23a"
                title="完全权限"
              >
                <SuccessFilled />
              </el-icon>
              <el-icon 
                v-else-if="dept.permissionLevel === 'department'" 
                color="#409eff"
                title="部门权限"
              >
                <UserFilled />
              </el-icon>
            </div>
          </div>
        </el-option>
      </el-option-group>
    </el-select>
    
    <!-- 已选部门展示 -->
    <div v-if="displaySelectedDepartments.length > 0" class="selected-departments">
      <span class="selected-label">已选：</span>
      <el-tag
        v-for="dept in displaySelectedDepartments"
        :key="dept.id"
        :type="getDepartmentTagType(dept)"
        size="small"
        closable
        @close="removeDepartment(dept.id)"
        class="selected-tag"
      >
        {{ dept.name }} ({{ getDepartmentTaskCount(dept.id) }})
      </el-tag>
      <el-button
        type="text"
        size="small"
        @click="clearAll"
      >
        清空
      </el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { usePermission } from '../hooks/usePermission'
import { useUserStore } from '/@/modules/base/store/user'

const props = defineProps({
  modelValue: {
    type: Array,
    default: () => []
  },
  showQuickOptions: {
    type: Boolean,
    default: true
  }
})

const emit = defineEmits(['update:modelValue', 'change'])

const userStore = useUserStore()
const { 
  userDepartments, 
  loadUserDepartments,
  loadDepartmentTaskCounts,
  getDepartmentPermissionLevel,
  getDepartmentTaskCount,
  currentUserDepartment
} = usePermission()

// 特殊键值
const MY_DEPARTMENT_KEY = 'MY_DEPARTMENT'
const ALL_DEPARTMENTS_KEY = 'ALL_DEPARTMENTS'

const selectedDepartments = ref([...props.modelValue])

// 计算属性
const sortedDepartments = computed(() => {
  return userDepartments.value
    .map(dept => ({
      ...dept,
      permissionLevel: getDepartmentPermissionLevel(dept),
      taskCount: getDepartmentTaskCount(dept.id)
    }))
    .sort((a, b) => {
      // 按任务数量排序，任务多的排前面
      return (b.taskCount || 0) - (a.taskCount || 0)
    })
})

const myDepartmentTaskCount = computed(() => {
  return currentUserDepartment.value 
    ? getDepartmentTaskCount(currentUserDepartment.value.id)
    : 0
})

const totalTaskCount = computed(() => {
  return userDepartments.value.reduce((sum, dept) => {
    return sum + getDepartmentTaskCount(dept.id)
  }, 0)
})

const displaySelectedDepartments = computed(() => {
  const result = []
  
  for (const value of selectedDepartments.value) {
    if (value === MY_DEPARTMENT_KEY && currentUserDepartment.value) {
      result.push(currentUserDepartment.value)
    } else if (value === ALL_DEPARTMENTS_KEY) {
      return userDepartments.value.slice(0, 3) // 最多显示3个
    } else if (typeof value === 'number') {
      const dept = userDepartments.value.find(d => d.id === value)
      if (dept) {
        result.push(dept)
      }
    }
  }
  
  return result
})

// 方法
const handleChange = (values) => {
  selectedDepartments.value = values
  
  // 处理快捷选项
  let actualDepartmentIds = []
  
  if (values.includes(ALL_DEPARTMENTS_KEY)) {
    // 如果选择了全部部门，返回所有部门ID
    actualDepartmentIds = userDepartments.value.map(d => d.id)
  } else {
    // 处理其他选项
    for (const value of values) {
      if (value === MY_DEPARTMENT_KEY && currentUserDepartment.value) {
        actualDepartmentIds.push(currentUserDepartment.value.id)
      } else if (typeof value === 'number') {
        actualDepartmentIds.push(value)
      }
    }
  }
  
  // 去重
  actualDepartmentIds = [...new Set(actualDepartmentIds)]
  
  emit('update:modelValue', actualDepartmentIds)
  emit('change', actualDepartmentIds)
}

const removeDepartment = (departmentId) => {
  // 移除特定部门
  const newValues = selectedDepartments.value.filter(value => {
    if (value === MY_DEPARTMENT_KEY) {
      return currentUserDepartment.value?.id !== departmentId
    }
    if (value === ALL_DEPARTMENTS_KEY) {
      return false // 移除全部选择
    }
    return value !== departmentId
  })
  
  selectedDepartments.value = newValues
  handleChange(newValues)
}

const clearAll = () => {
  selectedDepartments.value = []
  handleChange([])
}

const getDepartmentTagType = (dept) => {
  const level = getDepartmentPermissionLevel(dept)
  switch (level) {
    case 'full': return 'success'
    case 'department': return 'primary'
    case 'limited': return 'warning'
    default: return 'info'
  }
}

// 监听外部值变化
watch(() => props.modelValue, (newValue) => {
  // 根据实际部门ID反推选择项
  const mappedValues = []
  
  if (newValue.length === userDepartments.value.length) {
    // 如果选中了所有部门，设置为全部部门选项
    mappedValues.push(ALL_DEPARTMENTS_KEY)
  } else {
    // 检查是否包含当前用户部门
    if (currentUserDepartment.value && newValue.includes(currentUserDepartment.value.id)) {
      mappedValues.push(MY_DEPARTMENT_KEY)
    }
    
    // 添加其他具体选择的部门
    for (const deptId of newValue) {
      if (deptId !== currentUserDepartment.value?.id) {
        mappedValues.push(deptId)
      }
    }
  }
  
  selectedDepartments.value = mappedValues
}, { deep: true })

// 初始化
onMounted(async () => {
  await loadUserDepartments()
  await loadDepartmentTaskCounts()
})
</script>

<style scoped>
.department-filter {
  width: 100%;
}

.quick-option {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
}

.department-option {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.dept-info {
  flex: 1;
  min-width: 0;
}

.dept-name {
  font-weight: 500;
  color: #333;
}

.dept-path {
  font-size: 12px;
  color: #666;
  display: block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-top: 2px;
}

.dept-stats {
  display: flex;
  align-items: center;
  gap: 4px;
}

.selected-departments {
  margin-top: 8px;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 6px;
}

.selected-label {
  color: #666;
  font-size: 13px;
  margin-right: 4px;
}

.selected-tag {
  display: flex;
  align-items: center;
  gap: 4px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .selected-departments {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .dept-name {
    max-width: 120px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}
</style> 