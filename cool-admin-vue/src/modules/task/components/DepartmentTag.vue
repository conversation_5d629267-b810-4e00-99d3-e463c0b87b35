<template>
  <div class="department-tag-wrapper">
    <el-tag
      :type="tagType"
      :size="size"
      :class="tagClass"
      @click="handleClick"
    >
      <el-icon v-if="showIcon" class="tag-icon">
        <OfficeBuilding />
      </el-icon>
      <span class="tag-text">{{ displayName }}</span>
      <el-icon v-if="showPermissionIcon" class="permission-icon" :color="permissionColor">
        <component :is="permissionIcon" />
      </el-icon>
    </el-tag>
    
    <el-tooltip v-if="showTooltip" :content="tooltipContent" placement="top">
      <el-icon class="info-icon">
        <InfoFilled />
      </el-icon>
    </el-tooltip>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, onMounted, watch } from 'vue'
import { usePermission } from '../hooks/usePermission'

const props = defineProps({
  departmentId: {
    type: [Number, String],
    required: true
  },
  departmentName: {
    type: String,
    default: ''
  },
  size: {
    type: String,
    default: 'default',
    validator: (value) => ['large', 'default', 'small'].includes(value)
  },
  clickable: {
    type: Boolean,
    default: false
  },
  showIcon: {
    type: Boolean,
    default: true
  },
  showPermissionStatus: {
    type: Boolean,
    default: true
  },
  showTooltip: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['click'])

const { getDepartmentPermissionLevel, getDepartmentName } = usePermission()

// 响应式的部门名称
const fetchedDepartmentName = ref('')

const displayName = computed(() => {
  if (props.departmentName) {
    return props.departmentName
  }
  if (fetchedDepartmentName.value) {
    return fetchedDepartmentName.value
  }
  return '加载中...'
})

// 异步获取部门名称
const loadDepartmentName = async () => {
  if (!props.departmentName && props.departmentId) {
    try {
      const name = await getDepartmentName(props.departmentId)
      fetchedDepartmentName.value = name
    } catch (error) {
      console.warn('获取部门名称失败:', error)
      fetchedDepartmentName.value = '未知部门'
    }
  }
}

// 监听departmentId变化
watch(() => props.departmentId, () => {
  if (props.departmentId) {
    loadDepartmentName()
  }
}, { immediate: true })

onMounted(() => {
  if (props.departmentId && !props.departmentName) {
    loadDepartmentName()
  }
})

const permissionLevel = computed(() => {
  return getDepartmentPermissionLevel({ id: props.departmentId })
})

const tagType = computed(() => {
  switch (permissionLevel.value) {
    case 'full': return 'success'
    case 'department': return 'primary'
    case 'limited': return 'warning'
    case 'none': return 'info'
    default: return 'info'
  }
})

const tagClass = computed(() => {
  return {
    'department-tag': true,
    'clickable': props.clickable,
    [`permission-${permissionLevel.value}`]: true
  }
})

const showPermissionIcon = computed(() => {
  return props.showPermissionStatus && permissionLevel.value !== 'none'
})

const permissionIcon = computed(() => {
  switch (permissionLevel.value) {
    case 'full': return 'SuccessFilled'
    case 'department': return 'UserFilled'
    case 'limited': return 'WarningFilled'
    case 'none': return 'CircleCloseFilled'
    default: return 'QuestionFilled'
  }
})

const permissionColor = computed(() => {
  switch (permissionLevel.value) {
    case 'full': return '#67c23a'
    case 'department': return '#409eff'
    case 'limited': return '#e6a23c'
    case 'none': return '#909399'
    default: return '#909399'
  }
})

const tooltipContent = computed(() => {
  const permissionText = {
    'full': '完全权限 - 可管理该部门及其子部门的所有任务',
    'department': '部门权限 - 可管理该部门的任务',
    'limited': '受限权限 - 仅可查看部分任务',
    'none': '无权限 - 无法访问该部门的任务'
  }
  return `${displayName.value} - ${permissionText[permissionLevel.value] || '未知权限'}`
})

const handleClick = () => {
  if (props.clickable) {
    emit('click', {
      departmentId: props.departmentId,
      departmentName: displayName.value,
      permissionLevel: permissionLevel.value
    })
  }
}
</script>

<style scoped>
.department-tag-wrapper {
  display: inline-flex;
  align-items: center;
  gap: 4px;
}

.department-tag {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.department-tag.clickable {
  cursor: pointer;
}

.department-tag.clickable:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.tag-icon {
  font-size: 12px;
}

.tag-text {
  font-weight: 500;
}

.permission-icon {
  font-size: 10px;
}

.info-icon {
  font-size: 14px;
  color: #909399;
  cursor: help;
}

/* 权限级别样式 */
.permission-full {
  border-left: 3px solid #67c23a;
}

.permission-department {
  border-left: 3px solid #409eff;
}

.permission-limited {
  border-left: 3px solid #e6a23c;
}

.permission-none {
  border-left: 3px solid #909399;
  opacity: 0.7;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .tag-text {
    max-width: 80px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}
</style> 