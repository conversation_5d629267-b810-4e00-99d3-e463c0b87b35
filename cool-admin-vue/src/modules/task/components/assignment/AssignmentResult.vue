<template>
  <div class="assignment-result">
    <!-- 分配成功的任务 -->
    <div v-if="result.assignments && result.assignments.length > 0" class="success-section">
      <div class="section-header">
        <el-icon class="success-icon"><SuccessFilled /></el-icon>
        <h3>分配成功 ({{ result.assignments.length }}个任务)</h3>
      </div>
      
      <div class="assignment-list">
        <div
          v-for="assignment in result.assignments"
          :key="assignment.taskId"
          class="assignment-item success"
        >
          <div class="assignment-header">
            <div class="task-info">
              <h4>{{ assignment.taskName }}</h4>
              <el-tag v-if="assignment.aiGenerated" type="primary" size="small">
                <el-icon><Setting /></el-icon>
                AI推荐
              </el-tag>
              <el-tag v-else type="info" size="small">手动分配</el-tag>
            </div>
            <div class="confidence-badge">
              <el-progress
                type="circle"
                :percentage="assignment.confidence"
                :width="50"
                :stroke-width="6"
                :color="getConfidenceColor(assignment.confidence)"
              />
              <span class="confidence-text">置信度</span>
            </div>
          </div>

          <div class="assignees-section">
            <h5>执行人员:</h5>
            <div class="assignees-list">
              <div
                v-for="assignee in assignment.assignees"
                :key="assignee.userId"
                class="assignee-card"
              >
                <div class="assignee-info">
                  <div class="assignee-name">
                    <el-avatar :size="32" :src="getAvatarUrl(assignee.userId)">
                      {{ assignee.userName.charAt(0) }}
                    </el-avatar>
                    <div class="name-role">
                      <span class="name">{{ assignee.userName }}</span>
                      <span class="role">{{ assignee.userRole }}</span>
                    </div>
                  </div>
                  <div class="assignee-stats">
                    <el-tag
                      :type="assignee.taskRole === 'primary' ? 'primary' : 'info'"
                      size="small"
                    >
                      {{ assignee.taskRole === 'primary' ? '主要负责人' : '协助人员' }}
                    </el-tag>
                    <span class="workload">负载: {{ assignee.currentWorkload }}%</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="reason-section">
            <h5>分配理由:</h5>
            <div class="reason-content">
              <pre>{{ assignment.reason }}</pre>
            </div>
          </div>

          <div class="assignment-actions">
            <el-button size="small" @click="confirmAssignment(assignment)">
              <el-icon><Check /></el-icon>
              确认分配
            </el-button>
            <el-button size="small" type="warning" @click="reassignTask(assignment)">
              <el-icon><Refresh /></el-icon>
              重新分配
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 分配失败的任务 -->
    <div v-if="result.failedTasks && result.failedTasks.length > 0" class="failed-section">
      <div class="section-header">
        <el-icon class="error-icon"><CircleCloseFilled /></el-icon>
        <h3>分配失败 ({{ result.failedTasks.length }}个任务)</h3>
      </div>
      
      <div class="failed-list">
        <div
          v-for="failedTask in result.failedTasks"
          :key="failedTask.taskId"
          class="assignment-item failed"
        >
          <div class="failed-header">
            <h4>{{ failedTask.taskName }}</h4>
            <el-tag type="danger" size="small">分配失败</el-tag>
          </div>

          <div class="failure-reason">
            <h5>失败原因:</h5>
            <p>{{ failedTask.failureReason }}</p>
          </div>

          <div v-if="failedTask.suggestedActions && failedTask.suggestedActions.length > 0" class="suggestions">
            <h5>建议操作:</h5>
            <ul>
              <li v-for="action in failedTask.suggestedActions" :key="action">
                {{ action }}
              </li>
            </ul>
          </div>

          <div class="failed-actions">
            <el-button size="small" type="primary" @click="manualAssign(failedTask)">
              <el-icon><User /></el-icon>
              手动分配
            </el-button>
            <el-button size="small" @click="retryAssignment(failedTask)">
              <el-icon><RefreshRight /></el-icon>
              重试分配
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 分配总结 -->
    <div v-if="result.summary" class="summary-section">
      <el-card shadow="never">
        <template #header>
          <span>分配总结</span>
        </template>
        
        <div class="summary-stats">
          <div class="stat-item">
            <span class="stat-label">总任务数</span>
            <span class="stat-value">{{ result.summary.totalTasks }}</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">成功分配</span>
            <span class="stat-value success">{{ result.summary.successfulAssignments }}</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">分配失败</span>
            <span class="stat-value failed">{{ result.summary.failedAssignments }}</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">平均置信度</span>
            <span class="stat-value">{{ Math.round(result.summary.averageConfidence || 0) }}%</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">处理耗时</span>
            <span class="stat-value">{{ result.summary.processingTime }}ms</span>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 建议信息 -->
    <div v-if="result.suggestions && result.suggestions.length > 0" class="suggestions-section">
      <el-alert
        title="系统建议"
        type="info"
        :closable="false"
        show-icon
      >
        <ul>
          <li v-for="suggestion in result.suggestions" :key="suggestion">
            {{ suggestion }}
          </li>
        </ul>
      </el-alert>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import {
  SuccessFilled,
  CircleCloseFilled,
  Setting,
  Check,
  Refresh,
  User,
  RefreshRight
} from '@element-plus/icons-vue';

// Props
const props = defineProps({
  result: {
    type: Object,
    required: true
  }
});

// Emits
const emit = defineEmits([
  'confirm-assignment',
  'reassign-task',
  'manual-assign',
  'retry-assignment'
]);

// 方法
const getConfidenceColor = (confidence: number) => {
  if (confidence >= 80) return '#67c23a';
  if (confidence >= 60) return '#e6a23c';
  return '#f56c6c';
};

const getAvatarUrl = (userId: number) => {
  // 这里可以根据用户ID生成头像URL
  return `https://api.dicebear.com/7.x/avataaars/svg?seed=${userId}`;
};

const confirmAssignment = (assignment: any) => {
  emit('confirm-assignment', assignment);
};

const reassignTask = (assignment: any) => {
  emit('reassign-task', assignment);
};

const manualAssign = (failedTask: any) => {
  emit('manual-assign', failedTask);
};

const retryAssignment = (failedTask: any) => {
  emit('retry-assignment', failedTask);
};
</script>

<style lang="scss" scoped>
.assignment-result {
  .section-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 16px;
    
    .success-icon {
      color: #67c23a;
      font-size: 20px;
    }
    
    .error-icon {
      color: #f56c6c;
      font-size: 20px;
    }
    
    h3 {
      margin: 0;
      color: #303133;
    }
  }
  
  .assignment-item {
    background: white;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    
    &.success {
      border-left: 4px solid #67c23a;
    }
    
    &.failed {
      border-left: 4px solid #f56c6c;
    }
    
    .assignment-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 16px;
      
      .task-info {
        display: flex;
        align-items: center;
        gap: 12px;
        
        h4 {
          margin: 0;
          color: #303133;
        }
      }
      
      .confidence-badge {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 4px;
        
        .confidence-text {
          font-size: 12px;
          color: #909399;
        }
      }
    }
    
    .assignees-section {
      margin-bottom: 16px;
      
      h5 {
        margin: 0 0 8px 0;
        color: #606266;
        font-size: 14px;
      }
      
      .assignees-list {
        display: flex;
        flex-direction: column;
        gap: 8px;
        
        .assignee-card {
          background: #f8f9fa;
          border-radius: 6px;
          padding: 12px;
          
          .assignee-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            
            .assignee-name {
              display: flex;
              align-items: center;
              gap: 8px;
              
              .name-role {
                display: flex;
                flex-direction: column;
                
                .name {
                  font-weight: 500;
                  color: #303133;
                }
                
                .role {
                  font-size: 12px;
                  color: #909399;
                }
              }
            }
            
            .assignee-stats {
              display: flex;
              align-items: center;
              gap: 8px;
              
              .workload {
                font-size: 12px;
                color: #606266;
              }
            }
          }
        }
      }
    }
    
    .reason-section {
      margin-bottom: 16px;
      
      h5 {
        margin: 0 0 8px 0;
        color: #606266;
        font-size: 14px;
      }
      
      .reason-content {
        background: #f8f9fa;
        border-radius: 4px;
        padding: 12px;
        
        pre {
          margin: 0;
          font-family: inherit;
          white-space: pre-wrap;
          color: #606266;
          font-size: 13px;
          line-height: 1.5;
        }
      }
    }
    
    .assignment-actions,
    .failed-actions {
      display: flex;
      gap: 8px;
    }
    
    .failed-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
      
      h4 {
        margin: 0;
        color: #303133;
      }
    }
    
    .failure-reason {
      margin-bottom: 16px;
      
      h5 {
        margin: 0 0 8px 0;
        color: #606266;
        font-size: 14px;
      }
      
      p {
        margin: 0;
        color: #f56c6c;
        background: #fef0f0;
        padding: 8px 12px;
        border-radius: 4px;
        font-size: 13px;
      }
    }
    
    .suggestions {
      margin-bottom: 16px;
      
      h5 {
        margin: 0 0 8px 0;
        color: #606266;
        font-size: 14px;
      }
      
      ul {
        margin: 0;
        padding-left: 20px;
        color: #606266;
        font-size: 13px;
        
        li {
          margin-bottom: 4px;
        }
      }
    }
  }
  
  .summary-section {
    margin-top: 24px;
    
    .summary-stats {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
      gap: 16px;
      
      .stat-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        text-align: center;
        
        .stat-label {
          font-size: 12px;
          color: #909399;
          margin-bottom: 4px;
        }
        
        .stat-value {
          font-size: 18px;
          font-weight: 600;
          color: #303133;
          
          &.success {
            color: #67c23a;
          }
          
          &.failed {
            color: #f56c6c;
          }
        }
      }
    }
  }
  
  .suggestions-section {
    margin-top: 16px;
    
    ul {
      margin: 8px 0 0 0;
      padding-left: 20px;
      
      li {
        margin-bottom: 4px;
      }
    }
  }
}
</style>
