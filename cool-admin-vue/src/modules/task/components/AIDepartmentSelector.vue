<template>
	<div class="department-selector">
		<el-select
			v-model="selectedDepartments"
			multiple
			collapse-tags
			collapse-tags-tooltip
			:max-collapse-tags="3"
			placeholder="选择目标部门"
			filterable
			clearable
			class="w-full"
			@change="handleChange"
		>
			<template #prefix>
				<el-icon><OfficeBuilding /></el-icon>
			</template>

			<el-option
				v-for="dept in allDepartments"
				:key="dept.id"
				:label="dept.name"
				:value="dept.id"
			>
				<div class="flex justify-between items-center">
					<span>{{ dept.name }}</span>
					<el-tag v-if="dept.id === userStore.info?.departmentId" type="success" size="small"
						>当前部门</el-tag
					>
				</div>
			</el-option>
		</el-select>
	</div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from "vue";
import { useUserStore } from "/@/modules/base/store/user";
import { OfficeBuilding } from "@element-plus/icons-vue";

const props = defineProps({
	modelValue: {
		type: Array,
		default: () => []
	}
});

const emit = defineEmits(["update:modelValue"]);

const userStore = useUserStore();

const selectedDepartments = ref<number[]>(props.modelValue || []);

const allDepartments = computed(() => userStore.departments);

const handleChange = (value: number[]) => {
	emit("update:modelValue", value);
};

watch(
	() => props.modelValue,
	(newValue) => {
		selectedDepartments.value = newValue || [];
	},
	{ deep: true }
);

onMounted(async () => {
	if (!allDepartments.value || allDepartments.value.length === 0) {
		await userStore.getDepartmentList();
	}

	// 默认选择用户当前部门
	if (
		(!props.modelValue || props.modelValue.length === 0) &&
		userStore.info?.departmentId &&
		!selectedDepartments.value.includes(userStore.info.departmentId)
	) {
		selectedDepartments.value.push(userStore.info.departmentId);
		emit("update:modelValue", selectedDepartments.value);
	}
});
</script>

<style lang="scss" scoped>
.department-selector {
	width: 100%;
}
</style> 