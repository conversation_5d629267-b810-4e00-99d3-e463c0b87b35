<template>
  <el-tag 
    :type="tagType" 
    :size="size"
    :class="tagClass"
  >
    <el-icon v-if="showIcon">
      <component :is="priorityIcon" />
    </el-icon>
    <span>{{ priorityText }}</span>
  </el-tag>
</template>

<script setup lang="ts">
import { computed } from 'vue'

const props = defineProps({
  priority: {
    type: [Number, String],
    required: true
  },
  size: {
    type: String,
    default: 'small'
  },
  showIcon: {
    type: Boolean,
    default: true
  }
})

const priorityConfig = computed(() => {
  const configs = {
    1: { text: '低', type: 'success', icon: 'ArrowDown' },
    2: { text: '中', type: 'warning', icon: 'Minus' },
    3: { text: '高', type: 'danger', icon: 'ArrowUp' },
    4: { text: '紧急', type: 'danger', icon: 'Warning' }
  }
  return configs[props.priority] || { text: '普通', type: 'info', icon: 'Minus' }
})

const priorityText = computed(() => priorityConfig.value.text)
const tagType = computed(() => priorityConfig.value.type)
const priorityIcon = computed(() => priorityConfig.value.icon)

const tagClass = computed(() => ({
  'priority-tag': true,
  [`priority-${props.priority}`]: true
}))
</script>

<style scoped>
.priority-tag {
  display: inline-flex;
  align-items: center;
  gap: 2px;
}

.priority-4 {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.7; }
  100% { opacity: 1; }
}
</style> 