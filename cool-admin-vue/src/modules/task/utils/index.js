import { format } from 'date-fns'

/**
 * 格式化日期时间
 * @param {string|Date} dateTime 日期时间
 * @param {string} formatStr 格式化字符串
 * @returns {string} 格式化后的字符串
 */
export function formatDateTime(dateTime, formatStr = 'yyyy-MM-dd HH:mm') {
  if (!dateTime) return '-'
  
  try {
    const date = typeof dateTime === 'string' ? new Date(dateTime) : dateTime
    return format(date, formatStr)
  } catch (error) {
    console.error('日期格式化失败:', error)
    return '-'
  }
}

/**
 * 格式化日期
 * @param {string|Date} date 日期
 * @returns {string} 格式化后的字符串
 */
export function formatDate(date) {
  return formatDateTime(date, 'yyyy-MM-dd')
}

/**
 * 格式化时间
 * @param {string|Date} time 时间
 * @returns {string} 格式化后的字符串
 */
export function formatTime(time) {
  return formatDateTime(time, 'HH:mm:ss')
}

/**
 * 计算时间差
 * @param {string|Date} startTime 开始时间
 * @param {string|Date} endTime 结束时间
 * @returns {string} 时间差描述
 */
export function getTimeDuration(startTime, endTime) {
  if (!startTime || !endTime) return '-'
  
  try {
    const start = new Date(startTime)
    const end = new Date(endTime)
    const diffMs = end.getTime() - start.getTime()
    
    if (diffMs < 0) return '时间异常'
    
    const days = Math.floor(diffMs / (1000 * 60 * 60 * 24))
    const hours = Math.floor((diffMs % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
    const minutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60))
    
    if (days > 0) {
      return `${days}天${hours}小时`
    } else if (hours > 0) {
      return `${hours}小时${minutes}分钟`
    } else {
      return `${minutes}分钟`
    }
  } catch (error) {
    console.error('时间差计算失败:', error)
    return '-'
  }
}

/**
 * 获取相对时间描述
 * @param {string|Date} dateTime 时间
 * @returns {string} 相对时间描述
 */
export function getRelativeTime(dateTime) {
  if (!dateTime) return '-'
  
  try {
    const now = new Date()
    const target = new Date(dateTime)
    const diffMs = now.getTime() - target.getTime()
    const diffMinutes = Math.floor(diffMs / (1000 * 60))
    const diffHours = Math.floor(diffMinutes / 60)
    const diffDays = Math.floor(diffHours / 24)
    
    if (diffMinutes < 1) {
      return '刚刚'
    } else if (diffMinutes < 60) {
      return `${diffMinutes}分钟前`
    } else if (diffHours < 24) {
      return `${diffHours}小时前`
    } else if (diffDays < 30) {
      return `${diffDays}天前`
    } else {
      return formatDate(dateTime)
    }
  } catch (error) {
    console.error('相对时间计算失败:', error)
    return '-'
  }
}

/**
 * 计算任务完成率
 * @param {number} completed 已完成数量
 * @param {number} total 总数量
 * @returns {number} 完成率百分比
 */
export function calculateCompletionRate(completed, total) {
  if (!total || total === 0) return 0
  return Math.round((completed / total) * 100)
}

/**
 * 获取任务优先级描述
 * @param {number} priority 优先级
 * @returns {object} 优先级配置
 */
export function getPriorityConfig(priority) {
  const configs = {
    1: { text: '低', color: '#67c23a', icon: 'ArrowDown' },
    2: { text: '中', color: '#e6a23c', icon: 'Minus' },
    3: { text: '高', color: '#f56c6c', icon: 'ArrowUp' },
    4: { text: '紧急', color: '#f56c6c', icon: 'Warning' }
  }
  return configs[priority] || { text: '普通', color: '#909399', icon: 'Minus' }
}

/**
 * 生成随机颜色
 * @param {string} seed 种子字符串
 * @returns {string} 颜色值
 */
export function generateColor(seed) {
  let hash = 0
  for (let i = 0; i < seed.length; i++) {
    hash = seed.charCodeAt(i) + ((hash << 5) - hash)
  }
  
  const colors = [
    '#409eff', '#67c23a', '#e6a23c', '#f56c6c', 
    '#909399', '#00d7ff', '#ff6b6b', '#4ecdc4',
    '#45b7d1', '#96ceb4', '#ffeaa7', '#dda0dd'
  ]
  
  return colors[Math.abs(hash) % colors.length]
}

/**
 * 截断文本
 * @param {string} text 文本
 * @param {number} maxLength 最大长度
 * @returns {string} 截断后的文本
 */
export function truncateText(text, maxLength = 50) {
  if (!text) return ''
  if (text.length <= maxLength) return text
  return text.substring(0, maxLength) + '...'
}

/**
 * 去除HTML标签
 * @param {string} html HTML字符串
 * @returns {string} 纯文本
 */
export function stripHtml(html) {
  if (!html) return ''
  return html.replace(/<[^>]*>/g, '')
}

/**
 * 防抖函数
 * @param {Function} func 要防抖的函数
 * @param {number} wait 等待时间
 * @returns {Function} 防抖后的函数
 */
export function debounce(func, wait = 300) {
  let timeout
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout)
      func(...args)
    }
    clearTimeout(timeout)
    timeout = setTimeout(later, wait)
  }
}

/**
 * 节流函数
 * @param {Function} func 要节流的函数
 * @param {number} limit 限制时间
 * @returns {Function} 节流后的函数
 */
export function throttle(func, limit = 300) {
  let inThrottle
  return function executedFunction(...args) {
    if (!inThrottle) {
      func.apply(this, args)
      inThrottle = true
      setTimeout(() => inThrottle = false, limit)
    }
  }
}

/**
 * 深拷贝对象
 * @param {any} obj 要拷贝的对象
 * @returns {any} 拷贝后的对象
 */
export function deepClone(obj) {
  if (obj === null || typeof obj !== 'object') return obj
  if (obj instanceof Date) return new Date(obj.getTime())
  if (obj instanceof Array) return obj.map(item => deepClone(item))
  if (typeof obj === 'object') {
    const clonedObj = {}
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        clonedObj[key] = deepClone(obj[key])
      }
    }
    return clonedObj
  }
}

/**
 * 检查是否为空值
 * @param {any} value 要检查的值
 * @returns {boolean} 是否为空
 */
export function isEmpty(value) {
  if (value === null || value === undefined) return true
  if (typeof value === 'string') return value.trim() === ''
  if (Array.isArray(value)) return value.length === 0
  if (typeof value === 'object') return Object.keys(value).length === 0
  return false
}

/**
 * 数组去重
 * @param {Array} array 要去重的数组
 * @param {string} key 去重的键名（对象数组）
 * @returns {Array} 去重后的数组
 */
export function uniqueArray(array, key = null) {
  if (!Array.isArray(array)) return []
  
  if (key) {
    const seen = new Set()
    return array.filter(item => {
      const value = item[key]
      if (seen.has(value)) {
        return false
      } else {
        seen.add(value)
        return true
      }
    })
  } else {
    return [...new Set(array)]
  }
}

/**
 * 数组分组
 * @param {Array} array 要分组的数组
 * @param {string|Function} key 分组的键名或函数
 * @returns {Object} 分组后的对象
 */
export function groupBy(array, key) {
  if (!Array.isArray(array)) return {}
  
  return array.reduce((groups, item) => {
    const groupKey = typeof key === 'function' ? key(item) : item[key]
    if (!groups[groupKey]) {
      groups[groupKey] = []
    }
    groups[groupKey].push(item)
    return groups
  }, {})
}

/**
 * 数字格式化
 * @param {number} num 数字
 * @param {number} precision 小数位数
 * @returns {string} 格式化后的数字
 */
export function formatNumber(num, precision = 2) {
  if (isNaN(num)) return '0'
  return Number(num).toFixed(precision)
}

/**
 * 文件大小格式化
 * @param {number} bytes 字节数
 * @returns {string} 格式化后的文件大小
 */
export function formatFileSize(bytes) {
  if (bytes === 0) return '0 B'
  
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(1024))
  
  return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i]
}

/**
 * URL参数解析
 * @param {string} url URL字符串
 * @returns {Object} 参数对象
 */
export function parseUrlParams(url) {
  const params = {}
  const urlObj = new URL(url)
  
  for (const [key, value] of urlObj.searchParams) {
    params[key] = value
  }
  
  return params
}

/**
 * 生成UUID
 * @returns {string} UUID字符串
 */
export function generateUUID() {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0
    const v = c == 'x' ? r : (r & 0x3 | 0x8)
    return v.toString(16)
  })
} 