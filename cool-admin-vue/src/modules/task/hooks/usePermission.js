import { ref, computed } from 'vue'
import { useCool } from '/@/cool'
import { useUserStore } from '/@/modules/base/store/user'

export function usePermission() {
  const { service } = useCool()
  const userStore = useUserStore()
  
  // 权限缓存
  const permissionCache = ref(new Map())
  const userDepartments = ref([])
  const departmentTaskCounts = ref({})
  
  // 获取用户部门信息
  const loadUserDepartments = async () => {
    try {
      const response = await service.base.sys.department.list({
        userId: userStore.userInfo?.id
      })
      userDepartments.value = response.data || []
      return userDepartments.value
    } catch (error) {
      console.error('加载用户部门失败:', error)
      return []
    }
  }
  
  // 获取部门任务统计
  const loadDepartmentTaskCounts = async () => {
    try {
      const response = await service.task.package.page({
        page: 1,
        size: 1,
        departmentStats: true
      })
      departmentTaskCounts.value = response.data?.departmentCounts || {}
      return departmentTaskCounts.value
    } catch (error) {
      console.error('加载部门任务统计失败:', error)
      return {}
    }
  }
  
  // 检查任务权限
  const hasTaskPermission = async (taskType, taskId, operation) => {
    const cacheKey = `${taskType}:${taskId}:${operation}`
    
    // 检查缓存
    const cached = permissionCache.value.get(cacheKey)
    if (cached && Date.now() - cached.timestamp < 5 * 60 * 1000) {
      return cached.hasPermission
    }
    
    try {
      // admin用户拥有所有权限
      if (userStore.userInfo?.username === 'admin') {
        const result = { hasPermission: true, timestamp: Date.now() }
        permissionCache.value.set(cacheKey, result)
        return true
      }
      
      // 模拟权限检查 - 实际应调用后端API
      // const response = await service.task.permission.check({
      //   taskType, taskId, operation
      // })
      
      // 临时权限逻辑：基于用户部门权限
      const hasPermission = await checkTaskPermission(taskType, taskId, operation)
      
      const result = { hasPermission, timestamp: Date.now() }
      permissionCache.value.set(cacheKey, result)
      
      return hasPermission
    } catch (error) {
      console.error('权限检查失败:', error)
      return false
    }
  }
  
  // 检查任务权限的具体逻辑
  const checkTaskPermission = async (taskType, taskId, operation) => {
    try {
      // 获取任务信息
      let taskData
      switch (taskType) {
        case 'package':
          taskData = await service.task.package.info({ id: taskId })
          break
        case 'info':
          taskData = await service.task.info.info({ id: taskId })
          break
        default:
          return false
      }
      
      if (!taskData?.data) return false
      
      const task = taskData.data
      const currentUserId = userStore.userInfo?.id
      const currentUserDeptId = userStore.userInfo?.departmentId
      
      // 检查是否为任务创建者
      if (task.creatorId === currentUserId) {
        return true
      }
      
      // 检查部门权限
      if (task.departmentId === currentUserDeptId) {
        return ['VIEW', 'EDIT', 'ASSIGN'].includes(operation)
      }
      
      // 检查是否在用户权限部门范围内
      const userDeptIds = userDepartments.value.map(d => d.id)
      if (userDeptIds.includes(task.departmentId)) {
        return operation === 'VIEW' || operation === 'EDIT'
      }
      
      return false
    } catch (error) {
      console.error('检查任务权限失败:', error)
      return false
    }
  }
  
  // 获取部门权限级别
  const getDepartmentPermissionLevel = (department) => {
    if (!department || !department.id) {
      return 'none'
    }
    
    const currentUserDeptId = userStore.userInfo?.departmentId
    
    // admin用户拥有所有权限
    if (userStore.userInfo?.username === 'admin') {
      return 'full'
    }
    
    // 检查是否为用户的直属部门
    if (department.id === currentUserDeptId) {
      return 'full'
    }
    
    // 检查是否在用户的权限部门列表中
    const userDeptIds = userDepartments.value.map(d => d.id)
    if (userDeptIds.includes(department.id)) {
      return 'department'
    }
    
    return 'none'
  }
  
  // 获取任务权限级别
  const getTaskPermissionLevel = (task) => {
    const departmentLevel = getDepartmentPermissionLevel({
      id: task.departmentId
    })
    
    if (departmentLevel === 'none') {
      // 检查是否为跨部门协作任务
      if (task.isCrossDepartment) {
        return 'limited'
      }
      return 'none'
    }
    
    return departmentLevel
  }
  
  // 检查操作权限
  const canOperateTask = (task, operation) => {
    const permissionLevel = getTaskPermissionLevel(task)
    
    switch (operation) {
      case 'VIEW':
        return permissionLevel !== 'none'
      case 'EDIT':
        return ['full', 'department'].includes(permissionLevel)
      case 'DELETE':
        return permissionLevel === 'full'
      case 'ASSIGN':
        return ['full', 'department'].includes(permissionLevel)
      default:
        return false
    }
  }
  
  // 获取部门名称
  const getDepartmentName = async (departmentId) => {
    if (!departmentId) return '未分配'
    
    // 先从缓存中查找
    const dept = userDepartments.value.find(d => d.id === departmentId)
    if (dept?.name) {
      return dept.name
    }
    
    // 如果缓存中没有，尝试从后端获取
    try {
      const response = await service.base.sys.department.info({ id: departmentId })
      if (response?.data?.name) {
        const departmentName = response.data.name
        // 更新到缓存中
        const existingDept = userDepartments.value.find(d => d.id === departmentId)
        if (existingDept) {
          existingDept.name = departmentName
        } else {
          userDepartments.value.push({
            id: departmentId,
            name: departmentName
          })
        }
        return departmentName
      }
    } catch (error) {
      console.warn(`获取部门${departmentId}名称失败:`, error)
    }
    
    return '未知部门'
  }
  
  // 获取部门任务数量
  const getDepartmentTaskCount = (departmentId) => {
    return departmentTaskCounts.value[departmentId] || 0
  }
  
  // 清除权限缓存
  const clearPermissionCache = () => {
    permissionCache.value.clear()
  }
  
  // 计算属性
  const currentUserDepartment = computed(() => {
    const currentUserDeptId = userStore.userInfo?.departmentId
    return userDepartments.value.find(d => d.id === currentUserDeptId)
  })
  
  const departmentMap = computed(() => {
    const map = new Map()
    userDepartments.value.forEach(dept => {
      map.set(dept.id, dept)
    })
    return map
  })
  
  return {
    // 数据
    userDepartments,
    departmentTaskCounts,
    currentUserDepartment,
    departmentMap,
    
    // 方法
    loadUserDepartments,
    loadDepartmentTaskCounts,
    hasTaskPermission,
    getDepartmentPermissionLevel,
    getTaskPermissionLevel,
    canOperateTask,
    getDepartmentName,
    getDepartmentTaskCount,
    clearPermissionCache
  }
} 