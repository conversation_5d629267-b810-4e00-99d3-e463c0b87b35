[INFO] Scanning for projects...
[INFO] 
[INFO] ------------------------< com.cool:cool-admin >-------------------------
[INFO] Building cool-admin 8.0.0
[INFO]   from pom.xml
[INFO] --------------------------------[ jar ]---------------------------------
[INFO] 
[INFO] >>> spring-boot:3.2.5:run (default-cli) > test-compile @ cool-admin >>>
[INFO] 
[INFO] --- resources:3.3.1:resources (default-resources) @ cool-admin ---
[INFO] Copying 3 resources from src/main/resources to target/classes
[INFO] Copying 27 resources from src/main/resources to target/classes
[INFO] 
[INFO] --- compiler:3.11.0:compile (default-compile) @ cool-admin ---
[INFO] Nothing to compile - all classes are up to date
[INFO] 
[INFO] --- resources:3.3.1:testResources (default-testResources) @ cool-admin ---
[INFO] skip non existing resourceDirectory /Volumes/Data/code/yaya/cool/cool-admin-java/src/test/resources
[INFO] 
[INFO] --- compiler:3.11.0:testCompile (default-testCompile) @ cool-admin ---
[INFO] Nothing to compile - all classes are up to date
[INFO] 
[INFO] <<< spring-boot:3.2.5:run (default-cli) < test-compile @ cool-admin <<<
[INFO] 
[INFO] 
[INFO] --- spring-boot:3.2.5:run (default-cli) @ cool-admin ---
[INFO] Attaching agents: []
✨🌈✨[cool-admin-java-plus](https://gitee.com/hlc4417/cool-admin-java-plus)✨🌈✨
   ______    ___      ___   _____              _       ______   ____    ____  _____  ____  _____
 .' ___  | .'   `.  .'   `.|_   _|    V8.x    / \     |_   _ `.|_   \  /   _||_   _||_   \|_   _|
/ .'   \_|/  .-.  \/  .-.  \ | |     ______  / _ \      | | `. \ |   \/   |    | |    |   \ | |
| |       | |   | || |   | | | |   _|______|/ ___ \     | |  | | | |\  /| |    | |    | |\ \| |
\ `.___.'\\  `-'  /\  `-'  /_| |__/ |     _/ /   \ \_  _| |_.' /_| |_\/_| |_  _| |_  _| |_\   |_
 `.____ .' `.___.'  `.___.'|________|    |____| |____||______.'|_____||_____||_____||_____|\____|
:: https://java.cool-admin.com ::
2025-07-04T16:46:50.462+08:00  INFO 92160 --- [cool-admin-java] [           main] com.cool.CoolApplication                 : Starting CoolApplication using Java 21.0.7 with PID 92160 (/Volumes/Data/code/yaya/cool/cool-admin-java/target/classes started by goku in /Volumes/Data/code/yaya/cool/cool-admin-java)
2025-07-04T16:46:50.463+08:00  INFO 92160 --- [cool-admin-java] [           main] com.cool.CoolApplication                 : The following 1 profile is active: "dev"
2025-07-04T16:46:50.977+08:00  INFO 92160 --- [cool-admin-java] [           main] .s.d.r.c.RepositoryConfigurationDelegate : Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-04T16:46:50.978+08:00  INFO 92160 --- [cool-admin-java] [           main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-04T16:46:51.010+08:00  INFO 92160 --- [cool-admin-java] [           main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 25 ms. Found 0 Redis repository interfaces.
2025-07-04T16:46:51.482+08:00  INFO 92160 --- [cool-admin-java] [           main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 18001 (http)
2025-07-04T16:46:51.488+08:00  INFO 92160 --- [cool-admin-java] [           main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-07-04T16:46:51.488+08:00  INFO 92160 --- [cool-admin-java] [           main] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.20]
2025-07-04T16:46:51.521+08:00  INFO 92160 --- [cool-admin-java] [           main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-07-04T16:46:51.522+08:00  INFO 92160 --- [cool-admin-java] [           main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1037 ms
2025-07-04T16:46:51.782+08:00 ERROR 92160 --- [cool-admin-java] [           main] o.s.b.web.embedded.tomcat.TomcatStarter  : Error starting Tomcat context. Exception: org.springframework.beans.factory.UnsatisfiedDependencyException. Message: Error creating bean with name 'jwtAuthenticationTokenFilter' defined in file [/Volumes/Data/code/yaya/cool/cool-admin-java/target/classes/com/cool/core/security/JwtAuthenticationTokenFilter.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'jwtTokenUtil' defined in file [/Volumes/Data/code/yaya/cool/cool-admin-java/target/classes/com/cool/core/security/jwt/JwtTokenUtil.class]: Unsatisfied dependency expressed through constructor parameter 1: Error creating bean with name 'baseSysConfServiceImpl': Unsatisfied dependency expressed through field 'mapper': Error creating bean with name 'baseSysConfMapper' defined in file [/Volumes/Data/code/yaya/cool/cool-admin-java/target/classes/com/cool/modules/base/mapper/sys/BaseSysConfMapper.class]: Unsatisfied dependency expressed through bean property 'sqlSessionFactory': Error creating bean with name 'sqlSessionFactory' defined in class path resource [com/mybatisflex/spring/boot/MybatisFlexAutoConfiguration.class]: Unsatisfied dependency expressed through method 'sqlSessionFactory' parameter 0: Error creating bean with name 'dataSource' defined in class path resource [org/springframework/boot/autoconfigure/jdbc/DataSourceConfiguration$Hikari.class]: Failed to instantiate [com.zaxxer.hikari.HikariDataSource]: Factory method 'dataSource' threw exception with message: Failed to determine a suitable driver class
2025-07-04T16:46:51.795+08:00  INFO 92160 --- [cool-admin-java] [           main] o.apache.catalina.core.StandardService   : Stopping service [Tomcat]
2025-07-04T16:46:51.801+08:00  WARN 92160 --- [cool-admin-java] [           main] o.a.c.loader.WebappClassLoaderBase       : The web application [ROOT] appears to have started a thread named [lettuce-timer-3-1] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.base/java.lang.Thread.sleep0(Native Method)
 java.base/java.lang.Thread.sleep(Thread.java:509)
 io.netty.util.HashedWheelTimer$Worker.waitForNextTick(HashedWheelTimer.java:591)
 io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:487)
 io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
 java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-04T16:46:51.803+08:00  WARN 92160 --- [cool-admin-java] [           main] ConfigServletWebServerApplicationContext : Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Unable to start web server
2025-07-04T16:46:51.823+08:00  INFO 92160 --- [cool-admin-java] [           main] .s.b.a.l.ConditionEvaluationReportLogger : 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-07-04T16:46:51.830+08:00 ERROR 92160 --- [cool-admin-java] [           main] o.s.b.d.LoggingFailureAnalysisReporter   : 

***************************
APPLICATION FAILED TO START
***************************

Description:

Failed to configure a DataSource: 'url' attribute is not specified and no embedded datasource could be configured.

Reason: Failed to determine a suitable driver class


Action:

Consider the following:
	If you want an embedded database (H2, HSQL or Derby), please put it on the classpath.
	If you have database settings to be loaded from a particular profile you may need to activate it (the profiles dev are currently active).

[INFO] ------------------------------------------------------------------------
[INFO] BUILD FAILURE
[INFO] ------------------------------------------------------------------------
[INFO] Total time:  3.614 s
[INFO] Finished at: 2025-07-04T16:46:52+08:00
[INFO] ------------------------------------------------------------------------
[ERROR] Failed to execute goal org.springframework.boot:spring-boot-maven-plugin:3.2.5:run (default-cli) on project cool-admin: Process terminated with exit code: 1 -> [Help 1]
[ERROR] 
[ERROR] To see the full stack trace of the errors, re-run Maven with the -e switch.
[ERROR] Re-run Maven using the -X switch to enable full debug logging.
[ERROR] 
[ERROR] For more information about the errors and possible solutions, please read the following articles:
[ERROR] [Help 1] http://cwiki.apache.org/confluence/display/MAVEN/MojoExecutionException
