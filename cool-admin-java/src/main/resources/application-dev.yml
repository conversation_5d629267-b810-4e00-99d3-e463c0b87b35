spring:
  datasource:
    url: *****************************************************************************************************
    username: root
    password: A@1234cn
    driver-class-name: com.mysql.cj.jdbc.Driver

# AutoTable配置，根据实体类自动生成表
auto-table:
  # 启用自动维护表功能
  enable: true
  # 自动删除名称不匹配的索引
  autoDropIndex: true
  # 建表的时候，父类的字段排序是在子类后面还是前面
  superInsertPosition: before
  # 模型包路径
  model-package: com.cool.**.entity.*

# Cool相关配置
cool:
  # 初始化数据
  initData: true

# 文档
springdoc:
  api-docs:
    #是否开启文档功能
    enabled: true
  group-configs:
    - group: app
      paths-to-match: /app/**
    - group: admin
      paths-to-match: /admin/**

# 设置日志级别
logging:
  level:
    com.cool: debug 