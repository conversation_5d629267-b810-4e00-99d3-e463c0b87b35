<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cool.modules.task.mapper.TaskAssignmentMapper">

    <!-- 根据任务ID获取执行记录 -->
    <select id="getByTaskId" resultType="com.cool.modules.task.entity.TaskExecutionEntity">
        SELECT * FROM task_execution 
        WHERE task_id = #{taskId} 
        ORDER BY create_time DESC
    </select>

    <!-- 根据执行人ID获取执行记录 -->
    <select id="getByAssigneeId" resultType="com.cool.modules.task.entity.TaskExecutionEntity">
        SELECT * FROM task_execution 
        WHERE assignee_id = #{assigneeId} 
        ORDER BY create_time DESC
    </select>

    <!-- 检查任务是否已分配 -->
    <select id="countAssignedByTaskId" resultType="long">
        SELECT COUNT(*) FROM task_execution 
        WHERE task_id = #{taskId} 
        AND execution_status IN ('ASSIGNED', 'IN_PROGRESS')
    </select>

    <!-- 获取用户当前工作负载统计 -->
    <select id="getUserWorkloadStats" resultType="map">
        SELECT COUNT(*) as task_count FROM task_execution 
        WHERE assignee_id = #{userId} 
        AND execution_status IN ('ASSIGNED', 'IN_PROGRESS')
    </select>

    <!-- 获取用户历史绩效统计 -->
    <select id="getUserPerformanceStats" resultType="map">
        SELECT 
            COUNT(*) as total_tasks, 
            SUM(CASE WHEN execution_status = 'COMPLETED' THEN 1 ELSE 0 END) as completed_tasks, 
            AVG(CASE WHEN completion_time IS NOT NULL AND accept_time IS NOT NULL 
                THEN TIMESTAMPDIFF(HOUR, accept_time, completion_time) ELSE NULL END) as avg_completion_hours 
        FROM task_execution 
        WHERE assignee_id = #{userId}
    </select>

</mapper> 