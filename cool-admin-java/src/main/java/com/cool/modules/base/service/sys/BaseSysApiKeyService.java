package com.cool.modules.base.service.sys;

import com.cool.core.base.BaseService;
import com.cool.modules.base.entity.sys.BaseSysApiKeyEntity;

import java.util.List;

/**
 * APIKEY授权表Service接口
 */
public interface BaseSysApiKeyService extends BaseService<BaseSysApiKeyEntity> {
    /**
     * 创建APIKEY
     */
    BaseSysApiKeyEntity createApiKey(Long userId, String remark, Integer expireDays);

    /**
     * 重置APIKEY
     */
    BaseSysApiKeyEntity resetApiKey(Long id);

    /**
     * 禁用APIKEY
     */
    boolean disableApiKey(Long id);

    /**
     * 启用APIKEY
     */
    boolean enableApiKey(Long id);

    /**
     * 删除APIKEY
     */
    boolean deleteApiKey(Long id);

    /**
     * 查询用户的APIKEY列表
     */
    List<BaseSysApiKeyEntity> listByUserId(Long userId);

    /**
     * 根据apikey查找
     */
    BaseSysApiKeyEntity findByApiKey(String apikey);
} 