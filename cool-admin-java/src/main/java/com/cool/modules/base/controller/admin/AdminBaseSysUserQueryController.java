package com.cool.modules.base.controller.admin;

import com.cool.core.request.R;
import com.cool.modules.base.dto.UserQueryRequest;
import com.cool.modules.base.entity.sys.BaseSysUserEntity;
import com.cool.modules.base.service.sys.BaseSysUserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 用户查询控制器
 * 提供通用的用户查询接口，供各业务模块使用
 */
@RestController
@RequestMapping("/admin/base/user-query")
@Tag(name = "用户查询管理", description = "通用用户查询接口")
@RequiredArgsConstructor
public class AdminBaseSysUserQueryController {

    private final BaseSysUserService baseSysUserService;

    /**
     * 根据条件查询用户列表
     * 支持数据权限控制、部门筛选、角色筛选、模糊查询等
     */
    @PostMapping("/list")
    @Operation(summary = "根据条件查询用户列表", description = "支持数据权限控制、部门筛选、角色筛选、模糊查询等")
    public R<List<BaseSysUserEntity>> queryUsers(@RequestBody UserQueryRequest request) {
        List<BaseSysUserEntity> users = baseSysUserService.queryUsers(request);
        return R.ok(users);
    }

    /**
     * 获取可用的执行人列表
     * 默认配置：启用状态、排除admin、包含角色和部门信息
     */
    @GetMapping("/available-assignees")
    @Operation(summary = "获取可用的执行人列表", description = "获取可用的执行人列表，默认配置：启用状态、排除admin、包含角色和部门信息")
    public R<List<BaseSysUserEntity>> getAvailableAssignees(
            @RequestParam(required = false) List<Long> departmentIds,
            @RequestParam(required = false) List<Long> roleIds,
            @RequestParam(required = false) List<String> roleNames,
            @RequestParam(required = false) String keyword) {
        
        UserQueryRequest request = new UserQueryRequest();
        request.setDepartmentIds(departmentIds);
        request.setRoleIds(roleIds);
        request.setRoleNames(roleNames);
        request.setKeyword(keyword);
        request.setStatus(1); // 只查询启用状态的用户
        request.setExcludeAdmin(true); // 排除admin用户
        request.setIncludeRoles(true); // 包含角色信息
        request.setIncludeDepartment(true); // 包含部门信息
        
        List<BaseSysUserEntity> users = baseSysUserService.queryUsers(request);
        return R.ok(users);
    }

    /**
     * 根据部门获取用户列表
     */
    @GetMapping("/by-department/{departmentId}")
    @Operation(summary = "根据部门获取用户列表")
    public R<List<BaseSysUserEntity>> getUsersByDepartment(@PathVariable Long departmentId) {
        UserQueryRequest request = new UserQueryRequest();
        request.setDepartmentIds(List.of(departmentId));
        request.setStatus(1); // 只查询启用状态的用户
        request.setExcludeAdmin(true); // 排除admin用户
        request.setIncludeRoles(true); // 包含角色信息
        request.setIncludeDepartment(true); // 包含部门信息
        
        List<BaseSysUserEntity> users = baseSysUserService.queryUsers(request);
        return R.ok(users);
    }

    /**
     * 根据角色获取用户列表
     */
    @GetMapping("/by-role/{roleId}")
    @Operation(summary = "根据角色获取用户列表")
    public R<List<BaseSysUserEntity>> getUsersByRole(@PathVariable Long roleId) {
        UserQueryRequest request = new UserQueryRequest();
        request.setRoleIds(List.of(roleId));
        request.setStatus(1); // 只查询启用状态的用户
        request.setExcludeAdmin(true); // 排除admin用户
        request.setIncludeRoles(true); // 包含角色信息
        request.setIncludeDepartment(true); // 包含部门信息
        
        List<BaseSysUserEntity> users = baseSysUserService.queryUsers(request);
        return R.ok(users);
    }
} 