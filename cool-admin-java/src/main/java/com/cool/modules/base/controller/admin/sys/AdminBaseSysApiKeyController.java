package com.cool.modules.base.controller.admin.sys;

import com.cool.core.annotation.CoolRestController;
import com.cool.core.base.BaseController;
import com.cool.core.request.R;
import com.cool.core.util.CoolSecurityUtil;
import com.cool.modules.base.entity.sys.BaseSysApiKeyEntity;
import com.cool.modules.base.service.sys.BaseSysApiKeyService;

import cn.hutool.json.JSONObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;


/**
 * APIKEY管理接口
 */

@RequiredArgsConstructor
@Tag(name = "APIKEY管理", description = "APIKEY授权管理接口")
@CoolRestController(api = {"page","create","reset","disable","enable","delete","list"})
public class AdminBaseSysApiKeyController extends BaseController<BaseSysApiKeyService, BaseSysApiKeyEntity> {

    @Operation(summary = "创建APIKEY")
    @PostMapping("/create")
    public R<BaseSysApiKeyEntity> create(@RequestParam(required = false) String remark, @RequestParam(required = false) Integer expireDays) {
        Long userId = CoolSecurityUtil.getCurrentUserId();
        BaseSysApiKeyEntity entity = service.createApiKey(userId, remark, expireDays);
        return R.ok(entity);
    }

    @Operation(summary = "重置APIKEY")
    @PostMapping("/reset")
    public R<BaseSysApiKeyEntity> reset(@RequestParam Long id) {
        BaseSysApiKeyEntity entity = service.resetApiKey(id);
        return R.ok(entity);
    }

    @Operation(summary = "禁用APIKEY")
    @PostMapping("/disable")
    public R<Boolean> disable(@RequestParam Long id) {
        return R.ok(service.disableApiKey(id));
    }

    @Operation(summary = "启用APIKEY")
    @PostMapping("/enable")
    public R<Boolean> enable(@RequestParam Long id) {
        return R.ok(service.enableApiKey(id));
    }


    @Override
    protected void init(HttpServletRequest request, JSONObject requestParams) {
    }
} 