package com.cool.modules.dify.service;

import com.cool.core.base.BaseService;
import com.cool.modules.dify.dto.DifyWorkflowBlockingResponse;
import com.cool.modules.dify.dto.DifyWorkflowExecuteRequest;
import com.cool.modules.dify.entity.DifyWorkflowEntity;

import jakarta.servlet.http.HttpServletResponse;

/**
 * Dify工作流Service
 */
public interface DifyWorkflowService extends BaseService<DifyWorkflowEntity> {
    /**
     * 按英文名执行Dify对话型工作流
     */
    void executeWorkflowByName(DifyWorkflowExecuteRequest request, HttpServletResponse response);

    /**
     * 按英文名执行Dify对话型工作流（阻塞模式，返回完整结果）
     */
    DifyWorkflowBlockingResponse executeWorkflowByName(DifyWorkflowExecuteRequest request);
} 