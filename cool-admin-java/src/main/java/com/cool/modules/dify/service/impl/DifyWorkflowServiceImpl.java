package com.cool.modules.dify.service.impl;

import com.cool.modules.dify.entity.DifyWorkflowEntity;
import com.cool.modules.dify.mapper.DifyWorkflowMapper;
import com.cool.modules.dify.service.DifyWorkflowService;
import com.cool.core.base.BaseServiceImpl;
import com.cool.core.exception.CoolException;
import com.cool.modules.dify.dto.DifyWorkflowExecuteRequest;
import com.cool.modules.dify.dto.DifyWorkflowResponseModeEnum;
import com.cool.modules.dify.dto.DifyWorkflowBlockingResponse;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Flux;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import com.mybatisflex.core.query.QueryWrapper;
import cn.hutool.json.JSONUtil;
import cn.hutool.json.JSONObject;

/**
 * Dify工作流Service实现
 */
@Slf4j
@Service
public class DifyWorkflowServiceImpl extends BaseServiceImpl<DifyWorkflowMapper, DifyWorkflowEntity> implements DifyWorkflowService {

    @Autowired
    private RestTemplate restTemplate;

    @Autowired(required = false)
    private WebClient.Builder webClientBuilder;

    /**
     * 按英文名执行Dify Workflow应用（无会话，严格按/workflows/run接口）
     * @param request 请求参数
     * @param response Http响应（流式模式下直接写入）
     * @throws CoolException 业务异常
     */
    @Override
    public void executeWorkflowByName(DifyWorkflowExecuteRequest request, HttpServletResponse response) {
        // 1. 参数校验
        if (request == null || !StringUtils.hasText(request.getWorkflowName()) || request.getInputs() == null || !StringUtils.hasText(request.getUser())) {
            throw new CoolException("workflowName、inputs、user不能为空");
        }

        // 2. 查找工作流配置
        QueryWrapper qw = QueryWrapper.create()
            .eq("name", request.getWorkflowName())
            .eq("status", 1);
        DifyWorkflowEntity entity = this.mapper.selectOneByQuery(qw);
        if (entity == null) {
            throw new CoolException("未找到启用的Dify工作流配置: " + request.getWorkflowName());
        }

        // 3. 组装Dify API参数
        String apiUrl = entity.getUrl();
        String apiKey = entity.getApiKey();
        if (!StringUtils.hasText(apiUrl) || !StringUtils.hasText(apiKey)) {
            throw new CoolException("Dify工作流配置不完整（url/apiKey缺失）");
        }

        // 4. 构建请求体（严格按/workflows/run接口）
        Map<String, Object> body = new HashMap<>();
        // inputs为必填，文件变量需前置上传并填入inputs
        body.put("inputs", request.getInputs());
        body.put("user", request.getUser());
        String responseMode = StringUtils.hasText(request.getResponseMode()) ? request.getResponseMode() : DifyWorkflowResponseModeEnum.BLOCKING.getValue();
        body.put("response_mode", responseMode);

        // 5. 调用Dify API
        if (DifyWorkflowResponseModeEnum.STREAMING.name().equalsIgnoreCase(responseMode)) {
            handleStreaming(apiUrl, apiKey, body, response);
        } else {
            handleBlocking(apiUrl, apiKey, body, response);
        }
    }

    /**
     * 阻塞模式调用Dify API，返回完整结果（Service 间复用）
     */
    @Override
    public DifyWorkflowBlockingResponse executeWorkflowByName(DifyWorkflowExecuteRequest request) {
        // 1. 参数校验
        if (request == null || !StringUtils.hasText(request.getWorkflowName()) || request.getInputs() == null || !StringUtils.hasText(request.getUser())) {
            throw new CoolException("workflowName、inputs、user不能为空");
        }
        // 2. 查找工作流配置
        QueryWrapper qw = QueryWrapper.create()
            .eq("name", request.getWorkflowName())
            .eq("status", 1);
        DifyWorkflowEntity entity = this.mapper.selectOneByQuery(qw);
        if (entity == null) {
            throw new CoolException("未找到启用的Dify工作流配置: " + request.getWorkflowName());
        }
        // 3. 组装Dify API参数
        String apiUrl = entity.getUrl();
        String apiKey = entity.getApiKey();
        if (!StringUtils.hasText(apiUrl) || !StringUtils.hasText(apiKey)) {
            throw new CoolException("Dify工作流配置不完整（url/apiKey缺失）");
        }
        // 4. 构建请求体
        Map<String, Object> body = new HashMap<>();
        body.put("inputs", request.getInputs());
        body.put("user", request.getUser());
        String responseMode = StringUtils.hasText(request.getResponseMode()) ? request.getResponseMode() : DifyWorkflowResponseModeEnum.BLOCKING.getValue();
        body.put("response_mode", responseMode);
        log.debug("body: {}", body.toString());
        // 5. 阻塞调用并返回实体
        JSONObject json = handleBlocking(apiUrl, apiKey, body);
        return JSONUtil.toBean(json, DifyWorkflowBlockingResponse.class);
    }

    /**
     * 阻塞模式调用Dify API，返回完整结果（内部复用）
     */
    private JSONObject handleBlocking(String apiUrl, String apiKey, Map<String, Object> body) {
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.setBearerAuth(apiKey);
            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(body, headers);
            ResponseEntity<String> resp = restTemplate.postForEntity(apiUrl, entity, String.class);
            if (resp.getStatusCode().is2xxSuccessful()) {
                return JSONUtil.parseObj(resp.getBody());
            } else {
                throw new CoolException("Dify调用失败: " + resp.getBody());
            }
        } catch (Exception e) {
            log.error("Dify阻塞模式调用失败", e);
            throw new CoolException("Dify调用失败: " + e.getMessage());
        }
    }

    /**
     * 阻塞模式调用Dify API，直接写入 HttpServletResponse（兼容原有 Controller 输出场景）
     */
    private void handleBlocking(String apiUrl, String apiKey, Map<String, Object> body, HttpServletResponse response) {
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.setBearerAuth(apiKey);
            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(body, headers);
            ResponseEntity<String> resp = restTemplate.postForEntity(apiUrl, entity, String.class);
            response.setContentType("application/json;charset=UTF-8");
            response.getWriter().write(resp.getBody());
        } catch (Exception e) {
            log.error("Dify阻塞模式调用失败", e);
            throw new CoolException("Dify调用失败: " + e.getMessage());
        }
    }

    /**
     * 流式模式调用Dify API，SSE转发
     */
    private void handleStreaming(String apiUrl, String apiKey, Map<String, Object> body, HttpServletResponse response) {
        if (webClientBuilder == null) {
            throw new CoolException("WebClient未配置，无法支持流式SSE");
        }
        WebClient webClient = webClientBuilder.build();
        try {
            response.setContentType("text/event-stream;charset=UTF-8");
            response.setHeader("Cache-Control", "no-cache");
            response.setHeader("Connection", "keep-alive");

            Flux<String> flux = webClient.post()
                    .uri(apiUrl)
                    .header(HttpHeaders.AUTHORIZATION, "Bearer " + apiKey)
                    .contentType(MediaType.APPLICATION_JSON)
                    .bodyValue(body)
                    .retrieve()
                    .bodyToFlux(String.class);

            flux.doOnError(e -> log.error("Dify流式SSE调用失败", e))
                .doOnNext(chunk -> {
                    try {
                        response.getWriter().write(chunk);
                        response.getWriter().flush();
                    } catch (IOException ioException) {
                        log.error("SSE写入失败", ioException);
                    }
                })
                .blockLast(); // 阻塞直到流结束
        } catch (Exception e) {
            log.error("Dify流式SSE调用异常", e);
            throw new CoolException("Dify流式调用失败: " + e.getMessage());
        }
    }
} 