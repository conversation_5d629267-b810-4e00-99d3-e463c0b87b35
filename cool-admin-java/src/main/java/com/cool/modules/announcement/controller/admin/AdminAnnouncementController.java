package com.cool.modules.announcement.controller.admin;

import com.cool.core.annotation.CoolRestController;
import com.cool.core.base.BaseController;
import com.cool.modules.announcement.entity.AnnouncementEntity;
import com.cool.modules.announcement.entity.table.AnnouncementEntityTableDef;
import com.cool.modules.announcement.service.AnnouncementService;

import cn.hutool.json.JSONObject;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;

/**
 * 公示管理
 */
@Tag(name = "公示管理", description = "公示模块统一管理")
@CoolRestController(api = { "add", "delete", "update", "list", "info", "page" })
public class AdminAnnouncementController extends BaseController<AnnouncementService, AnnouncementEntity> {

    @Override
    protected void init(HttpServletRequest request, JSONObject requestParams) {
        setPageOption(createOp()
            .fieldEq(
                AnnouncementEntityTableDef.ANNOUNCEMENT_ENTITY.ID,
                AnnouncementEntityTableDef.ANNOUNCEMENT_ENTITY.PROJECT_ID,
                AnnouncementEntityTableDef.ANNOUNCEMENT_ENTITY.MONTH,
                AnnouncementEntityTableDef.ANNOUNCEMENT_ENTITY.STATUS
            ).keyWordLikeFields(
                AnnouncementEntityTableDef.ANNOUNCEMENT_ENTITY.TITLE
            )
        );
        setListOption(createOp().fieldEq(
            AnnouncementEntityTableDef.ANNOUNCEMENT_ENTITY.PROJECT_ID,
            AnnouncementEntityTableDef.ANNOUNCEMENT_ENTITY.MONTH,
            AnnouncementEntityTableDef.ANNOUNCEMENT_ENTITY.STATUS
        ).keyWordLikeFields(
            AnnouncementEntityTableDef.ANNOUNCEMENT_ENTITY.TITLE
        ));
    }
} 