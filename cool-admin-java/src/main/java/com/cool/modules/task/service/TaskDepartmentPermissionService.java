package com.cool.modules.task.service;

import com.mybatisflex.core.query.QueryWrapper;

import java.util.List;
import java.util.Map;

/**
 * 任务部门权限验证服务接口
 */
public interface TaskDepartmentPermissionService {
    
    /**
     * 验证用户是否有权限访问指定任务包
     * 
     * @param userId 用户ID
     * @param taskPackageId 任务包ID
     * @param operation 操作类型 (VIEW, EDIT, DELETE, ASSIGN等)
     * @return 是否有权限
     */
    boolean hasTaskPackagePermission(Long userId, Long taskPackageId, String operation);
    
    /**
     * 验证用户是否有权限访问指定任务信息
     * 
     * @param userId 用户ID
     * @param taskInfoId 任务信息ID
     * @param operation 操作类型
     * @return 是否有权限
     */
    boolean hasTaskInfoPermission(Long userId, Long taskInfoId, String operation);
    
    /**
     * 验证用户是否有权限访问指定任务执行
     * 
     * @param userId 用户ID
     * @param taskExecutionId 任务执行ID
     * @param operation 操作类型
     * @return 是否有权限
     */
    boolean hasTaskExecutionPermission(Long userId, Long taskExecutionId, String operation);
    
    /**
     * 获取用户有权限的部门ID列表
     * 
     * @param userId 用户ID
     * @return 部门ID数组
     */
    Long[] getUserDepartmentIds(Long userId);
    
    /**
     * 根据部门权限过滤任务包查询条件
     * 
     * @param queryWrapper 查询包装器
     * @param userId 用户ID
     */
    void applyTaskPackageDepartmentFilter(QueryWrapper queryWrapper, Long userId);
    
    /**
     * 根据部门权限过滤任务信息查询条件
     * 
     * @param queryWrapper 查询包装器
     * @param userId 用户ID
     */
    void applyTaskInfoDepartmentFilter(QueryWrapper queryWrapper, Long userId);
    
    /**
     * 根据部门权限过滤任务执行查询条件
     * 
     * @param queryWrapper 查询包装器
     * @param userId 用户ID
     */
    void applyTaskExecutionDepartmentFilter(QueryWrapper queryWrapper, Long userId);
    
    /**
     * 批量验证任务权限
     * 
     * @param userId 用户ID
     * @param taskType 任务类型
     * @param taskIds 任务ID列表
     * @param operation 操作类型
     * @return 权限结果映射 taskId -> hasPermission
     */
    Map<Long, Boolean> batchCheckPermissions(Long userId, String taskType, List<Long> taskIds, String operation);
    
    /**
     * 验证用户是否可以在指定部门创建任务
     * 
     * @param userId 用户ID
     * @param departmentId 部门ID
     * @return 是否有权限
     */
    boolean canCreateTaskInDepartment(Long userId, Long departmentId);
    
    /**
     * 验证用户是否可以将任务分配给指定执行人
     * 
     * @param userId 当前用户ID
     * @param assigneeId 被分配人ID
     * @return 是否有权限
     */
    boolean canAssignTaskToUser(Long userId, Long assigneeId);
    
    /**
     * 获取用户有权限的执行人列表
     * 
     * @param userId 用户ID
     * @return 执行人列表
     */
    List<Map<String, Object>> getAuthorizedAssignees(Long userId);
} 