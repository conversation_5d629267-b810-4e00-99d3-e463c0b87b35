package com.cool.modules.task.mapper;

import com.mybatisflex.core.BaseMapper;
import com.cool.modules.task.entity.TaskExecutionEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 任务分配执行Mapper
 */
@Mapper
public interface TaskAssignmentMapper extends BaseMapper<TaskExecutionEntity> {

    /**
     * 根据任务ID获取执行记录
     */
    List<TaskExecutionEntity> getByTaskId(@Param("taskId") Long taskId);

    /**
     * 根据执行人ID获取执行记录
     */
    List<TaskExecutionEntity> getByAssigneeId(@Param("assigneeId") Long assigneeId);

    /**
     * 检查任务是否已分配
     */
    Long countAssignedByTaskId(@Param("taskId") Long taskId);

    /**
     * 获取用户当前工作负载统计
     */
    Map<String, Object> getUserWorkloadStats(@Param("userId") Long userId);

    /**
     * 获取用户历史绩效统计
     */
    Map<String, Object> getUserPerformanceStats(@Param("userId") Long userId);
}
