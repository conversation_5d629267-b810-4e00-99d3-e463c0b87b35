package com.cool.modules.task.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 任务权限检查注解
 */
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
public @interface TaskPermissionCheck {
    
    /**
     * 操作类型
     */
    String operation() default "VIEW";
    
    /**
     * 任务类型
     */
    String taskType() default "package";
    
    /**
     * 任务ID参数名
     */
    String taskIdParam() default "id";
    
    /**
     * 是否必须检查权限
     */
    boolean required() default true;
    
    /**
     * 权限检查失败时的错误信息
     */
    String message() default "无权限访问此任务";
} 