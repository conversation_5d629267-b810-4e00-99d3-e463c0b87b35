package com.cool.modules.task.service.impl;

import com.cool.core.base.BaseServiceImpl;
import com.cool.modules.task.entity.TaskHistoryEntity;
import com.cool.modules.task.mapper.TaskHistoryMapper;
import com.cool.modules.task.service.TaskHistoryService;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * 任务历史记录服务实现
 */
@Service
public class TaskHistoryServiceImpl extends BaseServiceImpl<TaskHistoryMapper, TaskHistoryEntity>
        implements TaskHistoryService {

    @Override
    public void recordTaskHistory(Long taskId, String actionType, Integer oldStatus, Integer newStatus,
                                 Long actionBy, String actionByName, String note) {
        try {
            TaskHistoryEntity history = new TaskHistoryEntity();

            // 使用反射设置字段值，避免Lombok问题
            setFieldValue(history, "taskId", taskId);
            setFieldValue(history, "actionType", actionType);
            setFieldValue(history, "oldStatus", oldStatus);
            setFieldValue(history, "newStatus", newStatus);
            setFieldValue(history, "actionBy", actionBy);
            setFieldValue(history, "actionByName", actionByName);
            setFieldValue(history, "actionTime", new Date());
            setFieldValue(history, "note", note);

            save(history);
            System.out.println("记录任务历史成功，taskId: " + taskId + ", actionType: " + actionType);
        } catch (Exception e) {
            System.err.println("记录任务历史失败，taskId: " + taskId + ", actionType: " + actionType + ", error: " + e.getMessage());
        }
    }

    @Override
    public void recordTaskAction(Long taskId, String actionType, Long actionBy, String actionByName, String note) {
        recordTaskHistory(taskId, actionType, null, null, actionBy, actionByName, note);
    }

    private void setFieldValue(Object obj, String fieldName, Object value) {
        try {
            java.lang.reflect.Field field = obj.getClass().getDeclaredField(fieldName);
            field.setAccessible(true);
            field.set(obj, value);
        } catch (Exception e) {
            System.err.println("设置字段值失败，fieldName: " + fieldName + ", error: " + e.getMessage());
        }
    }
}
