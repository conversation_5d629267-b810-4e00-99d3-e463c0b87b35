package com.cool.modules.task.service.impl;

import com.cool.modules.task.dto.TaskCompletionRequest;
import com.cool.modules.task.dto.TaskCloseRequest;
import com.cool.modules.task.entity.TaskExecutionEntity;
import com.cool.modules.task.entity.TaskInfoEntity;
import com.cool.modules.task.enums.TaskBusinessStatusEnum;
import com.cool.modules.task.enums.TaskExecutionStatusEnum;
import com.cool.modules.task.service.TaskExecutionService;
import com.cool.modules.task.service.TaskInfoService;
import com.cool.modules.task.service.TaskStatusService;
import com.cool.modules.task.service.TaskHistoryService;
import com.cool.modules.task.service.TaskPermissionService;
import com.cool.modules.task.utils.ScheduleUtils;
import com.mybatisflex.core.query.QueryWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.quartz.Scheduler;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.List;

/**
 * 任务状态管理服务实现
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TaskStatusServiceImpl implements TaskStatusService {

    private final TaskExecutionService taskExecutionService;
    private final TaskInfoService taskInfoService;
    private final TaskHistoryService taskHistoryService;
    private final TaskPermissionService taskPermissionService;
    private final Scheduler scheduler;

    @Override
    @Transactional
    public Boolean completeTaskExecution(TaskCompletionRequest request) {
        try {
            // 1. 检查权限（这里需要传入当前用户ID，暂时使用assigneeId）
            if (!taskPermissionService.canCompleteTaskExecution(request.getTaskId(), request.getAssigneeId(), request.getAssigneeId())) {
                log.warn("用户无权限完成任务，taskId: {}, assigneeId: {}",
                    request.getTaskId(), request.getAssigneeId());
                return false;
            }

            // 2. 更新执行记录
            TaskExecutionEntity execution = taskExecutionService.getOne(QueryWrapper.create()
                    .eq("task_id", request.getTaskId())
                    .eq("assignee_id", request.getAssigneeId())
                    .in("execution_status", TaskExecutionStatusEnum.ASSIGNED.getCode(), TaskExecutionStatusEnum.IN_PROGRESS.getCode()));

            if (execution == null) {
                log.warn("未找到可完成的执行记录，taskId: {}, assigneeId: {}", 
                    request.getTaskId(), request.getAssigneeId());
                return false;
            }

            execution.setExecutionStatusEnum(TaskExecutionStatusEnum.COMPLETED);
            execution.setCompletionTime(new Date());
            execution.setCompletionNote(request.getCompletionNote());
            
            // 处理附件和照片
            if (!CollectionUtils.isEmpty(request.getAttachments())) {
                execution.setAttachments(String.join(",", request.getAttachments()));
            }
            if (!CollectionUtils.isEmpty(request.getPhotos())) {
                execution.setPhotos(String.join(",", request.getPhotos()));
            }

            taskExecutionService.updateById(execution);

            // 3. 检查是否所有执行人都完成，如果是则更新任务状态
            if (areAllExecutionsCompleted(request.getTaskId())) {
                TaskInfoEntity task = taskInfoService.getById(request.getTaskId());
                Integer oldStatus = task.getTaskStatus();
                updateTaskStatus(request.getTaskId(), TaskBusinessStatusEnum.COMPLETED.getCode());

                // 记录任务状态变更历史
                taskHistoryService.recordTaskHistory(
                    request.getTaskId(),
                    "COMPLETE",
                    oldStatus,
                    TaskBusinessStatusEnum.COMPLETED.getCode(),
                    request.getAssigneeId(),
                    execution.getAssigneeName(),
                    "所有执行人完成任务，任务状态自动更新为已完成"
                );
            }

            // 记录执行人完成操作
            taskHistoryService.recordTaskAction(
                request.getTaskId(),
                "EXECUTION_COMPLETE",
                request.getAssigneeId(),
                execution.getAssigneeName(),
                "执行人完成任务：" + (request.getCompletionNote() != null ? request.getCompletionNote() : "")
            );

            log.info("任务执行完成成功，taskId: {}, assigneeId: {}", 
                request.getTaskId(), request.getAssigneeId());
            return true;

        } catch (Exception e) {
            log.error("完成任务执行失败，taskId: {}, assigneeId: {}", 
                request.getTaskId(), request.getAssigneeId(), e);
            return false;
        }
    }

    @Override
    @Transactional
    public Boolean forceCompleteTask(Long taskId, String reason, Long operatorId) {
        try {
            // 检查权限
            if (!taskPermissionService.canForceCompleteTask(taskId, operatorId)) {
                log.warn("用户无权限强制完成任务，taskId: {}, operatorId: {}", taskId, operatorId);
                return false;
            }

            // 更新任务状态为已完成
            TaskInfoEntity task = taskInfoService.getById(taskId);
            if (task == null) {
                return false;
            }

            task.setTaskStatus(TaskBusinessStatusEnum.COMPLETED.getCode());
            task.setCompletionTime(new Date());
            taskInfoService.updateById(task);

            // 将所有未完成的执行记录标记为完成
            List<TaskExecutionEntity> executions = taskExecutionService.list(QueryWrapper.create()
                    .eq("task_id", taskId)
                    .notIn("execution_status", TaskExecutionStatusEnum.COMPLETED.getCode(), TaskExecutionStatusEnum.CANCELLED.getCode()));

            for (TaskExecutionEntity execution : executions) {
                execution.setExecutionStatusEnum(TaskExecutionStatusEnum.COMPLETED);
                execution.setCompletionTime(new Date());
                execution.setCompletionNote("管理员强制完成：" + reason);
                taskExecutionService.updateById(execution);
            }

            // 记录强制完成历史
            taskHistoryService.recordTaskHistory(
                taskId,
                "FORCE_COMPLETE",
                task.getTaskStatus(),
                TaskBusinessStatusEnum.COMPLETED.getCode(),
                operatorId,
                "管理员",
                "强制完成任务：" + reason
            );

            log.info("强制完成任务成功，taskId: {}, operatorId: {}, reason: {}",
                taskId, operatorId, reason);
            return true;

        } catch (Exception e) {
            log.error("强制完成任务失败，taskId: {}, operatorId: {}", taskId, operatorId, e);
            return false;
        }
    }

    @Override
    @Transactional
    public Boolean closeTask(TaskCloseRequest request) {
        try {
            // 检查权限
            if (!taskPermissionService.canCloseTask(request.getTaskId(), request.getOperatorId())) {
                log.warn("用户无权限关闭任务，taskId: {}, operatorId: {}",
                    request.getTaskId(), request.getOperatorId());
                return false;
            }

            TaskInfoEntity task = taskInfoService.getById(request.getTaskId());
            if (task == null) {
                return false;
            }

            // 更新任务状态为已关闭
            task.setTaskStatus(TaskBusinessStatusEnum.CLOSED.getCode());
            task.setCloseReason(request.getCloseReason());
            task.setClosedBy(request.getOperatorName());
            task.setCloseTime(new Date());

            // 停止任务调度
            task.setScheduleStatus(0);

            // 停止Quartz调度器中的任务
            try {
                ScheduleUtils.pauseJob(scheduler, request.getTaskId() + "");
                log.info("已停止任务调度，taskId: {}", request.getTaskId());
            } catch (Exception e) {
                log.warn("停止任务调度失败，taskId: {}, error: {}", request.getTaskId(), e.getMessage());
            }

            taskInfoService.updateById(task);

            // 记录关闭任务历史
            taskHistoryService.recordTaskHistory(
                request.getTaskId(),
                "CLOSE",
                TaskBusinessStatusEnum.COMPLETED.getCode(),
                TaskBusinessStatusEnum.CLOSED.getCode(),
                request.getOperatorId(),
                request.getOperatorName(),
                "关闭任务：" + request.getCloseReason()
            );

            log.info("关闭任务成功，taskId: {}, operatorId: {}, reason: {}",
                request.getTaskId(), request.getOperatorId(), request.getCloseReason());
            return true;

        } catch (Exception e) {
            log.error("关闭任务失败，taskId: {}, operatorId: {}", 
                request.getTaskId(), request.getOperatorId(), e);
            return false;
        }
    }

    @Override
    @Transactional
    public Boolean reopenTask(Long taskId, String reason, Long operatorId) {
        try {
            // 检查权限
            if (!taskPermissionService.canReopenTask(taskId, operatorId)) {
                log.warn("用户无权限重新开启任务，taskId: {}, operatorId: {}", taskId, operatorId);
                return false;
            }

            TaskInfoEntity task = taskInfoService.getById(taskId);
            if (task == null) {
                return false;
            }

            // 重新开启任务，状态设为执行中
            task.setTaskStatus(TaskBusinessStatusEnum.EXECUTING.getCode());
            task.setCloseReason(null);
            task.setClosedBy(null);
            task.setCloseTime(null);
            task.setCompletionTime(null);

            taskInfoService.updateById(task);

            // 记录重新开启任务历史
            taskHistoryService.recordTaskHistory(
                taskId,
                "REOPEN",
                TaskBusinessStatusEnum.CLOSED.getCode(),
                TaskBusinessStatusEnum.EXECUTING.getCode(),
                operatorId,
                "管理员",
                "重新开启任务：" + reason
            );

            log.info("重新开启任务成功，taskId: {}, operatorId: {}, reason: {}",
                taskId, operatorId, reason);
            return true;

        } catch (Exception e) {
            log.error("重新开启任务失败，taskId: {}, operatorId: {}", taskId, operatorId, e);
            return false;
        }
    }

    @Override
    public Boolean canCompleteTask(Long taskId, Long assigneeId) {
        // 使用权限服务检查（这里假设当前用户就是assigneeId）
        return taskPermissionService.canCompleteTaskExecution(taskId, assigneeId, assigneeId);
    }

    @Override
    public Boolean canCloseTask(Long taskId, Long operatorId) {
        // 使用权限服务检查
        return taskPermissionService.canCloseTask(taskId, operatorId);
    }

    @Override
    public Boolean areAllExecutionsCompleted(Long taskId) {
        // 检查是否所有执行记录都已完成
        List<TaskExecutionEntity> executions = taskExecutionService.list(QueryWrapper.create()
                .eq("task_id", taskId)
                .notIn("execution_status", TaskExecutionStatusEnum.CANCELLED.getCode()));

        if (executions.isEmpty()) {
            return false;
        }

        return executions.stream()
                .allMatch(execution -> TaskExecutionStatusEnum.COMPLETED.getCode().equals(execution.getExecutionStatus()));
    }

    @Override
    @Transactional
    public Integer batchForceCompleteTask(List<Long> taskIds, String reason, Long operatorId) {
        int successCount = 0;
        for (Long taskId : taskIds) {
            try {
                if (forceCompleteTask(taskId, reason, operatorId)) {
                    successCount++;
                }
            } catch (Exception e) {
                log.error("批量强制完成任务失败，taskId: {}", taskId, e);
            }
        }
        return successCount;
    }

    @Override
    @Transactional
    public Integer batchCloseTask(List<Long> taskIds, String reason, Long operatorId) {
        int successCount = 0;
        for (Long taskId : taskIds) {
            try {
                TaskCloseRequest request = new TaskCloseRequest();
                request.setTaskId(taskId);
                request.setCloseReason(reason);
                request.setOperatorId(operatorId);
                request.setOperatorName("批量操作");

                if (closeTask(request)) {
                    successCount++;
                }
            } catch (Exception e) {
                log.error("批量关闭任务失败，taskId: {}", taskId, e);
            }
        }
        return successCount;
    }

    @Override
    @Transactional
    public Integer batchReopenTask(List<Long> taskIds, String reason, Long operatorId) {
        int successCount = 0;
        for (Long taskId : taskIds) {
            try {
                if (reopenTask(taskId, reason, operatorId)) {
                    successCount++;
                }
            } catch (Exception e) {
                log.error("批量重新开启任务失败，taskId: {}", taskId, e);
            }
        }
        return successCount;
    }

    /**
     * 更新任务状态
     */
    private void updateTaskStatus(Long taskId, Integer status) {
        TaskInfoEntity task = taskInfoService.getById(taskId);
        if (task != null) {
            task.setTaskStatus(status);
            if (TaskBusinessStatusEnum.COMPLETED.getCode().equals(status)) {
                task.setCompletionTime(new Date());
            }
            taskInfoService.updateById(task);
        }
    }
}
