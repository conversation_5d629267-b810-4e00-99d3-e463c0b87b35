package com.cool.modules.task.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 任务分配策略枚举
 */
@Getter
@AllArgsConstructor
public enum AssignmentStrategyEnum {
    
    /**
     * AI自动分配策略
     */
    AI_AUTO("AI_AUTO", "AI自动分配"),
    
    /**
     * 手动分配策略
     */
    MANUAL("MANUAL", "手动分配"),
    
    /**
     * 负载均衡策略
     */
    LOAD_BALANCE("LOAD_BALANCE", "负载均衡"),
    
    /**
     * 技能匹配策略
     */
    SKILL_MATCH("SKILL_MATCH", "技能匹配"),
    
    /**
     * 优先级策略
     */
    PRIORITY_BASED("PRIORITY_BASED", "优先级策略"),
    
    /**
     * 地理位置策略
     */
    GEOGRAPHIC("GEOGRAPHIC", "地理位置策略");

    private final String code;
    private final String name;

    /**
     * 根据代码获取枚举
     */
    public static AssignmentStrategyEnum getByCode(String code) {
        if (code == null || code.isEmpty()) return null;
        for (AssignmentStrategyEnum strategy : values()) {
            if (strategy.getCode().equals(code)) {
                return strategy;
            }
        }
        return null;
    }

    /**
     * 根据代码获取名称
     */
    public static String getNameByCode(String code) {
        AssignmentStrategyEnum strategy = getByCode(code);
        return strategy != null ? strategy.getName() : "未知";
    }
    
    /**
     * 检查是否为有效的分配策略
     */
    public static boolean isValidStrategy(String code) {
        return getByCode(code) != null;
    }
} 