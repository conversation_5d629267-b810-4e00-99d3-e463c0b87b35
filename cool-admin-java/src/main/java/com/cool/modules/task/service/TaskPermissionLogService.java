package com.cool.modules.task.service;

import com.cool.core.base.BaseService;
import com.cool.modules.task.entity.TaskPermissionLogEntity;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 任务权限日志服务接口
 */
public interface TaskPermissionLogService extends BaseService<TaskPermissionLogEntity> {

    /**
     * 记录权限检查日志
     * 
     * @param userId 用户ID
     * @param username 用户名
     * @param operationType 操作类型
     * @param taskType 任务类型
     * @param taskId 任务ID
     * @param departmentId 部门ID
     * @param hasPermission 权限检查结果
     * @param clientIp 客户端IP
     * @param userAgent 用户代理
     */
    void logPermissionCheck(Long userId, String username, String operationType, 
                           String taskType, Long taskId, Long departmentId, 
                           boolean hasPermission, String clientIp, String userAgent);

    /**
     * 获取用户权限操作统计
     * 
     * @param userId 用户ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 统计结果
     */
    List<Map<String, Object>> getUserPermissionStats(Long userId, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 获取部门权限操作统计
     * 
     * @param departmentId 部门ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 统计结果
     */
    Map<String, Object> getDepartmentPermissionStats(Long departmentId, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 获取权限违规操作记录
     * 
     * @param startTime 开始时间
     * @param limit 限制数量
     * @return 违规记录列表
     */
    List<TaskPermissionLogEntity> getViolationLogs(LocalDateTime startTime, Integer limit);

    /**
     * 清理过期日志
     * 
     * @param expireTime 过期时间
     * @return 清理的记录数
     */
    int cleanExpiredLogs(LocalDateTime expireTime);
} 