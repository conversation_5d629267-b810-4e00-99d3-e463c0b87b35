package com.cool.modules.task.service.impl;

import com.cool.core.util.CoolSecurityUtil;
import com.cool.modules.base.entity.sys.BaseSysUserEntity;
import com.cool.modules.base.service.sys.BaseSysPermsService;
import com.cool.modules.base.service.sys.BaseSysUserService;
import com.cool.modules.task.entity.TaskExecutionEntity;
import com.cool.modules.task.entity.TaskInfoEntity;
import com.cool.modules.task.entity.TaskPackageEntity;
import com.cool.modules.task.service.TaskDepartmentPermissionService;
import com.cool.modules.task.service.TaskExecutionService;
import com.cool.modules.task.service.TaskInfoService;
import com.cool.modules.task.service.TaskPackageService;
import com.cool.modules.task.service.TaskPermissionLogService;
import com.mybatisflex.core.query.QueryWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 任务部门权限验证服务实现
 */
@Service
@Slf4j
public class TaskDepartmentPermissionServiceImpl implements TaskDepartmentPermissionService {
    
    private final BaseSysPermsService baseSysPermsService;
    private final TaskPackageService taskPackageService;
    private final TaskInfoService taskInfoService;
    private final TaskExecutionService taskExecutionService;
    private final TaskPermissionLogService taskPermissionLogService;
    private final BaseSysUserService baseSysUserService;
    
    public TaskDepartmentPermissionServiceImpl(
            BaseSysPermsService baseSysPermsService,
            @Lazy TaskPackageService taskPackageService,
            @Lazy TaskInfoService taskInfoService,
            @Lazy TaskExecutionService taskExecutionService,
            TaskPermissionLogService taskPermissionLogService,
            BaseSysUserService baseSysUserService) {
        this.baseSysPermsService = baseSysPermsService;
        this.taskPackageService = taskPackageService;
        this.taskInfoService = taskInfoService;
        this.taskExecutionService = taskExecutionService;
        this.taskPermissionLogService = taskPermissionLogService;
        this.baseSysUserService = baseSysUserService;
    }
    
    @Override
    public boolean hasTaskPackagePermission(Long userId, Long taskPackageId, String operation) {
        try {
            // 1. 获取用户的部门权限
            Long[] userDepartmentIds = getUserDepartmentIds(userId);
            
            // 2. 获取任务包的部门信息
            TaskPackageEntity taskPackage = taskPackageService.getById(taskPackageId);
            if (taskPackage == null) {
                logPermissionCheck(userId, operation, "package", taskPackageId, null, false);
                return false;
            }
            
            // 3. 验证权限
            boolean hasPermission = Arrays.asList(userDepartmentIds)
                .contains(taskPackage.getDepartmentId());
            
            // 4. 记录权限验证日志
            logPermissionCheck(userId, operation, "package", taskPackageId, 
                taskPackage.getDepartmentId(), hasPermission);
            
            return hasPermission;
            
        } catch (Exception e) {
            log.error("验证任务包权限失败: userId={}, taskPackageId={}, operation={}", 
                     userId, taskPackageId, operation, e);
            return false;
        }
    }
    
    @Override
    public boolean hasTaskInfoPermission(Long userId, Long taskInfoId, String operation) {
        try {
            Long[] userDepartmentIds = getUserDepartmentIds(userId);
            
            TaskInfoEntity taskInfo = taskInfoService.getById(taskInfoId);
            if (taskInfo == null) {
                logPermissionCheck(userId, operation, "info", taskInfoId, null, false);
                return false;
            }
            
            boolean hasPermission = Arrays.asList(userDepartmentIds)
                .contains(taskInfo.getDepartmentId());
            
            logPermissionCheck(userId, operation, "info", taskInfoId, 
                taskInfo.getDepartmentId(), hasPermission);
            
            return hasPermission;
            
        } catch (Exception e) {
            log.error("验证任务信息权限失败: userId={}, taskInfoId={}, operation={}", 
                     userId, taskInfoId, operation, e);
            return false;
        }
    }
    
    @Override
    public boolean hasTaskExecutionPermission(Long userId, Long taskExecutionId, String operation) {
        try {
            Long[] userDepartmentIds = getUserDepartmentIds(userId);
            
            TaskExecutionEntity taskExecution = taskExecutionService.getById(taskExecutionId);
            if (taskExecution == null) {
                logPermissionCheck(userId, operation, "execution", taskExecutionId, null, false);
                return false;
            }
            
            // 执行人可以操作自己的任务执行记录
            if (taskExecution.getAssigneeId().equals(userId)) {
                logPermissionCheck(userId, operation, "execution", taskExecutionId, 
                    taskExecution.getDepartmentId(), true);
                return true;
            }
            
            boolean hasPermission = Arrays.asList(userDepartmentIds)
                .contains(taskExecution.getDepartmentId());
            
            logPermissionCheck(userId, operation, "execution", taskExecutionId, 
                taskExecution.getDepartmentId(), hasPermission);
            
            return hasPermission;
            
        } catch (Exception e) {
            log.error("验证任务执行权限失败: userId={}, taskExecutionId={}, operation={}", 
                     userId, taskExecutionId, operation, e);
            return false;
        }
    }
    
    @Override
    public Long[] getUserDepartmentIds(Long userId) {
        // 复用现有的部门权限服务
        return baseSysPermsService.loginDepartmentIds();
    }
    
    @Override
    public void applyTaskPackageDepartmentFilter(QueryWrapper queryWrapper, Long userId) {
        Long[] departmentIds = getUserDepartmentIds(userId);
        String currentUser = CoolSecurityUtil.getAdminUsername();
        
        // admin用户不需要部门权限过滤
        if (!"admin".equals(currentUser) && departmentIds != null && departmentIds.length > 0) {
            queryWrapper.in("department_id", Arrays.asList(departmentIds));
        }
    }
    
    @Override
    public void applyTaskInfoDepartmentFilter(QueryWrapper queryWrapper, Long userId) {
        Long[] departmentIds = getUserDepartmentIds(userId);
        String currentUser = CoolSecurityUtil.getAdminUsername();
        
        if (!"admin".equals(currentUser) && departmentIds != null && departmentIds.length > 0) {
            queryWrapper.in("department_id", Arrays.asList(departmentIds));
        }
    }
    
    @Override
    public void applyTaskExecutionDepartmentFilter(QueryWrapper queryWrapper, Long userId) {
        Long[] departmentIds = getUserDepartmentIds(userId);
        String currentUser = CoolSecurityUtil.getAdminUsername();
        
        if (!"admin".equals(currentUser) && departmentIds != null && departmentIds.length > 0) {
            // 简化查询：只过滤部门权限，执行人权限在业务层处理
            queryWrapper.in("department_id", Arrays.asList(departmentIds));
        }
    }
    
    @Override
    public Map<Long, Boolean> batchCheckPermissions(Long userId, String taskType, List<Long> taskIds, String operation) {
        Map<Long, Boolean> result = new HashMap<>();
        
        for (Long taskId : taskIds) {
            boolean hasPermission = false;
            switch (taskType.toLowerCase()) {
                case "package":
                    hasPermission = hasTaskPackagePermission(userId, taskId, operation);
                    break;
                case "info":
                    hasPermission = hasTaskInfoPermission(userId, taskId, operation);
                    break;
                case "execution":
                    hasPermission = hasTaskExecutionPermission(userId, taskId, operation);
                    break;
                default:
                    log.warn("未知的任务类型: {}", taskType);
            }
            result.put(taskId, hasPermission);
        }
        
        return result;
    }
    
    @Override
    public boolean canCreateTaskInDepartment(Long userId, Long departmentId) {
        Long[] userDepartmentIds = getUserDepartmentIds(userId);
        return Arrays.asList(userDepartmentIds).contains(departmentId);
    }
    
    @Override
    public boolean canAssignTaskToUser(Long userId, Long assigneeId) {
        try {
            // 获取被分配人的部门信息
            BaseSysUserEntity assignee = baseSysUserService.getById(assigneeId);
            if (assignee == null) {
                return false;
            }
            
            // 检查被分配人的部门是否在当前用户的权限范围内
            Long[] userDepartmentIds = getUserDepartmentIds(userId);
            return Arrays.asList(userDepartmentIds).contains(assignee.getDepartmentId());
            
        } catch (Exception e) {
            log.error("检查任务分配权限失败: userId={}, assigneeId={}", userId, assigneeId, e);
            return false;
        }
    }
    
    @Override
    public List<Map<String, Object>> getAuthorizedAssignees(Long userId) {
        try {
            Long[] userDepartmentIds = getUserDepartmentIds(userId);
            
            // 构建查询条件
            QueryWrapper queryWrapper = QueryWrapper.create()
                .eq("status", 1) // 只获取启用的用户
                .in("department_id", Arrays.asList(userDepartmentIds));
            
            // 查询用户列表并转换为Map
            List<BaseSysUserEntity> users = baseSysUserService.list(queryWrapper);
            return users.stream().map(user -> {
                Map<String, Object> userMap = new HashMap<>();
                userMap.put("id", user.getId());
                userMap.put("username", user.getUsername());
                userMap.put("name", user.getName());
                userMap.put("departmentId", user.getDepartmentId());
                return userMap;
            }).toList();
            
        } catch (Exception e) {
            log.error("获取授权执行人列表失败: userId={}", userId, e);
            return List.of();
        }
    }
    
    private void logPermissionCheck(Long userId, String operation, String taskType, 
            Long taskId, Long departmentId, boolean result) {
        try {
            String username = CoolSecurityUtil.getAdminUsername();
            String clientIp = "unknown";  // 简化IP获取
            String userAgent = "unknown"; // 简化UserAgent获取
            
            taskPermissionLogService.logPermissionCheck(userId, username, operation, 
                taskType, taskId, departmentId, result, clientIp, userAgent);
                
        } catch (Exception e) {
            log.error("记录权限检查日志失败", e);
        }
    }
} 