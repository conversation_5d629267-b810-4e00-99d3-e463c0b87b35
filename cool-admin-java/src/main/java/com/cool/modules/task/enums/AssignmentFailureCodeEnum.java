package com.cool.modules.task.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 任务分配失败代码枚举
 */
@Getter
@AllArgsConstructor
public enum AssignmentFailureCodeEnum {
    
    /**
     * 任务不存在
     */
    TASK_NOT_FOUND("TASK_NOT_FOUND", "任务不存在"),
    
    /**
     * 没有找到候选人
     */
    NO_CANDIDATES("NO_CANDIDATES", "没有找到候选人"),
    
    /**
     * 没有合适的执行人
     */
    NO_SUITABLE_ASSIGNEE("NO_SUITABLE_ASSIGNEE", "没有合适的执行人"),
    
    /**
     * 工作负载过高
     */
    WORKLOAD_TOO_HIGH("WORKLOAD_TOO_HIGH", "工作负载过高"),
    
    /**
     * 技能不匹配
     */
    SKILL_MISMATCH("SKILL_MISMATCH", "技能不匹配"),
    
    /**
     * 地理位置不匹配
     */
    LOCATION_MISMATCH("LOCATION_MISMATCH", "地理位置不匹配"),
    
    /**
     * 时间冲突
     */
    TIME_CONFLICT("TIME_CONFLICT", "时间冲突"),
    
    /**
     * 权限不足
     */
    INSUFFICIENT_PERMISSION("INSUFFICIENT_PERMISSION", "权限不足"),
    
    /**
     * 分配记录创建失败
     */
    ASSIGNMENT_CREATION_FAILED("ASSIGNMENT_CREATION_FAILED", "分配记录创建失败"),
    
    /**
     * 系统错误
     */
    SYSTEM_ERROR("SYSTEM_ERROR", "系统错误");

    private final String code;
    private final String name;

    /**
     * 根据代码获取枚举
     */
    public static AssignmentFailureCodeEnum getByCode(String code) {
        if (code == null || code.isEmpty()) return null;
        for (AssignmentFailureCodeEnum failureCode : values()) {
            if (failureCode.getCode().equals(code)) {
                return failureCode;
            }
        }
        return null;
    }

    /**
     * 根据代码获取名称
     */
    public static String getNameByCode(String code) {
        AssignmentFailureCodeEnum failureCode = getByCode(code);
        return failureCode != null ? failureCode.getName() : "未知错误";
    }
    
    /**
     * 检查是否为有效的失败代码
     */
    public static boolean isValidCode(String code) {
        return getByCode(code) != null;
    }
} 