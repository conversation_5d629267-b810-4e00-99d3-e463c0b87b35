package com.cool.modules.task.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 任务完成请求DTO
 */
@Data
@Schema(description = "任务完成请求")
public class TaskCompletionRequest {

    @Schema(description = "任务ID", required = true)
    private Long taskId;

    @Schema(description = "执行人ID", required = true)
    private Long assigneeId;

    @Schema(description = "完成说明")
    private String completionNote;

    @Schema(description = "附件列表")
    private List<String> attachments;

    @Schema(description = "照片列表")
    private List<String> photos;
}
