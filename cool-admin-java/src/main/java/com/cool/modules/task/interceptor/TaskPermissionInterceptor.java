package com.cool.modules.task.interceptor;

import com.cool.core.exception.CoolPreconditions;
import com.cool.core.util.CoolSecurityUtil;
import com.cool.modules.task.annotation.TaskPermissionCheck;
import com.cool.modules.task.service.TaskDepartmentPermissionService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.lang.reflect.Parameter;
import java.util.Map;

/**
 * 任务权限拦截器
 */
@Aspect
@Component
@RequiredArgsConstructor
@Slf4j
public class TaskPermissionInterceptor {
    
    private final TaskDepartmentPermissionService departmentPermissionService;
    
    @Before("@annotation(com.cool.modules.task.annotation.TaskPermissionCheck)")
    public void checkTaskPermission(JoinPoint joinPoint) {
        try {
            // 获取当前用户ID
            Long currentUserId = CoolSecurityUtil.getCurrentUserId();
            if (currentUserId == null) {
                log.warn("用户未登录，跳过权限检查");
                return;
            }
            
            // 获取注解信息
            MethodSignature signature = (MethodSignature) joinPoint.getSignature();
            Method method = signature.getMethod();
            TaskPermissionCheck annotation = method.getAnnotation(TaskPermissionCheck.class);
            
            if (!annotation.required()) {
                return;
            }
            
            // 获取方法参数
            Object[] args = joinPoint.getArgs();
            Parameter[] parameters = method.getParameters();
            
            // 查找任务ID参数
            Long taskId = null;
            String taskIdParam = annotation.taskIdParam();
            
            for (int i = 0; i < parameters.length; i++) {
                Parameter parameter = parameters[i];
                Object arg = args[i];
                
                // 如果参数名匹配或者参数类型是Long且名称包含id
                if (parameter.getName().equals(taskIdParam) || 
                    (parameter.getType() == Long.class && parameter.getName().contains("Id"))) {
                    taskId = (Long) arg;
                    break;
                }
                
                // 如果参数是Map类型，尝试从中获取任务ID
                if (arg instanceof Map) {
                    Map<?, ?> map = (Map<?, ?>) arg;
                    if (map.containsKey(taskIdParam)) {
                        Object idValue = map.get(taskIdParam);
                        if (idValue instanceof Long) {
                            taskId = (Long) idValue;
                        } else if (idValue instanceof String) {
                            try {
                                taskId = Long.parseLong((String) idValue);
                            } catch (NumberFormatException e) {
                                log.warn("无法解析任务ID: {}", idValue);
                            }
                        }
                        break;
                    }
                }
            }
            
            if (taskId == null) {
                log.warn("无法获取任务ID，跳过权限检查: method={}, param={}", 
                        method.getName(), taskIdParam);
                return;
            }
            
            // 执行权限检查
            boolean hasPermission = false;
            String taskType = annotation.taskType();
            String operation = annotation.operation();
            
            switch (taskType.toLowerCase()) {
                case "package":
                    hasPermission = departmentPermissionService.hasTaskPackagePermission(
                        currentUserId, taskId, operation);
                    break;
                case "info":
                    hasPermission = departmentPermissionService.hasTaskInfoPermission(
                        currentUserId, taskId, operation);
                    break;
                case "execution":
                    hasPermission = departmentPermissionService.hasTaskExecutionPermission(
                        currentUserId, taskId, operation);
                    break;
                default:
                    log.warn("未知的任务类型: {}", taskType);
                    return;
            }
            
            // 权限检查失败，抛出异常
            if (!hasPermission) {
                log.warn("用户权限检查失败: userId={}, taskType={}, taskId={}, operation={}", 
                        currentUserId, taskType, taskId, operation);
                CoolPreconditions.check(true, annotation.message());
            }
            
        } catch (Exception e) {
            if (e instanceof RuntimeException) {
                throw e;
            }
            log.error("权限检查过程中发生异常", e);
            throw new RuntimeException("权限检查失败: " + e.getMessage());
        }
    }
} 