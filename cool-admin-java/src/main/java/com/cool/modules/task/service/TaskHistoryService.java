package com.cool.modules.task.service;

import com.cool.core.base.BaseService;
import com.cool.modules.task.entity.TaskHistoryEntity;

/**
 * 任务历史记录服务接口
 */
public interface TaskHistoryService extends BaseService<TaskHistoryEntity> {

    /**
     * 记录任务状态变更历史
     * @param taskId 任务ID
     * @param actionType 操作类型
     * @param oldStatus 原状态
     * @param newStatus 新状态
     * @param actionBy 操作人ID
     * @param actionByName 操作人姓名
     * @param note 操作说明
     */
    void recordTaskHistory(Long taskId, String actionType, Integer oldStatus, Integer newStatus, 
                          Long actionBy, String actionByName, String note);

    /**
     * 记录任务操作历史（不涉及状态变更）
     * @param taskId 任务ID
     * @param actionType 操作类型
     * @param actionBy 操作人ID
     * @param actionByName 操作人姓名
     * @param note 操作说明
     */
    void recordTaskAction(Long taskId, String actionType, Long actionBy, String actionByName, String note);
}
