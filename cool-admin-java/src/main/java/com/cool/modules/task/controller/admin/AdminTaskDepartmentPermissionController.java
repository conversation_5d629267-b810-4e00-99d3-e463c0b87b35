package com.cool.modules.task.controller.admin;

import com.cool.core.annotation.CoolRestController;
import com.cool.core.request.R;
import com.cool.core.util.CoolSecurityUtil;
import com.cool.modules.task.service.TaskDepartmentPermissionService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 任务部门权限管理控制器
 */
@Tag(name = "任务部门权限管理", description = "任务系统的部门权限控制")
@CoolRestController
@RequiredArgsConstructor
public class AdminTaskDepartmentPermissionController {
    
    private final TaskDepartmentPermissionService departmentPermissionService;
    
    @Operation(summary = "检查任务权限", description = "检查用户是否有权限访问指定任务")
    @PostMapping("/check-permission")
    public R<Boolean> checkPermission(@RequestBody Map<String, Object> params) {
        try {
            Long userId = CoolSecurityUtil.getCurrentUserId();
            String taskType = (String) params.get("taskType");
            Long taskId = Long.valueOf(params.get("taskId").toString());
            String operation = (String) params.get("operation");
            
            boolean hasPermission = false;
            switch (taskType.toLowerCase()) {
                case "package":
                    hasPermission = departmentPermissionService.hasTaskPackagePermission(userId, taskId, operation);
                    break;
                case "info":
                    hasPermission = departmentPermissionService.hasTaskInfoPermission(userId, taskId, operation);
                    break;
                case "execution":
                    hasPermission = departmentPermissionService.hasTaskExecutionPermission(userId, taskId, operation);
                    break;
                default:
                    return R.error("不支持的任务类型: " + taskType);
            }
            
            return R.ok(hasPermission);
            
        } catch (Exception e) {
            return R.error("权限检查失败: " + e.getMessage());
        }
    }
    
    @Operation(summary = "批量检查任务权限", description = "批量检查多个任务的权限")
    @PostMapping("/batch-check-permissions")
    public R<Map<Long, Boolean>> batchCheckPermissions(@RequestBody Map<String, Object> params) {
        try {
            Long userId = CoolSecurityUtil.getCurrentUserId();
            String taskType = (String) params.get("taskType");
            List<Long> taskIds = (List<Long>) params.get("taskIds");
            String operation = (String) params.get("operation");
            
            Map<Long, Boolean> result = departmentPermissionService.batchCheckPermissions(
                userId, taskType, taskIds, operation);
            
            return R.ok(result);
            
        } catch (Exception e) {
            return R.error("批量权限检查失败: " + e.getMessage());
        }
    }
    
    @Operation(summary = "获取用户有权限的部门列表", description = "获取当前用户有权限管理的部门")
    @GetMapping("/authorized-departments")
    public R<List<Map<String, Object>>> getAuthorizedDepartments() {
        try {
            Long userId = CoolSecurityUtil.getCurrentUserId();
            Long[] departmentIds = departmentPermissionService.getUserDepartmentIds(userId);
            
            // 这里应该查询部门详细信息，简化返回
            return R.ok(List.of());
            
        } catch (Exception e) {
            return R.error("获取授权部门失败: " + e.getMessage());
        }
    }
    
    @Operation(summary = "获取用户有权限的执行人列表", description = "获取当前用户有权限分配的执行人")
    @GetMapping("/authorized-assignees")
    public R<List<Map<String, Object>>> getAuthorizedAssignees() {
        try {
            Long userId = CoolSecurityUtil.getCurrentUserId();
            List<Map<String, Object>> assignees = departmentPermissionService.getAuthorizedAssignees(userId);
            
            return R.ok(assignees);
            
        } catch (Exception e) {
            return R.error("获取授权执行人失败: " + e.getMessage());
        }
    }
    
    @Operation(summary = "检查创建权限", description = "检查用户是否可以在指定部门创建任务")
    @PostMapping("/check-create-permission")
    public R<Boolean> checkCreatePermission(@RequestBody Map<String, Object> params) {
        try {
            Long userId = CoolSecurityUtil.getCurrentUserId();
            Long departmentId = Long.valueOf(params.get("departmentId").toString());
            
            boolean canCreate = departmentPermissionService.canCreateTaskInDepartment(userId, departmentId);
            
            return R.ok(canCreate);
            
        } catch (Exception e) {
            return R.error("创建权限检查失败: " + e.getMessage());
        }
    }
    
    @Operation(summary = "检查分配权限", description = "检查用户是否可以将任务分配给指定执行人")
    @PostMapping("/check-assign-permission")
    public R<Boolean> checkAssignPermission(@RequestBody Map<String, Object> params) {
        try {
            Long userId = CoolSecurityUtil.getCurrentUserId();
            Long assigneeId = Long.valueOf(params.get("assigneeId").toString());
            
            boolean canAssign = departmentPermissionService.canAssignTaskToUser(userId, assigneeId);
            
            return R.ok(canAssign);
            
        } catch (Exception e) {
            return R.error("分配权限检查失败: " + e.getMessage());
        }
    }
    
    @Operation(summary = "获取权限状态概览", description = "获取当前用户的权限状态概览")
    @GetMapping("/permission-overview")
    public R<Map<String, Object>> getPermissionOverview() {
        try {
            Long userId = CoolSecurityUtil.getCurrentUserId();
            Long[] departmentIds = departmentPermissionService.getUserDepartmentIds(userId);
            
            Map<String, Object> overview = new HashMap<>();
            overview.put("userId", userId);
            overview.put("departmentCount", departmentIds != null ? departmentIds.length : 0);
            overview.put("departmentIds", departmentIds);
            overview.put("isAdmin", "admin".equals(CoolSecurityUtil.getAdminUsername()));
            
            return R.ok(overview);
            
        } catch (Exception e) {
            return R.error("获取权限概览失败: " + e.getMessage());
        }
    }
} 