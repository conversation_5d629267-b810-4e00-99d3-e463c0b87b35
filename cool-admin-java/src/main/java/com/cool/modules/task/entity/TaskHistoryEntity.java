package com.cool.modules.task.entity;

import com.cool.core.base.BaseEntity;
import com.mybatisflex.annotation.Table;
import com.tangzc.mybatisflex.autotable.annotation.ColumnDefine;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * 任务历史记录实体
 */
@Getter
@Setter
@Table(value = "task_history", comment = "任务历史记录")
public class TaskHistoryEntity extends BaseEntity<TaskHistoryEntity> {

    @ColumnDefine(comment = "任务ID", notNull = true)
    private Long taskId;

    @ColumnDefine(comment = "操作类型", length = 50, notNull = true) // COMPLETE, CLOSE, REOPEN, ASSIGN, etc.
    private String actionType;

    @ColumnDefine(comment = "操作人ID")
    private Long actionBy;

    @ColumnDefine(comment = "操作人姓名", length = 100)
    private String actionByName;

    @ColumnDefine(comment = "操作时间", notNull = true)
    private Date actionTime;

    @ColumnDefine(comment = "原状态", type = "tinyint")
    private Integer oldStatus;

    @ColumnDefine(comment = "新状态", type = "tinyint")
    private Integer newStatus;

    @ColumnDefine(comment = "操作说明", type = "text")
    private String note;

    @ColumnDefine(comment = "额外数据(JSON)", type = "text")
    private String extraData;
}
