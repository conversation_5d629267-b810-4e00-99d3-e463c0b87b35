package com.cool.modules.organization.mapper;

import com.cool.modules.organization.entity.UserOrganizationEntity;
import com.mybatisflex.core.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 用户组织关系Mapper
 * 
 * <AUTHOR> Admin
 * @since 2025-01-17
 */
@Mapper
public interface UserOrganizationMapper extends BaseMapper<UserOrganizationEntity> {
    
    /**
     * 根据用户ID和组织类型获取组织关系
     * 
     * @param userId 用户ID
     * @param organizationType 组织类型
     * @return 组织关系列表
     */
    List<UserOrganizationEntity> getByUserIdAndType(@Param("userId") Long userId, 
                                                    @Param("organizationType") String organizationType);
    
    /**
     * 根据组织ID和组织类型获取用户关系
     * 
     * @param organizationId 组织ID
     * @param organizationType 组织类型
     * @return 用户关系列表
     */
    List<UserOrganizationEntity> getByOrganizationIdAndType(@Param("organizationId") Long organizationId, 
                                                            @Param("organizationType") String organizationType);
    
    /**
     * 检查用户是否在指定组织中
     * 
     * @param userId 用户ID
     * @param organizationId 组织ID
     * @param organizationType 组织类型
     * @return 是否存在
     */
    boolean existsByUserAndOrganization(@Param("userId") Long userId, 
                                       @Param("organizationId") Long organizationId, 
                                       @Param("organizationType") String organizationType);
    
    /**
     * 获取用户在指定组织中的角色
     * 
     * @param userId 用户ID
     * @param organizationId 组织ID
     * @param organizationType 组织类型
     * @return 角色代码
     */
    String getUserRoleInOrganization(@Param("userId") Long userId, 
                                    @Param("organizationId") Long organizationId, 
                                    @Param("organizationType") String organizationType);
    
    /**
     * 批量删除用户组织关系
     * 
     * @param userId 用户ID
     * @param organizationIds 组织ID列表
     * @param organizationType 组织类型
     * @return 影响行数
     */
    int batchDeleteByUserAndOrganizations(@Param("userId") Long userId, 
                                         @Param("organizationIds") List<Long> organizationIds, 
                                         @Param("organizationType") String organizationType);
    
    /**
     * 获取用户的最高项目角色
     * 
     * @param userId 用户ID
     * @return 最高角色代码
     */
    String getHighestProjectRole(@Param("userId") Long userId);
}
