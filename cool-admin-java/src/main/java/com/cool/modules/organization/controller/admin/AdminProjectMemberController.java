package com.cool.modules.organization.controller.admin;

import java.util.List;
import java.util.Map;

import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;

import com.cool.core.annotation.CoolRestController;
import com.cool.core.request.R;
import com.cool.core.util.CoolSecurityUtil;
import com.cool.modules.organization.dto.ProjectMemberDTO;
import com.cool.modules.organization.enums.GlobalProjectRoleEnum;
import com.cool.modules.organization.enums.OrganizationModeEnum;
import com.cool.modules.organization.service.ProjectInfoService;
import com.cool.modules.organization.service.UserOrganizationService;

import cn.hutool.core.lang.Dict;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;

/**
 * 项目成员管理控制器
 * 
 * <AUTHOR> Admin
 * @since 2025-01-17
 */
@Tag(name = "项目成员管理", description = "项目成员增删改查、角色分配等功能")
@CoolRestController
@RequiredArgsConstructor
public class AdminProjectMemberController {
    
    private final UserOrganizationService userOrganizationService;
    private final ProjectInfoService projectInfoService;
    
    @GetMapping("/list/{projectId}")
    @Operation(summary = "获取项目成员列表")
    public R<Object> getProjectMembers(@PathVariable Long projectId) {
        // 检查项目访问权限
        Long userId = CoolSecurityUtil.getCurrentUserId();
        if (!projectInfoService.hasProjectAccess(userId, projectId)) {
            return R.error("无权限访问此项目");
        }

        List<ProjectMemberDTO> members = userOrganizationService.getProjectMembers(projectId);

        return R.ok(members);
    }
    
    @PostMapping("/add")
    @Operation(summary = "添加项目成员")
    public R<Object> addProjectMember(@Valid @RequestBody ProjectMemberDTO memberDTO) {
        // 检查项目管理权限
        Long currentUserId = CoolSecurityUtil.getCurrentUserId();
        if (!userOrganizationService.hasProjectManagePermission(currentUserId, memberDTO.getProjectId())) {
            return R.error("无权限管理此项目成员");
        }
        
        // 验证角色代码
        if (!GlobalProjectRoleEnum.isValidCode(memberDTO.getRoleCode())) {
            return R.error("无效的项目角色代码");
        }
        
        boolean success = userOrganizationService.addUserToOrganization(
            memberDTO.getUserId(), 
            memberDTO.getProjectId(), 
            OrganizationModeEnum.PROJECT.getCode(),
            memberDTO.getRoleCode(), 
            currentUserId
        );
        
        if (success) {
            return R.ok("项目成员添加成功");
        } else {
            return R.error("项目成员添加失败");
        }
    }
    
    @PostMapping("/batch-add")
    @Operation(summary = "批量添加项目成员")
    public R<Object> batchAddProjectMembers(@RequestBody Map<String, Object> params) {
        Long projectId = Long.valueOf(params.get("projectId").toString());
        @SuppressWarnings("unchecked")
        List<Long> userIds = (List<Long>) params.get("userIds");
        String roleCode = params.get("roleCode").toString();
        
        // 检查项目管理权限
        Long currentUserId = CoolSecurityUtil.getCurrentUserId();
        if (!userOrganizationService.hasProjectManagePermission(currentUserId, projectId)) {
            return R.error("无权限管理此项目成员");
        }
        
        // 验证角色代码
        if (!GlobalProjectRoleEnum.isValidCode(roleCode)) {
            return R.error("无效的项目角色代码");
        }
        
        int successCount = userOrganizationService.batchAddUsersToOrganization(
            userIds, projectId, OrganizationModeEnum.PROJECT.getCode(), roleCode, currentUserId);
        
        return R.ok("成功添加 " + successCount + " 个项目成员");
    }
    
    @DeleteMapping("/remove/{projectId}/{userId}")
    @Operation(summary = "移除项目成员")
    public R<Object> removeProjectMember(@PathVariable Long projectId, @PathVariable Long userId) {
        // 检查项目管理权限
        Long currentUserId = CoolSecurityUtil.getCurrentUserId();
        if (!userOrganizationService.hasProjectManagePermission(currentUserId, projectId)) {
            return R.error("无权限管理此项目成员");
        }

        // 不能移除自己
        if (userId.equals(currentUserId)) {
            return R.error("不能移除自己");
        }
        
        boolean success = userOrganizationService.removeUserFromOrganization(
            userId, projectId, OrganizationModeEnum.PROJECT.getCode());
        
        if (success) {
            return R.ok("项目成员移除成功");
        } else {
            return R.error("项目成员移除失败");
        }
    }
    
    @PostMapping("/batch-remove")
    @Operation(summary = "批量移除项目成员")
    public R<Object> batchRemoveProjectMembers(@RequestBody Map<String, Object> params) {
        Long projectId = Long.valueOf(params.get("projectId").toString());
        @SuppressWarnings("unchecked")
        List<Long> userIds = (List<Long>) params.get("userIds");
        
        // 检查项目管理权限
        Long currentUserId = CoolSecurityUtil.getCurrentUserId();
        if (!userOrganizationService.hasProjectManagePermission(currentUserId, projectId)) {
            return R.error("无权限管理此项目成员");
        }

        // 移除当前用户自己
        userIds.remove(currentUserId);
        
        int successCount = userOrganizationService.batchRemoveUsersFromOrganization(
            userIds, projectId, OrganizationModeEnum.PROJECT.getCode());
        
        return R.ok("成功移除 " + successCount + " 个项目成员");
    }
    
    @PutMapping("/update-role")
    @Operation(summary = "更新项目成员角色")
    public R<Object> updateMemberRole(@RequestBody Map<String, Object> params) {
        Long projectId = Long.valueOf(params.get("projectId").toString());
        Long userId = Long.valueOf(params.get("userId").toString());
        String roleCode = params.get("roleCode").toString();
        
        // 检查项目管理权限
        Long currentUserId = CoolSecurityUtil.getCurrentUserId();
        if (!userOrganizationService.hasProjectManagePermission(currentUserId, projectId)) {
            return R.error("无权限管理此项目成员");
        }
        
        // 验证角色代码
        if (!GlobalProjectRoleEnum.isValidCode(roleCode)) {
            return R.error("无效的项目角色代码");
        }
        
        boolean success = userOrganizationService.updateUserRoleInOrganization(
            userId, projectId, OrganizationModeEnum.PROJECT.getCode(), roleCode);
        
        if (success) {
            return R.ok("项目成员角色更新成功");
        } else {
            return R.error("项目成员角色更新失败");
        }
    }
    
    @GetMapping("/role/{projectId}/{userId}")
    @Operation(summary = "获取用户在项目中的角色")
    public R<Object> getUserRoleInProject(@PathVariable Long projectId, @PathVariable Long userId) {
        // 检查项目访问权限
        Long currentUserId = CoolSecurityUtil.getCurrentUserId();
        if (!projectInfoService.hasProjectAccess(currentUserId, projectId)) {
            return R.error("无权限访问此项目");
        }
        
        String roleCode = userOrganizationService.getUserRoleInOrganization(
            userId, projectId, OrganizationModeEnum.PROJECT.getCode());
        
        String roleName = null;
        if (roleCode != null) {
            GlobalProjectRoleEnum role = GlobalProjectRoleEnum.getByCode(roleCode);
            if (role != null) {
                roleName = role.getName();
            }
        }
        
        return R.ok(Dict.create()
            .set("userId", userId)
            .set("projectId", projectId)
            .set("roleCode", roleCode)
            .set("roleName", roleName)
            .set("hasManagePermission", GlobalProjectRoleEnum.hasManagePermission(roleCode))
            .set("hasEditPermission", GlobalProjectRoleEnum.hasEditPermission(roleCode))
        );
    }
    
    @GetMapping("/check-permission/{projectId}")
    @Operation(summary = "检查当前用户在项目中的权限")
    public R<Object> checkProjectPermission(@PathVariable Long projectId) {
        Long currentUserId = CoolSecurityUtil.getCurrentUserId();
        
        boolean hasAccess = projectInfoService.hasProjectAccess(currentUserId, projectId);
        boolean hasManagePermission = userOrganizationService.hasProjectManagePermission(currentUserId, projectId);
        
        String roleCode = userOrganizationService.getUserRoleInOrganization(
            currentUserId, projectId, OrganizationModeEnum.PROJECT.getCode());
        
        return R.ok(Dict.create()
            .set("userId", currentUserId)
            .set("projectId", projectId)
            .set("hasAccess", hasAccess)
            .set("hasManagePermission", hasManagePermission)
            .set("roleCode", roleCode)
            .set("hasEditPermission", GlobalProjectRoleEnum.hasEditPermission(roleCode))
        );
    }
    
    @GetMapping("/roles")
    @Operation(summary = "获取所有项目角色")
    public R<Object> getProjectRoles() {
        List<Dict> roles = List.of(
            Dict.create()
                .set("code", GlobalProjectRoleEnum.PROJECT_OWNER.getCode())
                .set("name", GlobalProjectRoleEnum.PROJECT_OWNER.getName())
                .set("description", GlobalProjectRoleEnum.PROJECT_OWNER.getDescription())
                .set("level", GlobalProjectRoleEnum.PROJECT_OWNER.getLevel()),
            Dict.create()
                .set("code", GlobalProjectRoleEnum.PROJECT_ADMIN.getCode())
                .set("name", GlobalProjectRoleEnum.PROJECT_ADMIN.getName())
                .set("description", GlobalProjectRoleEnum.PROJECT_ADMIN.getDescription())
                .set("level", GlobalProjectRoleEnum.PROJECT_ADMIN.getLevel()),
            Dict.create()
                .set("code", GlobalProjectRoleEnum.PROJECT_MEMBER.getCode())
                .set("name", GlobalProjectRoleEnum.PROJECT_MEMBER.getName())
                .set("description", GlobalProjectRoleEnum.PROJECT_MEMBER.getDescription())
                .set("level", GlobalProjectRoleEnum.PROJECT_MEMBER.getLevel()),
            Dict.create()
                .set("code", GlobalProjectRoleEnum.PROJECT_VIEWER.getCode())
                .set("name", GlobalProjectRoleEnum.PROJECT_VIEWER.getName())
                .set("description", GlobalProjectRoleEnum.PROJECT_VIEWER.getDescription())
                .set("level", GlobalProjectRoleEnum.PROJECT_VIEWER.getLevel())
        );
        
        return R.ok(roles);
    }
}
