package com.cool.modules.organization.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.cool.modules.base.entity.sys.BaseSysDepartmentEntity;
import com.cool.modules.base.service.sys.BaseSysDepartmentService;
import com.cool.modules.organization.service.DualDimensionMigrationService;
import com.cool.modules.sop.entity.WorkOrderEntity;
import com.cool.modules.sop.service.WorkOrderService;
import com.cool.modules.task.entity.TaskExecutionEntity;
import com.cool.modules.task.entity.TaskInfoEntity;
import com.cool.modules.task.entity.TaskPackageEntity;
import com.cool.modules.task.service.TaskExecutionService;
import com.cool.modules.task.service.TaskInfoService;
import com.cool.modules.task.service.TaskPackageService;
import com.mybatisflex.core.query.QueryWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 双维度权限数据迁移服务实现
 * 
 * <AUTHOR> Admin
 * @since 2025-01-17
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DualDimensionMigrationServiceImpl implements DualDimensionMigrationService {
    
    private final WorkOrderService workOrderService;
    private final TaskPackageService taskPackageService;
    private final TaskInfoService taskInfoService;
    private final TaskExecutionService taskExecutionService;
    private final BaseSysDepartmentService departmentService;
    
    private static final Map<String, MigrationStatus> migrationStatusMap = new ConcurrentHashMap<>();
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public MigrationResult executeFullMigration() {
        long startTime = System.currentTimeMillis();
        MigrationResult result = new MigrationResult();
        
        try {
            log.info("开始执行双维度权限数据迁移");
            migrationStatusMap.put("full", MigrationStatus.IN_PROGRESS);
            
            // 1. 迁移工单部门数据
            int workOrderMigrated = migrateWorkOrderDepartments();
            result.setWorkOrderMigrated(workOrderMigrated);
            log.info("工单部门数据迁移完成，处理 {} 条记录", workOrderMigrated);
            
            // 2. 处理任务包数据
            int taskPackageProcessed = processTaskPackageData();
            result.setTaskPackageProcessed(taskPackageProcessed);
            log.info("任务包数据处理完成，处理 {} 条记录", taskPackageProcessed);
            
            // 3. 处理任务信息数据
            int taskInfoProcessed = processTaskInfoData();
            result.setTaskInfoProcessed(taskInfoProcessed);
            log.info("任务信息数据处理完成，处理 {} 条记录", taskInfoProcessed);
            
            // 4. 处理任务执行数据
            int taskExecutionProcessed = processTaskExecutionData();
            result.setTaskExecutionProcessed(taskExecutionProcessed);
            log.info("任务执行数据处理完成，处理 {} 条记录", taskExecutionProcessed);
            
            // 5. 初始化项目维度
            boolean projectInitialized = initializeProjectDimension();
            if (!projectInitialized) {
                log.warn("项目维度初始化失败");
            }
            
            // 6. 验证数据一致性
            ValidationResult validation = validateDataConsistency();
            if (!validation.isValid()) {
                log.warn("数据一致性验证发现问题: {}", validation.getMessage());
            }
            
            result.setSuccess(true);
            result.setMessage("数据迁移成功完成");
            migrationStatusMap.put("full", MigrationStatus.COMPLETED);
            
        } catch (Exception e) {
            log.error("数据迁移失败", e);
            result.setSuccess(false);
            result.setMessage("数据迁移失败: " + e.getMessage());
            migrationStatusMap.put("full", MigrationStatus.FAILED);
        } finally {
            long executionTime = System.currentTimeMillis() - startTime;
            result.setExecutionTime(executionTime);
            log.info("数据迁移执行完成，耗时 {} ms", executionTime);
        }
        
        return result;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int migrateWorkOrderDepartments() {
        log.info("开始迁移工单部门数据");
        
        // 获取所有需要迁移的工单（applicant_dept不为空但applicant_dept_id为空）
        List<WorkOrderEntity> workOrders = workOrderService.list(
            QueryWrapper.create()
                .isNotNull(WorkOrderEntity::getApplicantDept)
                .ne(WorkOrderEntity::getApplicantDept, "")
                .isNull(WorkOrderEntity::getApplicantDeptId)
        );
        
        if (CollUtil.isEmpty(workOrders)) {
            log.info("没有需要迁移的工单数据");
            return 0;
        }
        
        // 获取所有部门信息，建立名称到ID的映射
        List<BaseSysDepartmentEntity> departments = departmentService.list();
        Map<String, Long> departmentNameToIdMap = new ConcurrentHashMap<>();
        for (BaseSysDepartmentEntity dept : departments) {
            if (StrUtil.isNotBlank(dept.getName())) {
                departmentNameToIdMap.put(dept.getName().trim(), dept.getId());
            }
        }
        
        int migratedCount = 0;
        for (WorkOrderEntity workOrder : workOrders) {
            String deptName = workOrder.getApplicantDept().trim();
            Long deptId = departmentNameToIdMap.get(deptName);
            
            if (deptId != null) {
                workOrder.setApplicantDeptId(deptId);
                workOrderService.updateById(workOrder);
                migratedCount++;
                log.debug("工单 {} 的申请部门 '{}' 已映射到部门ID {}", 
                         workOrder.getId(), deptName, deptId);
            } else {
                log.warn("工单 {} 的申请部门 '{}' 未找到对应的部门ID", 
                        workOrder.getId(), deptName);
            }
        }
        
        log.info("工单部门数据迁移完成，成功迁移 {} 条记录", migratedCount);
        return migratedCount;
    }
    
    @Override
    public boolean initializeProjectDimension() {
        log.info("开始初始化项目维度数据");
        
        try {
            // 确保所有现有业务数据的项目ID字段为NULL
            // 这样这些数据将在部门形态下可见，在项目形态下不可见
            
            // 任务包
            List<TaskPackageEntity> taskPackages = taskPackageService.list(
                QueryWrapper.create().isNotNull(TaskPackageEntity::getProjectId)
            );
            for (TaskPackageEntity entity : taskPackages) {
                entity.setProjectId(null);
                taskPackageService.updateById(entity);
            }
            
            // 任务信息
            List<TaskInfoEntity> taskInfos = taskInfoService.list(
                QueryWrapper.create().isNotNull(TaskInfoEntity::getProjectId)
            );
            for (TaskInfoEntity entity : taskInfos) {
                entity.setProjectId(null);
                taskInfoService.updateById(entity);
            }
            
            // 任务执行
            List<TaskExecutionEntity> taskExecutions = taskExecutionService.list(
                QueryWrapper.create().isNotNull(TaskExecutionEntity::getProjectId)
            );
            for (TaskExecutionEntity entity : taskExecutions) {
                entity.setProjectId(null);
                taskExecutionService.updateById(entity);
            }
            
            // 工单
            List<WorkOrderEntity> workOrders = workOrderService.list(
                QueryWrapper.create().isNotNull(WorkOrderEntity::getProjectId)
            );
            for (WorkOrderEntity entity : workOrders) {
                entity.setProjectId(null);
                workOrderService.updateById(entity);
            }
            
            log.info("项目维度数据初始化完成，现有数据将在部门形态下可见");
            return true;
            
        } catch (Exception e) {
            log.error("项目维度数据初始化失败", e);
            return false;
        }
    }
    
    @Override
    public ValidationResult validateDataConsistency() {
        log.info("开始验证数据一致性");
        
        ValidationResult result = new ValidationResult();
        
        try {
            // 检查任务包数据一致性
            int taskPackageIssues = countTaskPackageIssues();
            result.setTaskPackageIssues(taskPackageIssues);
            
            // 检查任务信息数据一致性
            int taskInfoIssues = countTaskInfoIssues();
            result.setTaskInfoIssues(taskInfoIssues);
            
            // 检查任务执行数据一致性
            int taskExecutionIssues = countTaskExecutionIssues();
            result.setTaskExecutionIssues(taskExecutionIssues);
            
            // 检查工单数据一致性
            int workOrderIssues = countWorkOrderIssues();
            result.setWorkOrderIssues(workOrderIssues);
            
            int totalIssues = taskPackageIssues + taskInfoIssues + taskExecutionIssues + workOrderIssues;
            
            if (totalIssues == 0) {
                result.setValid(true);
                result.setMessage("数据一致性验证通过");
            } else {
                result.setValid(false);
                result.setMessage(String.format(
                    "发现 %d 个数据一致性问题：任务包(%d)，任务信息(%d)，任务执行(%d)，工单(%d)",
                    totalIssues, taskPackageIssues, taskInfoIssues, taskExecutionIssues, workOrderIssues
                ));
            }
            
        } catch (Exception e) {
            log.error("数据一致性验证失败", e);
            result.setValid(false);
            result.setMessage("数据一致性验证失败: " + e.getMessage());
        }
        
        return result;
    }
    
    @Override
    public boolean rollbackMigration() {
        log.info("开始回滚数据迁移");
        
        try {
            migrationStatusMap.put("full", MigrationStatus.IN_PROGRESS);
            
            // 清空工单的申请部门ID
            List<WorkOrderEntity> workOrdersToUpdate = workOrderService.list(
                QueryWrapper.create().isNotNull(WorkOrderEntity::getApplicantDeptId)
            );
            for (WorkOrderEntity entity : workOrdersToUpdate) {
                entity.setApplicantDeptId(null);
                workOrderService.updateById(entity);
            }
            
            // 清空所有项目ID字段
            List<TaskPackageEntity> taskPackagesToUpdate = taskPackageService.list(
                QueryWrapper.create().isNotNull(TaskPackageEntity::getProjectId)
            );
            for (TaskPackageEntity entity : taskPackagesToUpdate) {
                entity.setProjectId(null);
                taskPackageService.updateById(entity);
            }
            
            List<TaskInfoEntity> taskInfosToUpdate = taskInfoService.list(
                QueryWrapper.create().isNotNull(TaskInfoEntity::getProjectId)
            );
            for (TaskInfoEntity entity : taskInfosToUpdate) {
                entity.setProjectId(null);
                taskInfoService.updateById(entity);
            }
            
            List<TaskExecutionEntity> taskExecutionsToUpdate = taskExecutionService.list(
                QueryWrapper.create().isNotNull(TaskExecutionEntity::getProjectId)
            );
            for (TaskExecutionEntity entity : taskExecutionsToUpdate) {
                entity.setProjectId(null);
                taskExecutionService.updateById(entity);
            }
            
            List<WorkOrderEntity> workOrdersProjectToUpdate = workOrderService.list(
                QueryWrapper.create().isNotNull(WorkOrderEntity::getProjectId)
            );
            for (WorkOrderEntity entity : workOrdersProjectToUpdate) {
                entity.setProjectId(null);
                workOrderService.updateById(entity);
            }
            
            migrationStatusMap.put("full", MigrationStatus.ROLLED_BACK);
            log.info("数据迁移回滚完成");
            return true;
            
        } catch (Exception e) {
            log.error("数据迁移回滚失败", e);
            migrationStatusMap.put("full", MigrationStatus.FAILED);
            return false;
        }
    }
    
    @Override
    public MigrationStatus getMigrationStatus() {
        return migrationStatusMap.getOrDefault("full", MigrationStatus.NOT_STARTED);
    }
    
    // 私有辅助方法
    
    private int processTaskPackageData() {
        // 确保任务包数据的一致性
        List<TaskPackageEntity> taskPackages = taskPackageService.list();
        int processedCount = 0;
        
        for (TaskPackageEntity entity : taskPackages) {
            boolean updated = false;
            
            // 确保项目ID为NULL（现有数据在部门形态下可见）
            if (entity.getProjectId() != null) {
                entity.setProjectId(null);
                updated = true;
            }
            
            if (updated) {
                taskPackageService.updateById(entity);
                processedCount++;
            }
        }
        
        return processedCount;
    }
    
    private int processTaskInfoData() {
        // 确保任务信息数据的一致性
        List<TaskInfoEntity> taskInfos = taskInfoService.list();
        int processedCount = 0;
        
        for (TaskInfoEntity entity : taskInfos) {
            boolean updated = false;
            
            // 确保项目ID为NULL（现有数据在部门形态下可见）
            if (entity.getProjectId() != null) {
                entity.setProjectId(null);
                updated = true;
            }
            
            if (updated) {
                taskInfoService.updateById(entity);
                processedCount++;
            }
        }
        
        return processedCount;
    }
    
    private int processTaskExecutionData() {
        // 确保任务执行数据的一致性
        List<TaskExecutionEntity> taskExecutions = taskExecutionService.list();
        int processedCount = 0;
        
        for (TaskExecutionEntity entity : taskExecutions) {
            boolean updated = false;
            
            // 确保项目ID为NULL（现有数据在部门形态下可见）
            if (entity.getProjectId() != null) {
                entity.setProjectId(null);
                updated = true;
            }
            
            if (updated) {
                taskExecutionService.updateById(entity);
                processedCount++;
            }
        }
        
        return processedCount;
    }
    
    private int countTaskPackageIssues() {
        // 检查任务包数据问题：部门ID和项目ID同时为空或同时不为空
        return (int) taskPackageService.count(
            QueryWrapper.create()
                .and("department_id IS NULL AND project_id IS NULL")
                .or("department_id IS NOT NULL AND project_id IS NOT NULL")
        );
    }
    
    private int countTaskInfoIssues() {
        // 检查任务信息数据问题
        return (int) taskInfoService.count(
            QueryWrapper.create()
                .and("department_id IS NULL AND project_id IS NULL")
                .or("department_id IS NOT NULL AND project_id IS NOT NULL")
        );
    }
    
    private int countTaskExecutionIssues() {
        // 检查任务执行数据问题
        return (int) taskExecutionService.count(
            QueryWrapper.create()
                .and("department_id IS NULL AND project_id IS NULL")
                .or("department_id IS NOT NULL AND project_id IS NOT NULL")
        );
    }
    
    private int countWorkOrderIssues() {
        // 检查工单数据问题
        return (int) workOrderService.count(
            QueryWrapper.create()
                .and("applicant_dept_id IS NULL AND project_id IS NULL")
                .or("applicant_dept_id IS NOT NULL AND project_id IS NOT NULL")
        );
    }
}
