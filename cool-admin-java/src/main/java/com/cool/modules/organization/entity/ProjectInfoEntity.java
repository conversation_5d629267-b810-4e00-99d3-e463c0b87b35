package com.cool.modules.organization.entity;

import com.cool.core.base.BaseEntity;
import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Table;
import com.tangzc.mybatisflex.autotable.annotation.ColumnDefine;
import lombok.Getter;
import lombok.Setter;
import org.dromara.autotable.annotation.Index;

import java.util.Date;

/**
 * 项目信息实体
 * 
 * <AUTHOR> Admin
 * @since 2025-01-17
 */
@Getter
@Setter
@Table(value = "org_project_info", comment = "项目信息表")
public class ProjectInfoEntity extends BaseEntity<ProjectInfoEntity> {
    
    /**
     * 项目名称
     */
    @Index
    @ColumnDefine(comment = "项目名称", length = 200, notNull = true)
    private String projectName;
    
    /**
     * 项目编码
     */
    @Index
    @ColumnDefine(comment = "项目编码", length = 100, notNull = true)
    private String projectCode;
    
    /**
     * 项目描述
     */
    @ColumnDefine(comment = "项目描述", type = "text")
    private String description;
    
    /**
     * 项目状态
     */
    @ColumnDefine(comment = "项目状态 0:规划中 1:进行中 2:已完成 3:已暂停 4:已取消", type = "tinyint", defaultValue = "0")
    private Integer status;
    
    /**
     * 项目优先级
     */
    @ColumnDefine(comment = "项目优先级 1:低 2:中 3:高 4:紧急", type = "tinyint", defaultValue = "2")
    private Integer priority;
    
    /**
     * 项目负责人ID
     */
    @Index
    @ColumnDefine(comment = "项目负责人ID", type = "bigint", notNull = true)
    private Long ownerId;
    
    /**
     * 项目负责人姓名（冗余字段，便于查询）
     */
    @Column(ignore = true)
    private String ownerName;
    
    /**
     * 创建人ID
     */
    @ColumnDefine(comment = "创建人ID", type = "bigint", notNull = true)
    private Long creatorId;
    
    /**
     * 创建人姓名（冗余字段，便于查询）
     */
    @Column(ignore = true)
    private String creatorName;
    
    /**
     * 计划开始时间
     */
    @ColumnDefine(comment = "计划开始时间")
    private Date plannedStartTime;
    
    /**
     * 计划结束时间
     */
    @ColumnDefine(comment = "计划结束时间")
    private Date plannedEndTime;
    
    /**
     * 实际开始时间
     */
    @ColumnDefine(comment = "实际开始时间")
    private Date actualStartTime;
    
    /**
     * 实际结束时间
     */
    @ColumnDefine(comment = "实际结束时间")
    private Date actualEndTime;
    
    /**
     * 项目预算
     */
    @ColumnDefine(comment = "项目预算", type = "decimal(15,2)")
    private java.math.BigDecimal budget;
    
    /**
     * 项目标签（JSON格式）
     */
    @ColumnDefine(comment = "项目标签", type = "json")
    private String tags;
    
    /**
     * 项目附件
     */
    @ColumnDefine(comment = "项目附件", type = "json")
    private String attachments;
    
    /**
     * 是否启用
     */
    @ColumnDefine(comment = "是否启用", defaultValue = "true")
    private Boolean enabled;
    
    /**
     * 排序号
     */
    @ColumnDefine(comment = "排序号", type = "int", defaultValue = "0")
    private Integer orderNum;
    
    /**
     * 备注
     */
    @ColumnDefine(comment = "备注", type = "text")
    private String remark;
    
    // 以下为查询时填充的字段，不存储到数据库
    
    /**
     * 项目成员数量
     */
    @Column(ignore = true)
    private Integer memberCount;
    
    /**
     * 项目任务数量
     */
    @Column(ignore = true)
    private Integer taskCount;
    
    /**
     * 项目完成率
     */
    @Column(ignore = true)
    private java.math.BigDecimal completionRate;
}
