package com.cool.modules.organization.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 数据权限过滤注解
 * 
 * <AUTHOR> Admin
 * @since 2025-01-17
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface DataPermissionFilter {
    
    /**
     * 实体类型
     */
    String entityType();
    
    /**
     * 是否启用权限过滤
     */
    boolean enable() default true;
    
    /**
     * 是否忽略系统管理员权限检查
     */
    boolean ignoreAdmin() default false;
    
    /**
     * 自定义权限检查方法
     */
    String customChecker() default "";
    
    /**
     * 权限过滤描述
     */
    String description() default "";
}
