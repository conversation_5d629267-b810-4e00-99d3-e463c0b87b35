package com.cool.modules.organization.service;

import com.cool.modules.organization.dto.ProjectMemberDTO;
import com.cool.modules.organization.entity.UserOrganizationEntity;
import com.mybatisflex.core.service.IService;

import java.util.List;

/**
 * 用户组织关系服务接口
 * 
 * <AUTHOR> Admin
 * @since 2025-01-17
 */
public interface UserOrganizationService extends IService<UserOrganizationEntity> {
    
    /**
     * 根据用户ID和组织类型获取组织关系
     * 
     * @param userId 用户ID
     * @param organizationType 组织类型
     * @return 组织关系列表
     */
    List<UserOrganizationEntity> getByUserIdAndType(Long userId, String organizationType);
    
    /**
     * 根据组织ID和组织类型获取用户关系
     * 
     * @param organizationId 组织ID
     * @param organizationType 组织类型
     * @return 用户关系列表
     */
    List<UserOrganizationEntity> getByOrganizationIdAndType(Long organizationId, String organizationType);
    
    /**
     * 检查用户是否在指定组织中
     * 
     * @param userId 用户ID
     * @param organizationId 组织ID
     * @param organizationType 组织类型
     * @return 是否存在
     */
    boolean existsByUserAndOrganization(Long userId, Long organizationId, String organizationType);
    
    /**
     * 获取用户在指定组织中的角色
     * 
     * @param userId 用户ID
     * @param organizationId 组织ID
     * @param organizationType 组织类型
     * @return 角色代码
     */
    String getUserRoleInOrganization(Long userId, Long organizationId, String organizationType);
    
    /**
     * 添加用户到组织
     * 
     * @param userId 用户ID
     * @param organizationId 组织ID
     * @param organizationType 组织类型
     * @param roleCode 角色代码
     * @param assignerId 分配人ID
     * @return 是否成功
     */
    boolean addUserToOrganization(Long userId, Long organizationId, String organizationType, 
                                 String roleCode, Long assignerId);
    
    /**
     * 从组织中移除用户
     * 
     * @param userId 用户ID
     * @param organizationId 组织ID
     * @param organizationType 组织类型
     * @return 是否成功
     */
    boolean removeUserFromOrganization(Long userId, Long organizationId, String organizationType);
    
    /**
     * 更新用户在组织中的角色
     * 
     * @param userId 用户ID
     * @param organizationId 组织ID
     * @param organizationType 组织类型
     * @param roleCode 新角色代码
     * @return 是否成功
     */
    boolean updateUserRoleInOrganization(Long userId, Long organizationId, String organizationType, String roleCode);
    
    /**
     * 批量添加用户到组织
     * 
     * @param userIds 用户ID列表
     * @param organizationId 组织ID
     * @param organizationType 组织类型
     * @param roleCode 角色代码
     * @param assignerId 分配人ID
     * @return 成功添加的数量
     */
    int batchAddUsersToOrganization(List<Long> userIds, Long organizationId, String organizationType, 
                                   String roleCode, Long assignerId);
    
    /**
     * 批量从组织中移除用户
     * 
     * @param userIds 用户ID列表
     * @param organizationId 组织ID
     * @param organizationType 组织类型
     * @return 成功移除的数量
     */
    int batchRemoveUsersFromOrganization(List<Long> userIds, Long organizationId, String organizationType);
    
    /**
     * 获取用户的项目列表
     * 
     * @param userId 用户ID
     * @return 项目ID列表
     */
    List<Long> getUserProjectIds(Long userId);
    
    /**
     * 获取用户的最高项目角色
     * 
     * @param userId 用户ID
     * @return 最高角色代码
     */
    String getHighestProjectRole(Long userId);
    
    /**
     * 获取项目成员列表
     * 
     * @param projectId 项目ID
     * @return 项目成员列表
     */
    List<ProjectMemberDTO> getProjectMembers(Long projectId);
    
    /**
     * 检查用户是否有项目管理权限
     * 
     * @param userId 用户ID
     * @param projectId 项目ID
     * @return 是否有管理权限
     */
    boolean hasProjectManagePermission(Long userId, Long projectId);
}
