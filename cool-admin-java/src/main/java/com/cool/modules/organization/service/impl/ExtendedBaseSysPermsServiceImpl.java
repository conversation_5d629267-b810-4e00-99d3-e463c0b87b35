package com.cool.modules.organization.service.impl;

import org.springframework.context.annotation.Lazy;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

import com.cool.modules.base.service.sys.impl.BaseSysPermsServiceImpl;
import com.cool.modules.organization.service.DualDimensionPermsService;

import cn.hutool.core.lang.Dict;
import lombok.extern.slf4j.Slf4j;

/**
 * 扩展的权限服务实现，支持双维度权限
 * 
 * <AUTHOR> Admin
 * @since 2025-01-17
 */
@Slf4j
@Service
@Primary
public class ExtendedBaseSysPermsServiceImpl extends BaseSysPermsServiceImpl {
    
    private final DualDimensionPermsService dualDimensionPermsService;
    
    public ExtendedBaseSysPermsServiceImpl(
            @Lazy DualDimensionPermsService dualDimensionPermsService,
            com.cool.core.cache.CoolCache coolCache,
            com.cool.modules.base.mapper.sys.BaseSysUserMapper baseSysUserMapper,
            com.cool.modules.base.mapper.sys.BaseSysUserRoleMapper baseSysUserRoleMapper,
            com.cool.modules.base.mapper.sys.BaseSysMenuMapper baseSysMenuMapper,
            com.cool.modules.base.mapper.sys.BaseSysRoleMenuMapper baseSysRoleMenuMapper,
            com.cool.modules.base.mapper.sys.BaseSysRoleDepartmentMapper baseSysRoleDepartmentMapper,
            com.cool.modules.base.mapper.sys.BaseSysDepartmentMapper baseSysDepartmentMapper,
            java.util.concurrent.ExecutorService cachedThreadPool) {
        super(coolCache, baseSysUserMapper, baseSysUserRoleMapper, baseSysMenuMapper, 
              baseSysRoleMenuMapper, baseSysRoleDepartmentMapper, baseSysDepartmentMapper, cachedThreadPool);
        this.dualDimensionPermsService = dualDimensionPermsService;
    }
    
    @Override
    public Dict permmenu(Long adminUserId) {
        try {
            // 使用双维度权限服务获取权限菜单
            return dualDimensionPermsService.permmenu(adminUserId);
        } catch (Exception e) {
            log.error("获取双维度权限菜单失败，回退到原始实现", e);
            // 如果双维度权限服务失败，回退到原始实现
            return super.permmenu(adminUserId);
        }
    }
}
