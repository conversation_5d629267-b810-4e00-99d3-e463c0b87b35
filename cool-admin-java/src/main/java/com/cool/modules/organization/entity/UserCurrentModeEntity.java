package com.cool.modules.organization.entity;

import com.cool.core.base.BaseEntity;
import com.mybatisflex.annotation.Table;
import com.tangzc.mybatisflex.autotable.annotation.ColumnDefine;
import lombok.Getter;
import lombok.Setter;
import org.dromara.autotable.annotation.Index;

/**
 * 用户当前组织模式实体
 * 
 * <AUTHOR> Admin
 * @since 2025-01-17
 */
@Getter
@Setter
@Table(value = "org_user_current_mode", comment = "用户当前组织模式表")
public class UserCurrentModeEntity extends BaseEntity<UserCurrentModeEntity> {
    
    /**
     * 用户ID
     */
    @Index
    @ColumnDefine(comment = "用户ID", type = "bigint", notNull = true)
    private Long userId;
    
    /**
     * 当前组织模式
     */
    @ColumnDefine(comment = "当前组织模式", length = 20, defaultValue = "DEPARTMENT", notNull = true)
    private String currentMode;
    
    /**
     * 上次切换时间
     */
    @ColumnDefine(comment = "上次切换时间")
    private java.util.Date lastSwitchTime;
    
    /**
     * 切换次数
     */
    @ColumnDefine(comment = "切换次数", type = "int", defaultValue = "0")
    private Integer switchCount;
    
    /**
     * 备注
     */
    @ColumnDefine(comment = "备注", length = 500)
    private String remark;
}
