package com.cool.modules.organization.mapper;

import com.cool.modules.organization.entity.ProjectInfoEntity;
import com.mybatisflex.core.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 项目信息Mapper
 * 
 * <AUTHOR> Admin
 * @since 2025-01-17
 */
@Mapper
public interface ProjectInfoMapper extends BaseMapper<ProjectInfoEntity> {
    
    /**
     * 根据项目编码获取项目信息
     * 
     * @param projectCode 项目编码
     * @return 项目信息
     */
    ProjectInfoEntity getByProjectCode(@Param("projectCode") String projectCode);
    
    /**
     * 检查项目编码是否存在
     * 
     * @param projectCode 项目编码
     * @param excludeId 排除的项目ID
     * @return 是否存在
     */
    boolean existsByProjectCode(@Param("projectCode") String projectCode, @Param("excludeId") Long excludeId);
    
    /**
     * 根据负责人ID获取项目列表
     * 
     * @param ownerId 负责人ID
     * @return 项目列表
     */
    List<ProjectInfoEntity> getByOwnerId(@Param("ownerId") Long ownerId);
    
    /**
     * 根据创建人ID获取项目列表
     * 
     * @param creatorId 创建人ID
     * @return 项目列表
     */
    List<ProjectInfoEntity> getByCreatorId(@Param("creatorId") Long creatorId);
    
    /**
     * 获取用户参与的项目列表
     * 
     * @param userId 用户ID
     * @return 项目列表
     */
    List<ProjectInfoEntity> getUserProjects(@Param("userId") Long userId);
    
    /**
     * 更新项目状态
     * 
     * @param projectId 项目ID
     * @param status 状态
     * @return 影响行数
     */
    int updateStatus(@Param("projectId") Long projectId, @Param("status") Integer status);
    
    /**
     * 批量更新项目状态
     * 
     * @param projectIds 项目ID列表
     * @param status 状态
     * @return 影响行数
     */
    int batchUpdateStatus(@Param("projectIds") List<Long> projectIds, @Param("status") Integer status);
}
