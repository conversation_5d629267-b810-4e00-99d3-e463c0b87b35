package com.cool.modules.organization.controller.admin;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import com.cool.core.annotation.CoolRestController;
import com.cool.core.request.R;
import com.cool.core.util.CoolSecurityUtil;
import com.cool.modules.organization.dto.DataPermissionScopeDTO;
import com.cool.modules.organization.dto.OrganizationModeSwitchDTO;
import com.cool.modules.organization.service.OrganizationModeService;

import cn.hutool.core.lang.Dict;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;

/**
 * 组织形态管理控制器
 * 
 * <AUTHOR> Admin
 * @since 2025-01-17
 */
@Tag(name = "组织形态管理", description = "组织形态切换和权限管理")
@CoolRestController
@RequiredArgsConstructor
public class AdminOrganizationModeController {
    
    private final OrganizationModeService organizationModeService;
    
    @GetMapping("/current")
    @Operation(summary = "获取当前组织模式")
    public R<Dict> getCurrentMode() {
        Long userId = CoolSecurityUtil.getCurrentUserId();
        String currentMode = organizationModeService.getCurrentMode(userId);
        
        return R.ok(Dict.create()
            .set("currentMode", currentMode)
            .set("userId", userId)
            .set("canSwitchToDepartment", organizationModeService.canSwitchToMode(userId, "DEPARTMENT"))
            .set("canSwitchToProject", organizationModeService.canSwitchToMode(userId, "PROJECT"))
        );
    }
    
    @PostMapping("/switch")
    @Operation(summary = "切换组织模式")
    public R<String> switchMode(@Valid @RequestBody OrganizationModeSwitchDTO switchDTO, 
                               HttpServletRequest request) {
        // 设置当前用户ID
        Long userId = CoolSecurityUtil.getCurrentUserId();
        switchDTO.setUserId(userId);
        
        // 设置客户端信息
        switchDTO.setIpAddress(getClientIpAddress(request));
        switchDTO.setUserAgent(request.getHeader("User-Agent"));
        
        boolean success = organizationModeService.switchMode(switchDTO);
        
        if (success) {
            return R.ok("组织模式切换成功");
        } else {
            return R.error("组织模式切换失败");
        }
    }
    
    @GetMapping("/permission-scope")
    @Operation(summary = "获取用户权限范围")
    public R<DataPermissionScopeDTO> getPermissionScope() {
        Long userId = CoolSecurityUtil.getCurrentUserId();
        DataPermissionScopeDTO scope = organizationModeService.getUserPermissionScope(userId);
        
        return R.ok(scope);
    }
    
    @PostMapping("/refresh-cache")
    @Operation(summary = "刷新权限缓存")
    public R<String> refreshCache() {
        Long userId = CoolSecurityUtil.getCurrentUserId();
        organizationModeService.refreshUserPermissionCache(userId);
        
        return R.ok("权限缓存刷新成功");
    }
    
    @GetMapping("/switch-history")
    @Operation(summary = "获取切换历史")
    public R<Dict> getSwitchHistory() {
        Long userId = CoolSecurityUtil.getCurrentUserId();
        Integer switchCount = organizationModeService.getUserSwitchCount(userId);
        
        return R.ok(Dict.create()
            .set("userId", userId)
            .set("switchCount", switchCount)
        );
    }
    
    @GetMapping("/validate-mode/{mode}")
    @Operation(summary = "验证组织模式")
    public R<Dict> validateMode(@PathVariable String mode) {
        Long userId = CoolSecurityUtil.getCurrentUserId();
        boolean isValid = organizationModeService.isValidMode(mode);
        boolean canSwitch = organizationModeService.canSwitchToMode(userId, mode);
        
        return R.ok(Dict.create()
            .set("mode", mode)
            .set("isValid", isValid)
            .set("canSwitch", canSwitch)
        );
    }
    
    /**
     * 获取客户端IP地址
     */
    private String getClientIpAddress(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty() && !"unknown".equalsIgnoreCase(xForwardedFor)) {
            return xForwardedFor.split(",")[0];
        }
        
        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty() && !"unknown".equalsIgnoreCase(xRealIp)) {
            return xRealIp;
        }
        
        return request.getRemoteAddr();
    }
}
