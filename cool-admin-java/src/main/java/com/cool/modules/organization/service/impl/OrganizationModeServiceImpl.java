package com.cool.modules.organization.service.impl;

import cn.hutool.core.util.StrUtil;
import com.cool.core.exception.CoolException;
import com.cool.core.util.CoolSecurityUtil;
import com.cool.modules.base.service.sys.BaseSysPermsService;
import com.cool.modules.organization.dto.DataPermissionScopeDTO;
import com.cool.modules.organization.dto.OrganizationModeSwitchDTO;
import com.cool.modules.organization.entity.UserCurrentModeEntity;
import com.cool.modules.organization.entity.UserOrganizationEntity;
import com.cool.modules.organization.enums.OrganizationModeEnum;
import com.cool.modules.organization.mapper.UserCurrentModeMapper;
import com.cool.modules.organization.service.OrganizationModeService;
import com.cool.modules.organization.service.UserOrganizationService;
import com.mybatisflex.core.query.QueryWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 组织形态管理服务实现
 * 
 * <AUTHOR> Admin
 * @since 2025-01-17
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OrganizationModeServiceImpl implements OrganizationModeService {
    
    private final UserCurrentModeMapper userCurrentModeMapper;
    private final UserOrganizationService userOrganizationService;
    private final BaseSysPermsService baseSysPermsService;
    private final RedisTemplate<String, Object> redisTemplate;
    
    private static final String CACHE_KEY_PREFIX = "user:permission:scope:";
    private static final String MODE_CACHE_KEY_PREFIX = "user:current:mode:";
    private static final int CACHE_EXPIRE_MINUTES = 10;
    
    @Override
    public String getCurrentMode(Long userId) {
        if (userId == null) {
            return OrganizationModeEnum.DEPARTMENT.getCode();
        }
        
        // 先从缓存获取
        String cacheKey = MODE_CACHE_KEY_PREFIX + userId;
        String cachedMode = (String) redisTemplate.opsForValue().get(cacheKey);
        if (StrUtil.isNotBlank(cachedMode)) {
            return cachedMode;
        }
        
        // 从数据库获取
        String currentMode = userCurrentModeMapper.getCurrentModeByUserId(userId);
        if (StrUtil.isBlank(currentMode)) {
            // 如果没有记录，初始化为部门模式
            initUserMode(userId, OrganizationModeEnum.DEPARTMENT.getCode());
            currentMode = OrganizationModeEnum.DEPARTMENT.getCode();
        }
        
        // 缓存结果
        redisTemplate.opsForValue().set(cacheKey, currentMode, CACHE_EXPIRE_MINUTES, TimeUnit.MINUTES);
        
        return currentMode;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean switchMode(OrganizationModeSwitchDTO switchDTO) {
        Long userId = switchDTO.getUserId();
        String targetMode = switchDTO.getTargetMode();
        
        // 验证目标模式
        if (!isValidMode(targetMode)) {
            throw new CoolException("无效的组织模式: " + targetMode);
        }
        
        // 检查是否可以切换
        if (!switchDTO.getForceSwitch() && !canSwitchToMode(userId, targetMode)) {
            throw new CoolException("用户无权限切换到指定组织模式");
        }
        
        try {
            // 更新数据库
            int result = userCurrentModeMapper.updateCurrentModeByUserId(userId, targetMode);
            if (result == 0) {
                // 如果没有记录，创建新记录
                UserCurrentModeEntity entity = new UserCurrentModeEntity();
                entity.setUserId(userId);
                entity.setCurrentMode(targetMode);
                entity.setLastSwitchTime(new Date());
                entity.setSwitchCount(1);
                entity.setRemark(switchDTO.getReason());
                userCurrentModeMapper.insert(entity);
            } else {
                // 更新切换时间和次数
                UserCurrentModeEntity existing = userCurrentModeMapper.selectOneByQuery(
                    QueryWrapper.create().eq("user_id", userId));
                if (existing != null) {
                    existing.setLastSwitchTime(new Date());
                    existing.setSwitchCount(existing.getSwitchCount() + 1);
                    existing.setRemark(switchDTO.getReason());
                    userCurrentModeMapper.update(existing);
                }
            }
            
            // 清理相关缓存
            refreshUserPermissionCache(userId);
            
            log.info("用户 {} 成功切换组织模式到 {}, 原因: {}", userId, targetMode, switchDTO.getReason());
            return true;
            
        } catch (Exception e) {
            log.error("用户 {} 切换组织模式失败", userId, e);
            throw new CoolException("切换组织模式失败: " + e.getMessage());
        }
    }
    
    @Override
    public boolean canSwitchToMode(Long userId, String targetMode) {
        if (!isValidMode(targetMode)) {
            return false;
        }
        
        // 系统管理员可以切换到任何模式
        String currentUser = CoolSecurityUtil.getAdminUsername();
        if ("admin".equals(currentUser)) {
            return true;
        }
        
        if (OrganizationModeEnum.DEPARTMENT.getCode().equals(targetMode)) {
            // 所有用户都可以切换到部门模式
            return true;
        } else if (OrganizationModeEnum.PROJECT.getCode().equals(targetMode)) {
            // 检查用户是否参与了任何项目
            List<UserOrganizationEntity> projectRoles = userOrganizationService.getByUserIdAndType(
                userId, OrganizationModeEnum.PROJECT.getCode());
            return !projectRoles.isEmpty();
        }
        
        return false;
    }
    
    @Override
    public DataPermissionScopeDTO getUserPermissionScope(Long userId) {
        if (userId == null) {
            return new DataPermissionScopeDTO().setIsUnlimited(false);
        }
        
        String cacheKey = CACHE_KEY_PREFIX + userId;
        DataPermissionScopeDTO cachedScope = (DataPermissionScopeDTO) redisTemplate.opsForValue().get(cacheKey);
        
        if (cachedScope != null) {
            return cachedScope;
        }
        
        DataPermissionScopeDTO scope = calculateUserPermissionScope(userId);
        
        // 缓存结果
        redisTemplate.opsForValue().set(cacheKey, scope, CACHE_EXPIRE_MINUTES, TimeUnit.MINUTES);
        
        return scope;
    }
    
    private DataPermissionScopeDTO calculateUserPermissionScope(Long userId) {
        DataPermissionScopeDTO scope = new DataPermissionScopeDTO();
        scope.setUserId(userId);
        scope.setCacheTimestamp(System.currentTimeMillis());
        
        // 检查是否系统管理员
        String currentUser = CoolSecurityUtil.getAdminUsername();
        boolean isSystemAdmin = "admin".equals(currentUser);
        scope.setIsSystemAdmin(isSystemAdmin);
        scope.setIsUnlimited(isSystemAdmin);
        
        if (isSystemAdmin) {
            scope.setDescription("系统管理员，拥有所有权限");
            return scope;
        }
        
        // 获取当前组织模式
        String currentMode = getCurrentMode(userId);
        scope.setOrganizationMode(currentMode);
        
        if (OrganizationModeEnum.DEPARTMENT.getCode().equals(currentMode)) {
            // 部门模式：获取部门权限
            Long[] departmentIds = baseSysPermsService.loginDepartmentIds();
            scope.setDepartmentIds(departmentIds != null ? Arrays.asList(departmentIds) : null);
            scope.setDescription("部门模式，可访问 " + (departmentIds != null ? departmentIds.length : 0) + " 个部门");
        } else if (OrganizationModeEnum.PROJECT.getCode().equals(currentMode)) {
            // 项目模式：获取项目权限
            List<Long> projectIds = userOrganizationService.getUserProjectIds(userId);
            scope.setProjectIds(projectIds);
            scope.setDescription("项目模式，可访问 " + projectIds.size() + " 个项目");
        }
        
        return scope;
    }
    
    @Override
    public void refreshUserPermissionCache(Long userId) {
        if (userId == null) {
            return;
        }
        
        String permissionCacheKey = CACHE_KEY_PREFIX + userId;
        String modeCacheKey = MODE_CACHE_KEY_PREFIX + userId;
        
        redisTemplate.delete(permissionCacheKey);
        redisTemplate.delete(modeCacheKey);
        
        log.debug("已清理用户 {} 的权限缓存", userId);
    }
    
    @Override
    public void clearAllPermissionCache() {
        try {
            // 清理权限范围缓存
            redisTemplate.delete(redisTemplate.keys(CACHE_KEY_PREFIX + "*"));
            // 清理模式缓存
            redisTemplate.delete(redisTemplate.keys(MODE_CACHE_KEY_PREFIX + "*"));
            
            log.info("已清理所有用户权限缓存");
        } catch (Exception e) {
            log.error("清理权限缓存失败", e);
        }
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void initUserMode(Long userId, String defaultMode) {
        if (userId == null) {
            return;
        }
        
        if (!isValidMode(defaultMode)) {
            defaultMode = OrganizationModeEnum.DEPARTMENT.getCode();
        }
        
        // 检查是否已存在记录
        UserCurrentModeEntity existing = userCurrentModeMapper.selectOneByQuery(
            QueryWrapper.create().eq("user_id", userId));
        
        if (existing == null) {
            UserCurrentModeEntity entity = new UserCurrentModeEntity();
            entity.setUserId(userId);
            entity.setCurrentMode(defaultMode);
            entity.setLastSwitchTime(new Date());
            entity.setSwitchCount(0);
            entity.setRemark("系统初始化");
            userCurrentModeMapper.insert(entity);
            
            log.debug("为用户 {} 初始化组织模式: {}", userId, defaultMode);
        }
    }
    
    @Override
    public Integer getUserSwitchCount(Long userId) {
        if (userId == null) {
            return 0;
        }
        
        UserCurrentModeEntity entity = userCurrentModeMapper.selectOneByQuery(
            QueryWrapper.create().eq("user_id", userId));
        
        return entity != null ? entity.getSwitchCount() : 0;
    }
    
    @Override
    public boolean isValidMode(String mode) {
        return OrganizationModeEnum.isValidCode(mode);
    }
}
