package com.cool.modules.organization.entity;

import com.cool.core.base.BaseEntity;
import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Table;
import com.tangzc.mybatisflex.autotable.annotation.ColumnDefine;
import lombok.Getter;
import lombok.Setter;
import org.dromara.autotable.annotation.Index;

import java.util.Date;

/**
 * 用户组织关系实体
 * 
 * <AUTHOR> Admin
 * @since 2025-01-17
 */
@Getter
@Setter
@Table(value = "org_user_organization", comment = "用户组织关系表")
public class UserOrganizationEntity extends BaseEntity<UserOrganizationEntity> {
    
    /**
     * 用户ID
     */
    @Index
    @ColumnDefine(comment = "用户ID", type = "bigint", notNull = true)
    private Long userId;
    
    /**
     * 组织类型
     */
    @Index
    @ColumnDefine(comment = "组织类型", length = 20, notNull = true)
    private String organizationType;
    
    /**
     * 组织ID
     */
    @Index
    @ColumnDefine(comment = "组织ID", type = "bigint", notNull = true)
    private Long organizationId;
    
    /**
     * 角色代码
     */
    @ColumnDefine(comment = "角色代码", length = 50, notNull = true)
    private String roleCode;
    
    /**
     * 角色名称（冗余字段，便于查询）
     */
    @Column(ignore = true)
    private String roleName;
    
    /**
     * 组织名称（冗余字段，便于查询）
     */
    @Column(ignore = true)
    private String organizationName;
    
    /**
     * 用户姓名（冗余字段，便于查询）
     */
    @Column(ignore = true)
    private String userName;
    
    /**
     * 状态
     */
    @ColumnDefine(comment = "状态 0:禁用 1:启用", type = "tinyint", defaultValue = "1")
    private Integer status;
    
    /**
     * 加入时间
     */
    @ColumnDefine(comment = "加入时间")
    private Date joinTime;
    
    /**
     * 过期时间
     */
    @ColumnDefine(comment = "过期时间")
    private Date expireTime;
    
    /**
     * 分配人ID
     */
    @ColumnDefine(comment = "分配人ID", type = "bigint")
    private Long assignerId;
    
    /**
     * 分配人姓名（冗余字段，便于查询）
     */
    @Column(ignore = true)
    private String assignerName;
    
    /**
     * 分配时间
     */
    @ColumnDefine(comment = "分配时间")
    private Date assignTime;
    
    /**
     * 权限范围（JSON格式，用于存储特殊权限配置）
     */
    @ColumnDefine(comment = "权限范围", type = "json")
    private String permissionScope;
    
    /**
     * 备注
     */
    @ColumnDefine(comment = "备注", length = 500)
    private String remark;
    
    /**
     * 是否为主要角色
     */
    @ColumnDefine(comment = "是否为主要角色", defaultValue = "false")
    private Boolean isPrimary;
    
    /**
     * 排序号
     */
    @ColumnDefine(comment = "排序号", type = "int", defaultValue = "0")
    private Integer orderNum;
}
