package com.cool.modules.organization.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * 数据权限范围DTO
 * 
 * <AUTHOR> Admin
 * @since 2025-01-17
 */
@Data
@Accessors(chain = true)
public class DataPermissionScopeDTO implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 当前组织模式
     */
    private String organizationMode;
    
    /**
     * 可访问的部门ID列表
     */
    private List<Long> departmentIds;
    
    /**
     * 可访问的项目ID列表
     */
    private List<Long> projectIds;
    
    /**
     * 是否系统管理员
     */
    private Boolean isSystemAdmin;
    
    /**
     * 是否无限制权限
     */
    private Boolean isUnlimited;
    
    /**
     * 权限范围描述
     */
    private String description;
    
    /**
     * 缓存时间戳
     */
    private Long cacheTimestamp;
    
    /**
     * 检查是否有部门权限
     * 
     * @param departmentId 部门ID
     * @return 是否有权限
     */
    public boolean hasDepartmentPermission(Long departmentId) {
        if (isUnlimited != null && isUnlimited) {
            return true;
        }
        return departmentIds != null && departmentIds.contains(departmentId);
    }
    
    /**
     * 检查是否有项目权限
     * 
     * @param projectId 项目ID
     * @return 是否有权限
     */
    public boolean hasProjectPermission(Long projectId) {
        if (isUnlimited != null && isUnlimited) {
            return true;
        }
        return projectIds != null && projectIds.contains(projectId);
    }
    
    /**
     * 检查是否为部门模式
     * 
     * @return 是否为部门模式
     */
    public boolean isDepartmentMode() {
        return "DEPARTMENT".equals(organizationMode);
    }
    
    /**
     * 检查是否为项目模式
     * 
     * @return 是否为项目模式
     */
    public boolean isProjectMode() {
        return "PROJECT".equals(organizationMode);
    }
    
    /**
     * 获取当前模式下的组织ID列表
     * 
     * @return 组织ID列表
     */
    public List<Long> getCurrentModeOrganizationIds() {
        if (isDepartmentMode()) {
            return departmentIds;
        } else if (isProjectMode()) {
            return projectIds;
        }
        return null;
    }
    
    /**
     * 检查权限范围是否为空
     * 
     * @return 是否为空
     */
    public boolean isEmpty() {
        if (isUnlimited != null && isUnlimited) {
            return false;
        }
        
        if (isDepartmentMode()) {
            return departmentIds == null || departmentIds.isEmpty();
        } else if (isProjectMode()) {
            return projectIds == null || projectIds.isEmpty();
        }
        
        return true;
    }
}
