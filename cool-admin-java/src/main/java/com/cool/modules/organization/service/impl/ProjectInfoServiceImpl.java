package com.cool.modules.organization.service.impl;

import java.util.Date;
import java.util.List;

import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.cool.core.base.BaseServiceImpl;
import com.cool.core.exception.CoolException;
import com.cool.core.util.CoolSecurityUtil;
import com.cool.modules.base.entity.sys.BaseSysUserEntity;
import com.cool.modules.base.service.sys.BaseSysUserService;
import com.cool.modules.organization.entity.ProjectInfoEntity;
import com.cool.modules.organization.entity.UserOrganizationEntity;
import com.cool.modules.organization.enums.GlobalProjectRoleEnum;
import com.cool.modules.organization.enums.OrganizationModeEnum;
import com.cool.modules.organization.mapper.ProjectInfoMapper;
import com.cool.modules.organization.service.ProjectInfoService;
import com.cool.modules.organization.service.UserOrganizationService;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 项目信息服务实现
 * 
 * <AUTHOR> Admin
 * @since 2025-01-17
 */
@Slf4j
@Service
public class ProjectInfoServiceImpl extends BaseServiceImpl<ProjectInfoMapper, ProjectInfoEntity> 
        implements ProjectInfoService {
    
    @Autowired
    private BaseSysUserService baseSysUserService;
    @Autowired
    @Lazy
    private UserOrganizationService userOrganizationService;
    
    @Override
    public ProjectInfoEntity getByProjectCode(String projectCode) {
        if (StrUtil.isBlank(projectCode)) {
            return null;
        }
        
        return mapper.getByProjectCode(projectCode);
    }
    
    @Override
    public boolean existsByProjectCode(String projectCode, Long excludeId) {
        if (StrUtil.isBlank(projectCode)) {
            return false;
        }
        
        return mapper.existsByProjectCode(projectCode, excludeId);
    }
    
    @Override
    public List<ProjectInfoEntity> getByOwnerId(Long ownerId) {
        if (ownerId == null) {
            return List.of();
        }
        
        return mapper.getByOwnerId(ownerId);
    }
    
    @Override
    public List<ProjectInfoEntity> getByCreatorId(Long creatorId) {
        if (creatorId == null) {
            return List.of();
        }
        
        return mapper.getByCreatorId(creatorId);
    }
    
    @Override
    public List<ProjectInfoEntity> getUserProjects(Long userId) {
        if (userId == null) {
            return List.of();
        }
        
        List<ProjectInfoEntity> projects = mapper.getUserProjects(userId);
        
        // 填充项目统计信息
        projects.forEach(this::fillProjectStats);
        
        return projects;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean createProject(ProjectInfoEntity project) {
        // 参数验证
        validateProjectInfo(project);
        
        // 检查项目编码是否重复
        if (existsByProjectCode(project.getProjectCode(), null)) {
            throw new CoolException("项目编码已存在: " + project.getProjectCode());
        }
        
        try {
            // 设置创建信息
            Long currentUserId = CoolSecurityUtil.getCurrentUserId();
            project.setCreatorId(currentUserId);
            project.setCreateTime(new Date());
            
            // 如果没有指定负责人，默认为创建人
            if (project.getOwnerId() == null) {
                project.setOwnerId(currentUserId);
            }
            
            // 设置默认值
            if (project.getStatus() == null) {
                project.setStatus(0); // 规划中
            }
            if (project.getPriority() == null) {
                project.setPriority(2); // 中等优先级
            }
            if (project.getEnabled() == null) {
                project.setEnabled(true);
            }
            if (project.getOrderNum() == null) {
                project.setOrderNum(0);
            }
            
            // 保存项目
            boolean result = save(project);
            
            if (result) {
                // 自动将项目负责人添加为项目成员
                addProjectOwnerAsMember(project.getId(), project.getOwnerId(), currentUserId);
                
                log.info("项目创建成功: {} (ID: {})", project.getProjectName(), project.getId());
            }
            
            return result;
            
        } catch (Exception e) {
            log.error("创建项目失败", e);
            throw new CoolException("创建项目失败: " + e.getMessage());
        }
    }
    
    private void validateProjectInfo(ProjectInfoEntity project) {
        if (project == null) {
            throw new CoolException("项目信息不能为空");
        }
        
        if (StrUtil.isBlank(project.getProjectName())) {
            throw new CoolException("项目名称不能为空");
        }
        
        if (StrUtil.isBlank(project.getProjectCode())) {
            throw new CoolException("项目编码不能为空");
        }
        
        // 验证项目编码格式（只允许字母、数字、下划线、中划线）
        if (!project.getProjectCode().matches("^[a-zA-Z0-9_-]+$")) {
            throw new CoolException("项目编码只能包含字母、数字、下划线和中划线");
        }
        
        // 验证时间逻辑
        if (project.getPlannedStartTime() != null && project.getPlannedEndTime() != null) {
            if (project.getPlannedStartTime().after(project.getPlannedEndTime())) {
                throw new CoolException("计划开始时间不能晚于计划结束时间");
            }
        }
        
        if (project.getActualStartTime() != null && project.getActualEndTime() != null) {
            if (project.getActualStartTime().after(project.getActualEndTime())) {
                throw new CoolException("实际开始时间不能晚于实际结束时间");
            }
        }
    }
    
    private void addProjectOwnerAsMember(Long projectId, Long ownerId, Long assignerId) {
        try {
            // 检查负责人是否已经是项目成员
            if (!userOrganizationService.existsByUserAndOrganization(
                    ownerId, projectId, OrganizationModeEnum.PROJECT.getCode())) {
                
                // 将负责人添加为项目负责人角色
                userOrganizationService.addUserToOrganization(
                    ownerId, projectId, OrganizationModeEnum.PROJECT.getCode(), 
                    GlobalProjectRoleEnum.PROJECT_OWNER.getCode(), assignerId);
                
                log.debug("已将用户 {} 添加为项目 {} 的负责人", ownerId, projectId);
            }
        } catch (Exception e) {
            log.warn("添加项目负责人为成员失败", e);
        }
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateStatus(Long projectId, Integer status) {
        if (projectId == null || status == null) {
            return false;
        }
        
        try {
            int result = mapper.updateStatus(projectId, status);
            
            if (result > 0) {
                log.info("项目 {} 状态已更新为 {}", projectId, status);
            }
            
            return result > 0;
            
        } catch (Exception e) {
            log.error("更新项目状态失败", e);
            throw new CoolException("更新项目状态失败: " + e.getMessage());
        }
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchUpdateStatus(List<Long> projectIds, Integer status) {
        if (projectIds == null || projectIds.isEmpty() || status == null) {
            return 0;
        }
        
        try {
            int result = mapper.batchUpdateStatus(projectIds, status);
            
            log.info("批量更新 {} 个项目状态为 {}", result, status);
            
            return result;
            
        } catch (Exception e) {
            log.error("批量更新项目状态失败", e);
            throw new CoolException("批量更新项目状态失败: " + e.getMessage());
        }
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteProject(Long projectId) {
        if (projectId == null) {
            return false;
        }
        
        try {
            // 检查项目是否存在
            ProjectInfoEntity project = getById(projectId);
            if (project == null) {
                throw new CoolException("项目不存在");
            }
            
            // 检查用户是否有删除权限
            Long currentUserId = CoolSecurityUtil.getCurrentUserId();
            if (!hasProjectAccess(currentUserId, projectId)) {
                throw new CoolException("无权限删除此项目");
            }
            
            // 软删除项目（设置为已取消状态）
            boolean result = updateStatus(projectId, 4); // 4: 已取消
            
            if (result) {
                log.info("项目 {} 已被删除（软删除）", projectId);
            }
            
            return result;
            
        } catch (Exception e) {
            log.error("删除项目失败", e);
            throw new CoolException("删除项目失败: " + e.getMessage());
        }
    }
    
    @Override
    public boolean hasProjectAccess(Long userId, Long projectId) {
        if (userId == null || projectId == null) {
            return false;
        }
        
        // 系统管理员有所有权限
        String currentUser = CoolSecurityUtil.getAdminUsername();
        if ("admin".equals(currentUser)) {
            return true;
        }
        
        // 检查用户是否是项目成员
        return userOrganizationService.existsByUserAndOrganization(
            userId, projectId, OrganizationModeEnum.PROJECT.getCode());
    }
    
    @Override
    public ProjectInfoEntity getProjectWithStats(Long projectId) {
        if (projectId == null) {
            return null;
        }
        
        ProjectInfoEntity project = getById(projectId);
        if (project != null) {
            fillProjectStats(project);
            fillProjectUserInfo(project);
        }
        
        return project;
    }
    
    private void fillProjectStats(ProjectInfoEntity project) {
        if (project == null) {
            return;
        }
        
        try {
            // 获取项目成员数量
            List<UserOrganizationEntity> members = userOrganizationService.getByOrganizationIdAndType(
                project.getId(), OrganizationModeEnum.PROJECT.getCode());
            project.setMemberCount(members.size());
            
            // TODO: 获取项目任务数量和完成率
            // 这里需要根据具体的任务实体实现
            project.setTaskCount(0);
            project.setCompletionRate(java.math.BigDecimal.ZERO);
            
        } catch (Exception e) {
            log.warn("填充项目统计信息失败", e);
        }
    }
    
    private void fillProjectUserInfo(ProjectInfoEntity project) {
        if (project == null) {
            return;
        }
        
        try {
            // 填充负责人信息
            if (project.getOwnerId() != null) {
                BaseSysUserEntity owner = baseSysUserService.getById(project.getOwnerId());
                if (owner != null) {
                    project.setOwnerName(owner.getName());
                }
            }
            
            // 填充创建人信息
            if (project.getCreatorId() != null) {
                BaseSysUserEntity creator = baseSysUserService.getById(project.getCreatorId());
                if (creator != null) {
                    project.setCreatorName(creator.getName());
                }
            }
            
        } catch (Exception e) {
            log.warn("填充项目用户信息失败", e);
        }
    }
}
