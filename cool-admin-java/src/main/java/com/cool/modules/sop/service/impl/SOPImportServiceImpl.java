package com.cool.modules.sop.service.impl;

import com.cool.modules.sop.dto.*;
import com.cool.modules.sop.entity.SOPScenarioEntity;
import com.cool.modules.sop.entity.SOPStepEntity;
import com.cool.modules.sop.service.SOPImportService;
import com.cool.modules.sop.service.SOPScenarioService;
import com.cool.modules.sop.service.SOPStepService;
import com.mybatisflex.core.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * SOP导入服务实现
 */
@Slf4j
@Service
public class SOPImportServiceImpl implements SOPImportService {
    
    @Autowired
    private SOPScenarioService sopScenarioService;
    
    @Autowired
    private SOPStepService sopStepService;
    
    // 版本号正则表达式
    private static final Pattern VERSION_PATTERN = Pattern.compile("^\\d+\\.\\d+(\\.\\d+)?$");
    
    // Excel列映射
    private static final Map<String, Integer> SCENARIO_COLUMNS = Map.of(
        "industryName", 0,
        "stage", 1,
        "moduleCode", 2,
        "moduleName", 3,
        "scenarioCode", 4,
        "scenarioName", 5,
        "executionCycle", 6,
        "version", 7,
        "description", 8,
        "difficultyLevel", 9
    );
    
    private static final Map<String, Integer> STEP_COLUMNS = Map.of(
        "stepCode", 0,
        "stepName", 1,
        "stepDescription", 2,
        "stepOrder", 3,
        "entityTouchpoint", 4,
        "userActivity", 5,
        "employeeBehavior", 6,
        "workHighlight", 7,
        "employeeRole", 8,
        "estimatedTime", 9
    );
    
    @Override
    public SOPImportResult previewImport(MultipartFile file) {
        try {
            log.info("开始预览SOP导入文件: {}", file.getOriginalFilename());
            
            // 解析Excel文件
            List<SOPExcelData> sopDataList = parseExcelFile(file);
            
            // 校验数据
            SOPImportResult validationResult = validateSOPData(sopDataList);
            
            // 检查版本冲突
            List<SOPImportResult.VersionConflict> conflicts = checkVersionConflicts(sopDataList);
            validationResult.setVersionConflicts(conflicts);
            
            // 生成统计信息
            SOPImportResult.ImportStatistics statistics = SOPImportResult.ImportStatistics.builder()
                .totalScenarios(sopDataList.size())
                .totalSteps(sopDataList.stream().mapToInt(data -> data.getSteps().size()).sum())
                .build();
            validationResult.setStatistics(statistics);
            
            log.info("SOP导入预览完成，场景数: {}, 步骤数: {}", 
                statistics.getTotalScenarios(), statistics.getTotalSteps());
            
            return validationResult;
            
        } catch (Exception e) {
            log.error("SOP导入预览失败: {}", e.getMessage(), e);
            return SOPImportResult.builder()
                .success(false)
                .message("导入预览失败: " + e.getMessage())
                .build();
        }
    }
    
    @Override
    @Transactional
    public SOPImportResult importSOP(SOPImportRequest request) {
        try {
            log.info("开始执行SOP导入，操作人: {}", request.getOperatorName());
            
            // 解析Excel文件
            List<SOPExcelData> sopDataList = parseExcelFile(request.getFile());
            
            // 校验数据
            SOPImportResult validationResult = validateSOPData(sopDataList);
            if (!validationResult.getSuccess()) {
                return validationResult;
            }
            
            // 检查版本冲突
            List<SOPImportResult.VersionConflict> conflicts = checkVersionConflicts(sopDataList);
            
            // 如果有冲突且不强制覆盖，返回冲突信息
            if (!conflicts.isEmpty() && !request.getForceOverride()) {
                return SOPImportResult.builder()
                    .success(false)
                    .message("存在版本冲突，请确认是否覆盖")
                    .versionConflicts(conflicts)
                    .build();
            }
            
            // 备份现有数据
            String backupId = null;
            if (request.getBackupExisting()) {
                List<String> scenarioCodes = sopDataList.stream()
                    .map(data -> data.getScenario().getScenarioCode())
                    .collect(Collectors.toList());
                backupId = backupExistingData(scenarioCodes);
            }
            
            // 执行导入
            List<SOPImportResult.ImportedScenario> importedScenarios = new ArrayList<>();
            int successCount = 0;
            int failedCount = 0;
            
            for (SOPExcelData sopData : sopDataList) {
                try {
                    SOPImportResult.ImportedScenario imported = importSingleScenario(sopData, request);
                    importedScenarios.add(imported);
                    successCount++;
                } catch (Exception e) {
                    log.error("导入场景失败: {}, 错误: {}", 
                        sopData.getScenario().getScenarioCode(), e.getMessage());
                    failedCount++;
                }
            }
            
            // 构建结果
            SOPImportResult.ImportStatistics statistics = SOPImportResult.ImportStatistics.builder()
                .totalScenarios(sopDataList.size())
                .successScenarios(successCount)
                .failedScenarios(failedCount)
                .totalSteps(sopDataList.stream().mapToInt(data -> data.getSteps().size()).sum())
                .build();
            
            log.info("SOP导入完成，成功: {}, 失败: {}, 备份ID: {}", 
                successCount, failedCount, backupId);
            
            return SOPImportResult.builder()
                .success(failedCount == 0)
                .message(String.format("导入完成，成功: %d, 失败: %d", successCount, failedCount))
                .statistics(statistics)
                .importedScenarios(importedScenarios)
                .build();
            
        } catch (Exception e) {
            log.error("SOP导入失败: {}", e.getMessage(), e);
            return SOPImportResult.builder()
                .success(false)
                .message("导入失败: " + e.getMessage())
                .build();
        }
    }
    
    @Override
    public List<SOPExcelData> parseExcelFile(MultipartFile file) {
        List<SOPExcelData> result = new ArrayList<>();
        
        try (InputStream inputStream = file.getInputStream();
             Workbook workbook = new XSSFWorkbook(inputStream)) {
            
            // 解析场景工作表
            Sheet scenarioSheet = workbook.getSheet("场景信息");
            if (scenarioSheet == null) {
                scenarioSheet = workbook.getSheetAt(0); // 默认第一个工作表
            }
            
            // 解析步骤工作表
            Sheet stepSheet = workbook.getSheet("步骤信息");
            if (stepSheet == null && workbook.getNumberOfSheets() > 1) {
                stepSheet = workbook.getSheetAt(1); // 默认第二个工作表
            }
            
            // 解析场景数据
            Map<String, SOPExcelData.ScenarioData> scenarioMap = parseScenarioSheet(scenarioSheet);
            
            // 解析步骤数据
            Map<String, List<SOPExcelData.StepData>> stepMap = parseStepSheet(stepSheet);
            
            // 组合数据
            for (Map.Entry<String, SOPExcelData.ScenarioData> entry : scenarioMap.entrySet()) {
                String scenarioCode = entry.getKey();
                SOPExcelData.ScenarioData scenario = entry.getValue();
                List<SOPExcelData.StepData> steps = stepMap.getOrDefault(scenarioCode, new ArrayList<>());
                
                result.add(SOPExcelData.builder()
                    .scenario(scenario)
                    .steps(steps)
                    .build());
            }
            
        } catch (IOException e) {
            log.error("解析Excel文件失败: {}", e.getMessage(), e);
            throw new RuntimeException("解析Excel文件失败: " + e.getMessage());
        }
        
        return result;
    }
    
    private Map<String, SOPExcelData.ScenarioData> parseScenarioSheet(Sheet sheet) {
        Map<String, SOPExcelData.ScenarioData> result = new HashMap<>();
        
        if (sheet == null) {
            return result;
        }
        
        // 跳过标题行，从第二行开始
        for (int i = 1; i <= sheet.getLastRowNum(); i++) {
            Row row = sheet.getRow(i);
            if (row == null) continue;
            
            try {
                SOPExcelData.ScenarioData scenario = parseScenarioRow(row, i + 1);
                if (scenario != null && scenario.getScenarioCode() != null) {
                    result.put(scenario.getScenarioCode(), scenario);
                }
            } catch (Exception e) {
                log.warn("解析场景行失败，行号: {}, 错误: {}", i + 1, e.getMessage());
            }
        }
        
        return result;
    }
    
    private SOPExcelData.ScenarioData parseScenarioRow(Row row, int rowNumber) {
        return SOPExcelData.ScenarioData.builder()
            .industryName(getCellStringValue(row, SCENARIO_COLUMNS.get("industryName")))
            .stage(getCellStringValue(row, SCENARIO_COLUMNS.get("stage")))
            .moduleCode(getCellStringValue(row, SCENARIO_COLUMNS.get("moduleCode")))
            .moduleName(getCellStringValue(row, SCENARIO_COLUMNS.get("moduleName")))
            .scenarioCode(getCellStringValue(row, SCENARIO_COLUMNS.get("scenarioCode")))
            .scenarioName(getCellStringValue(row, SCENARIO_COLUMNS.get("scenarioName")))
            .executionCycle(getCellStringValue(row, SCENARIO_COLUMNS.get("executionCycle")))
            .version(getCellStringValue(row, SCENARIO_COLUMNS.get("version")))
            .description(getCellStringValue(row, SCENARIO_COLUMNS.get("description")))
            .difficultyLevel(getCellIntValue(row, SCENARIO_COLUMNS.get("difficultyLevel")))
            .rowNumber(rowNumber)
            .build();
    }
    
    private Map<String, List<SOPExcelData.StepData>> parseStepSheet(Sheet sheet) {
        Map<String, List<SOPExcelData.StepData>> result = new HashMap<>();
        
        if (sheet == null) {
            return result;
        }
        
        // 跳过标题行，从第二行开始
        for (int i = 1; i <= sheet.getLastRowNum(); i++) {
            Row row = sheet.getRow(i);
            if (row == null) continue;
            
            try {
                SOPExcelData.StepData step = parseStepRow(row, i + 1);
                if (step != null) {
                    // 假设步骤编码格式为 "场景编码.步骤编码"
                    String scenarioCode = extractScenarioCodeFromStep(step.getStepCode());
                    result.computeIfAbsent(scenarioCode, k -> new ArrayList<>()).add(step);
                }
            } catch (Exception e) {
                log.warn("解析步骤行失败，行号: {}, 错误: {}", i + 1, e.getMessage());
            }
        }
        
        return result;
    }
    
    private SOPExcelData.StepData parseStepRow(Row row, int rowNumber) {
        return SOPExcelData.StepData.builder()
            .stepCode(getCellStringValue(row, STEP_COLUMNS.get("stepCode")))
            .stepName(getCellStringValue(row, STEP_COLUMNS.get("stepName")))
            .stepDescription(getCellStringValue(row, STEP_COLUMNS.get("stepDescription")))
            .stepOrder(getCellIntValue(row, STEP_COLUMNS.get("stepOrder")))
            .entityTouchpoint(getCellStringValue(row, STEP_COLUMNS.get("entityTouchpoint")))
            .userActivity(getCellStringValue(row, STEP_COLUMNS.get("userActivity")))
            .employeeBehavior(getCellStringValue(row, STEP_COLUMNS.get("employeeBehavior")))
            .workHighlight(getCellStringValue(row, STEP_COLUMNS.get("workHighlight")))
            .employeeRole(getCellStringValue(row, STEP_COLUMNS.get("employeeRole")))
            .estimatedTime(getCellIntValue(row, STEP_COLUMNS.get("estimatedTime")))
            .stepType("normal") // 默认值
            .isRequired(true) // 默认值
            .rowNumber(rowNumber)
            .build();
    }
    
    private String getCellStringValue(Row row, Integer columnIndex) {
        if (columnIndex == null || row == null) return null;
        
        Cell cell = row.getCell(columnIndex);
        if (cell == null) return null;
        
        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue().trim();
            case NUMERIC:
                return String.valueOf((long) cell.getNumericCellValue());
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            default:
                return null;
        }
    }
    
    private Integer getCellIntValue(Row row, Integer columnIndex) {
        if (columnIndex == null || row == null) return null;
        
        Cell cell = row.getCell(columnIndex);
        if (cell == null) return null;
        
        try {
            if (cell.getCellType() == CellType.NUMERIC) {
                return (int) cell.getNumericCellValue();
            } else if (cell.getCellType() == CellType.STRING) {
                String value = cell.getStringCellValue().trim();
                return value.isEmpty() ? null : Integer.parseInt(value);
            }
        } catch (NumberFormatException e) {
            log.warn("解析整数值失败: {}", cell.toString());
        }
        
        return null;
    }
    
    private String extractScenarioCodeFromStep(String stepCode) {
        if (stepCode == null || stepCode.isEmpty()) {
            return null;
        }

        // 假设步骤编码格式为 "S11.1.1" 或 "S11-1-1"
        String[] parts = stepCode.split("[.\\-]");
        return parts.length > 0 ? parts[0] : stepCode;
    }

    @Override
    public SOPImportResult validateSOPData(List<SOPExcelData> sopDataList) {
        List<SOPImportResult.ValidationError> errors = new ArrayList<>();

        for (SOPExcelData sopData : sopDataList) {
            // 校验场景数据
            validateScenarioData(sopData.getScenario(), errors);

            // 校验步骤数据
            for (SOPExcelData.StepData step : sopData.getSteps()) {
                validateStepData(step, errors);
            }
        }

        boolean success = errors.isEmpty();
        String message = success ? "数据校验通过" : String.format("发现 %d 个校验错误", errors.size());

        return SOPImportResult.builder()
            .success(success)
            .message(message)
            .validationErrors(errors)
            .build();
    }

    private void validateScenarioData(SOPExcelData.ScenarioData scenario, List<SOPImportResult.ValidationError> errors) {
        // 必填字段校验
        if (isBlank(scenario.getScenarioCode())) {
            errors.add(createValidationError("MISSING_FIELD", "scenarioCode", null,
                "场景编码不能为空", scenario.getRowNumber(), null));
        }

        if (isBlank(scenario.getScenarioName())) {
            errors.add(createValidationError("MISSING_FIELD", "scenarioName", null,
                "场景名称不能为空", scenario.getRowNumber(), scenario.getScenarioCode()));
        }

        if (isBlank(scenario.getModuleCode())) {
            errors.add(createValidationError("MISSING_FIELD", "moduleCode", null,
                "模块编码不能为空", scenario.getRowNumber(), scenario.getScenarioCode()));
        }

        // 版本号格式校验
        if (!isBlank(scenario.getVersion()) && !VERSION_PATTERN.matcher(scenario.getVersion()).matches()) {
            errors.add(createValidationError("INVALID_FORMAT", "version", scenario.getVersion(),
                "版本号格式不正确，应为 x.y 或 x.y.z 格式", scenario.getRowNumber(), scenario.getScenarioCode()));
        }

        // 难度等级校验
        if (scenario.getDifficultyLevel() != null &&
            (scenario.getDifficultyLevel() < 1 || scenario.getDifficultyLevel() > 5)) {
            errors.add(createValidationError("INVALID_FORMAT", "difficultyLevel",
                String.valueOf(scenario.getDifficultyLevel()),
                "难度等级必须在1-5之间", scenario.getRowNumber(), scenario.getScenarioCode()));
        }
    }

    private void validateStepData(SOPExcelData.StepData step, List<SOPImportResult.ValidationError> errors) {
        // 必填字段校验
        if (isBlank(step.getStepCode())) {
            errors.add(createValidationError("MISSING_FIELD", "stepCode", null,
                "步骤编码不能为空", step.getRowNumber(), null));
        }

        if (isBlank(step.getStepName())) {
            errors.add(createValidationError("MISSING_FIELD", "stepName", null,
                "步骤名称不能为空", step.getRowNumber(), step.getStepCode()));
        }

        if (isBlank(step.getStepDescription())) {
            errors.add(createValidationError("MISSING_FIELD", "stepDescription", null,
                "步骤描述不能为空", step.getRowNumber(), step.getStepCode()));
        }

        // 步骤顺序校验
        if (step.getStepOrder() == null || step.getStepOrder() <= 0) {
            errors.add(createValidationError("INVALID_FORMAT", "stepOrder",
                String.valueOf(step.getStepOrder()),
                "步骤顺序必须大于0", step.getRowNumber(), step.getStepCode()));
        }

        // 预估时间校验
        if (step.getEstimatedTime() != null && step.getEstimatedTime() <= 0) {
            errors.add(createValidationError("INVALID_FORMAT", "estimatedTime",
                String.valueOf(step.getEstimatedTime()),
                "预估时间必须大于0", step.getRowNumber(), step.getStepCode()));
        }
    }

    private SOPImportResult.ValidationError createValidationError(String type, String field, String value,
                                                                  String message, Integer rowNumber, String scenarioCode) {
        return SOPImportResult.ValidationError.builder()
            .type(type)
            .field(field)
            .value(value)
            .message(message)
            .rowNumber(rowNumber)
            .scenarioCode(scenarioCode)
            .build();
    }

    private boolean isBlank(String str) {
        return str == null || str.trim().isEmpty();
    }

    @Override
    public List<SOPImportResult.VersionConflict> checkVersionConflicts(List<SOPExcelData> sopDataList) {
        List<SOPImportResult.VersionConflict> conflicts = new ArrayList<>();

        for (SOPExcelData sopData : sopDataList) {
            String scenarioCode = sopData.getScenario().getScenarioCode();
            String importVersion = sopData.getScenario().getVersion();

            if (isBlank(scenarioCode)) continue;

            // 查询现有场景
            QueryWrapper wrapper = QueryWrapper.create()
                .eq("scenario_code", scenarioCode)
                .orderBy("version", false)
                .limit(1);

            SOPScenarioEntity existing = sopScenarioService.getOne(wrapper);

            if (existing != null) {
                String existingVersion = existing.getVersion();
                String conflictType = determineConflictType(existingVersion, importVersion);

                if (!"NO_CONFLICT".equals(conflictType)) {
                    conflicts.add(SOPImportResult.VersionConflict.builder()
                        .scenarioCode(scenarioCode)
                        .scenarioName(sopData.getScenario().getScenarioName())
                        .existingVersion(existingVersion)
                        .importVersion(importVersion)
                        .conflictType(conflictType)
                        .recommendation(getRecommendation(conflictType))
                        .build());
                }
            }
        }

        return conflicts;
    }

    private String determineConflictType(String existingVersion, String importVersion) {
        if (isBlank(importVersion)) {
            return "VERSION_MISSING";
        }

        if (isBlank(existingVersion)) {
            return "NO_CONFLICT";
        }

        int comparison = compareVersions(existingVersion, importVersion);

        if (comparison > 0) {
            return "VERSION_LOWER";
        } else if (comparison == 0) {
            return "VERSION_SAME";
        } else {
            return "NO_CONFLICT";
        }
    }

    private String getRecommendation(String conflictType) {
        switch (conflictType) {
            case "VERSION_LOWER":
                return "SKIP";
            case "VERSION_SAME":
                return "MERGE";
            case "VERSION_MISSING":
                return "UPGRADE";
            default:
                return "UPGRADE";
        }
    }

    private int compareVersions(String version1, String version2) {
        String[] parts1 = version1.split("\\.");
        String[] parts2 = version2.split("\\.");

        int maxLength = Math.max(parts1.length, parts2.length);

        for (int i = 0; i < maxLength; i++) {
            int v1 = i < parts1.length ? Integer.parseInt(parts1[i]) : 0;
            int v2 = i < parts2.length ? Integer.parseInt(parts2[i]) : 0;

            if (v1 != v2) {
                return Integer.compare(v1, v2);
            }
        }

        return 0;
    }

    @Override
    public String generateNewVersion(String currentVersion, String strategy) {
        if (isBlank(currentVersion)) {
            return "1.0.0";
        }

        String[] parts = currentVersion.split("\\.");
        int major = parts.length > 0 ? Integer.parseInt(parts[0]) : 1;
        int minor = parts.length > 1 ? Integer.parseInt(parts[1]) : 0;
        int patch = parts.length > 2 ? Integer.parseInt(parts[2]) : 0;

        switch (strategy.toUpperCase()) {
            case "MAJOR":
                return (major + 1) + ".0.0";
            case "MINOR":
                return major + "." + (minor + 1) + ".0";
            case "PATCH":
                return major + "." + minor + "." + (patch + 1);
            default:
                return major + "." + (minor + 1) + ".0";
        }
    }

    @Override
    public String backupExistingData(List<String> scenarioCodes) {
        // 生成备份ID
        String backupId = "backup_" + System.currentTimeMillis();

        try {
            // 这里可以实现具体的备份逻辑
            // 例如：将现有数据导出到文件或备份表
            log.info("开始备份SOP数据，场景数量: {}, 备份ID: {}", scenarioCodes.size(), backupId);

            // TODO: 实现具体的备份逻辑
            // 1. 查询现有场景和步骤数据
            // 2. 序列化到文件或备份表
            // 3. 记录备份信息

            log.info("SOP数据备份完成，备份ID: {}", backupId);
            return backupId;

        } catch (Exception e) {
            log.error("备份SOP数据失败: {}", e.getMessage(), e);
            throw new RuntimeException("备份失败: " + e.getMessage());
        }
    }

    @Override
    public Boolean restoreBackupData(String backupId) {
        try {
            log.info("开始恢复SOP数据，备份ID: {}", backupId);

            // TODO: 实现具体的恢复逻辑
            // 1. 根据备份ID查找备份数据
            // 2. 恢复场景和步骤数据
            // 3. 清理当前数据

            log.info("SOP数据恢复完成，备份ID: {}", backupId);
            return true;

        } catch (Exception e) {
            log.error("恢复SOP数据失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public byte[] getImportTemplate() {
        try (Workbook workbook = new XSSFWorkbook();
             ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {

            // 创建场景信息工作表
            Sheet scenarioSheet = workbook.createSheet("场景信息");
            createScenarioTemplateHeaders(scenarioSheet);

            // 创建步骤信息工作表
            Sheet stepSheet = workbook.createSheet("步骤信息");
            createStepTemplateHeaders(stepSheet);

            // 创建说明工作表
            Sheet instructionSheet = workbook.createSheet("导入说明");
            createInstructionSheet(instructionSheet);

            workbook.write(outputStream);
            return outputStream.toByteArray();

        } catch (IOException e) {
            log.error("生成导入模板失败: {}", e.getMessage(), e);
            throw new RuntimeException("生成导入模板失败: " + e.getMessage());
        }
    }

    private void createScenarioTemplateHeaders(Sheet sheet) {
        Row headerRow = sheet.createRow(0);
        String[] headers = {
            "行业名称", "阶段", "模块编码", "模块名称", "场景编码",
            "场景名称", "执行周期", "版本号", "场景描述", "难度等级"
        };

        for (int i = 0; i < headers.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(headers[i]);
        }

        // 添加示例数据
        Row exampleRow = sheet.createRow(1);
        exampleRow.createCell(0).setCellValue("物业管理");
        exampleRow.createCell(1).setCellValue("日常运营");
        exampleRow.createCell(2).setCellValue("M01");
        exampleRow.createCell(3).setCellValue("客户服务");
        exampleRow.createCell(4).setCellValue("S11");
        exampleRow.createCell(5).setCellValue("客户满意度调查");
        exampleRow.createCell(6).setCellValue("月度");
        exampleRow.createCell(7).setCellValue("1.0");
        exampleRow.createCell(8).setCellValue("定期进行客户满意度调查，收集反馈意见");
        exampleRow.createCell(9).setCellValue(3);
    }

    private void createStepTemplateHeaders(Sheet sheet) {
        Row headerRow = sheet.createRow(0);
        String[] headers = {
            "步骤编码", "步骤名称", "步骤描述", "步骤顺序", "实体触点",
            "用户活动", "员工行为", "工作亮点", "员工角色", "预估时间(分钟)"
        };

        for (int i = 0; i < headers.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(headers[i]);
        }

        // 添加示例数据
        Row exampleRow = sheet.createRow(1);
        exampleRow.createCell(0).setCellValue("S11.1");
        exampleRow.createCell(1).setCellValue("制定调查问卷");
        exampleRow.createCell(2).setCellValue("根据调查目标设计问卷内容");
        exampleRow.createCell(3).setCellValue(1);
        exampleRow.createCell(4).setCellValue("调查问卷");
        exampleRow.createCell(5).setCellValue("填写问卷");
        exampleRow.createCell(6).setCellValue("设计问卷，确保问题清晰明确");
        exampleRow.createCell(7).setCellValue("问卷设计专业，覆盖全面");
        exampleRow.createCell(8).setCellValue("客服专员");
        exampleRow.createCell(9).setCellValue(60);
    }

    private void createInstructionSheet(Sheet sheet) {
        Row titleRow = sheet.createRow(0);
        titleRow.createCell(0).setCellValue("SOP导入说明");

        String[] instructions = {
            "",
            "1. 文件格式要求：",
            "   - 支持 .xlsx 格式的Excel文件",
            "   - 必须包含'场景信息'和'步骤信息'两个工作表",
            "",
            "2. 场景信息字段说明：",
            "   - 场景编码：必填，唯一标识，如 S11",
            "   - 场景名称：必填，场景的名称描述",
            "   - 模块编码：必填，所属模块的编码",
            "   - 版本号：必填，格式为 x.y 或 x.y.z",
            "",
            "3. 步骤信息字段说明：",
            "   - 步骤编码：必填，格式为 场景编码.步骤编码，如 S11.1",
            "   - 步骤名称：必填，步骤的名称描述",
            "   - 步骤顺序：必填，数字，表示执行顺序",
            "",
            "4. 版本管理：",
            "   - 如果场景编码已存在，系统会检查版本冲突",
            "   - 支持版本升级策略：MAJOR(主版本)、MINOR(次版本)、PATCH(补丁版本)",
            "",
            "5. 注意事项：",
            "   - 导入前建议先使用预览功能检查数据",
            "   - 系统会自动备份现有数据（如果选择备份选项）",
            "   - 发生错误时可以使用备份数据恢复"
        };

        for (int i = 0; i < instructions.length; i++) {
            Row row = sheet.createRow(i + 1);
            row.createCell(0).setCellValue(instructions[i]);
        }
    }

    private SOPImportResult.ImportedScenario importSingleScenario(SOPExcelData sopData, SOPImportRequest request) {
        SOPExcelData.ScenarioData scenarioData = sopData.getScenario();
        String scenarioCode = scenarioData.getScenarioCode();

        // 检查是否存在现有场景
        QueryWrapper wrapper = QueryWrapper.create().eq("scenario_code", scenarioCode);
        SOPScenarioEntity existing = sopScenarioService.getOne(wrapper);

        String action;
        Long scenarioId;

        if (existing != null) {
            // 更新现有场景
            updateScenarioFromData(existing, scenarioData, request);
            sopScenarioService.updateById(existing);
            scenarioId = existing.getId();
            action = "UPDATED";

            // 删除现有步骤
            sopStepService.deleteByTemplateId(scenarioId);
        } else {
            // 创建新场景
            SOPScenarioEntity newScenario = createScenarioFromData(scenarioData);
            sopScenarioService.save(newScenario);
            scenarioId = newScenario.getId();
            action = "CREATED";
        }

        // 导入步骤
        for (SOPExcelData.StepData stepData : sopData.getSteps()) {
            SOPStepEntity step = createStepFromData(stepData, scenarioId, scenarioData);
            sopStepService.save(step);
        }

        return SOPImportResult.ImportedScenario.builder()
            .scenarioId(scenarioId)
            .scenarioCode(scenarioCode)
            .scenarioName(scenarioData.getScenarioName())
            .version(scenarioData.getVersion())
            .action(action)
            .stepCount(sopData.getSteps().size())
            .build();
    }

    private void updateScenarioFromData(SOPScenarioEntity scenario, SOPExcelData.ScenarioData data, SOPImportRequest request) {
        scenario.setIndustryName(data.getIndustryName());
        scenario.setStage(data.getStage());
        scenario.setModuleCode(data.getModuleCode());
        scenario.setModuleName(data.getModuleName());
        scenario.setScenarioName(data.getScenarioName());
        scenario.setExecutionCycle(data.getExecutionCycle());
        scenario.setExecutionFrequency(data.getExecutionFrequency());
        scenario.setExecutionCount(data.getExecutionCount());
        scenario.setDescription(data.getDescription());
        scenario.setDifficultyLevel(data.getDifficultyLevel());
        scenario.setQualityStandard(data.getQualityStandard());
        scenario.setSuccessCriteria(data.getSuccessCriteria());
        scenario.setRiskPoints(data.getRiskPoints());
        scenario.setAttentionPoints(data.getAttentionPoints());
        scenario.setApplicableArea(data.getApplicableArea());

        // 版本管理
        if (!isBlank(data.getVersion())) {
            scenario.setVersion(data.getVersion());
        } else {
            String newVersion = generateNewVersion(scenario.getVersion(), request.getVersionStrategy());
            scenario.setVersion(newVersion);
        }

        scenario.setUpdateTime(new Date());
    }

    private SOPScenarioEntity createScenarioFromData(SOPExcelData.ScenarioData data) {
        SOPScenarioEntity scenario = new SOPScenarioEntity();
        scenario.setIndustryName(data.getIndustryName());
        scenario.setStage(data.getStage());
        scenario.setModuleCode(data.getModuleCode());
        scenario.setModuleName(data.getModuleName());
        scenario.setScenarioCode(data.getScenarioCode());
        scenario.setScenarioName(data.getScenarioName());
        scenario.setExecutionCycle(data.getExecutionCycle());
        scenario.setExecutionFrequency(data.getExecutionFrequency());
        scenario.setExecutionCount(data.getExecutionCount());
        scenario.setVersion(isBlank(data.getVersion()) ? "1.0" : data.getVersion());
        scenario.setStatus(1); // 启用状态
        scenario.setDescription(data.getDescription());
        scenario.setDifficultyLevel(data.getDifficultyLevel() != null ? data.getDifficultyLevel() : 3);
        scenario.setQualityStandard(data.getQualityStandard());
        scenario.setSuccessCriteria(data.getSuccessCriteria());
        scenario.setRiskPoints(data.getRiskPoints());
        scenario.setAttentionPoints(data.getAttentionPoints());
        scenario.setApplicableArea(data.getApplicableArea());
        scenario.setCreateTime(new Date());
        scenario.setUpdateTime(new Date());
        return scenario;
    }

    private SOPStepEntity createStepFromData(SOPExcelData.StepData data, Long scenarioId, SOPExcelData.ScenarioData scenarioData) {
        SOPStepEntity step = new SOPStepEntity();
        step.setSopId(scenarioId);
        step.setIndustryName(scenarioData.getIndustryName());
        step.setStage(scenarioData.getStage());
        step.setModuleCode(scenarioData.getModuleCode());
        step.setModuleName(scenarioData.getModuleName());
        step.setScenarioCode(scenarioData.getScenarioCode());
        step.setScenarioName(scenarioData.getScenarioName());
        step.setExecutionCycle(scenarioData.getExecutionCycle());
        step.setStepCode(data.getStepCode());
        step.setStepName(data.getStepName());
        step.setStepDescription(data.getStepDescription());
        step.setStepOrder(data.getStepOrder());
        step.setEntityTouchpoint(data.getEntityTouchpoint());
        step.setUserActivity(data.getUserActivity());
        step.setEmployeeBehavior(data.getEmployeeBehavior());
        step.setWorkHighlight(data.getWorkHighlight());
        step.setEmployeeRole(data.getEmployeeRole());
        step.setStepType(data.getStepType() != null ? data.getStepType() : "normal");
        step.setIsRequired(data.getIsRequired() != null ? data.getIsRequired() : true);
        step.setEstimatedTime(data.getEstimatedTime() != null ? data.getEstimatedTime() : 30);
        step.setSkillRequirements(data.getSkillRequirements());
        step.setToolsRequired(data.getToolsRequired());
        step.setQualityCheckPoints(data.getQualityCheckPoints());
        step.setRiskWarnings(data.getRiskWarnings());
        step.setSuccessCriteria(data.getSuccessCriteria());
        step.setFailureHandling(data.getFailureHandling());
        step.setNextStepCondition(data.getNextStepCondition());
        step.setParallelSteps(data.getParallelSteps());
        step.setPrerequisiteSteps(data.getPrerequisiteSteps());
        step.setVersion(data.getVersion() != null ? data.getVersion() : scenarioData.getVersion());
        step.setStatus(1); // 启用状态
        step.setCreateTime(new Date());
        step.setUpdateTime(new Date());
        return step;
    }
}
