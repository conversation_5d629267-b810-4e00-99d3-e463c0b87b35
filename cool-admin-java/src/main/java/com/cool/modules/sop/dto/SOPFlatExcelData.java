package com.cool.modules.sop.dto;

import lombok.Data;
import lombok.Builder;
import java.util.List;

/**
 * SOP扁平化Excel数据结构
 * 对应模板头：行业名称	项目阶段	模块编号	模块名称	场景编号	场景名称	执行周期	步骤	实体触点	用户活动	员工行为	工作亮点	员工角色	前台/中后台	支持系统	相关附件
 */
@Data
@Builder
public class SOPFlatExcelData {
    
    /**
     * SOP数据行列表（扁平化结构）
     */
    private List<SOPRowData> rows;
    
    /**
     * SOP数据行（包含场景和步骤的完整信息）
     */
    @Data
    @Builder
    public static class SOPRowData {
        private String industryName;        // 行业名称
        private String projectStage;        // 项目阶段
        private String moduleCode;          // 模块编号
        private String moduleName;          // 模块名称
        private String scenarioCode;        // 场景编号
        private String scenarioName;        // 场景名称
        private String executionCycle;      // 执行周期
        private String step;                // 步骤
        private String stepOrder;           // 步骤编号（从步骤文本中提取）
        private String entityTouchpoint;    // 实体触点
        private String userActivity;        // 用户活动
        private String employeeBehavior;    // 员工行为
        private String workHighlight;       // 工作亮点
        private String employeeRole;        // 员工角色
        private String frontBackend;        // 前台/中后台
        private String supportSystem;       // 支持系统
        private String relatedAttachments;  // 相关附件
        
        // Excel行号，用于错误定位
        private Integer rowNumber;
    }
}
