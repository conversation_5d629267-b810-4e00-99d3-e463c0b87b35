package com.cool.modules.sop.service;

import com.cool.modules.sop.entity.AiTaskGenerateRecordEntity;
import java.util.List;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;
import com.cool.modules.sop.dto.ai.MultiDepartmentGenerateResponse;
import com.cool.modules.sop.dto.ai.TaskGenerateRequest;
import com.cool.modules.sop.enums.TaskGenerateMode;
import com.cool.core.base.BaseService;
import com.mybatisflex.core.paginate.Page;
import java.util.Map;
import com.cool.modules.sop.dto.ai.PreviewResultDTO;

public interface AiTaskGenerateRecordService extends BaseService<AiTaskGenerateRecordEntity> {
    Long submitAsyncTask(AiTaskGenerateRecordEntity record);

    void asyncGenerateTask(Long taskId);

    SseEmitter subscribe(Long taskId);

    AiTaskGenerateRecordEntity getById(Long id);
    Page<AiTaskGenerateRecordEntity> page(int page, int size, String taskDesc, Integer status, Long userId, boolean isAdmin);
    boolean retry(Long id);
    boolean cancel(Long id);
    boolean removeByIds(List<Long> ids);
    Long submitPreviewTask(TaskGenerateRequest request);
    void processPreviewGeneration(Long recordId);
    void pushSse(Long taskId, Object data);
    
    // 新增统一的任务生成方法
    /**
     * 提交异步任务生成请求
     * @param request 任务生成请求
     * @param mode 生成模式
     * @return 任务记录ID
     */
    Long submitAsyncTaskGeneration(TaskGenerateRequest request, TaskGenerateMode mode);
    
    /**
     * 异步处理任务生成
     * @param recordId 记录ID
     * @param mode 生成模式
     */
    void processAsyncTaskGeneration(Long recordId, TaskGenerateMode mode);
    
    /**
     * 接受预览结果并生成正式任务
     * @param previewRecordId 预览记录ID
     * @return 正式任务记录ID
     */
    Long acceptPreviewAndGenerate(Long previewRecordId);
    
    /**
     * 调整预览任务的执行人分配
     * @param recordId 预览记录ID
     * @param taskIndex 任务索引
     * @param newAssigneeId 新执行人ID
     * @param reason 调整原因
     * @return 调整结果
     */
    java.util.Map<String, Object> adjustPreviewAssignment(Long recordId, Integer taskIndex, Long newAssigneeId, String reason);
    
    // 保持向后兼容，但标记为过时
    @Deprecated
    MultiDepartmentGenerateResponse generateTasksByAI(TaskGenerateRequest request);
    // 其它业务方法...
    /**
     * 保存前端最新预览结果（兼容手动分配 assignmentType=MANUAL）
     */
    void savePreviewResults(Long recordId, PreviewResultDTO previewData);
} 