package com.cool.modules.sop.entity;

import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import com.tangzc.mybatisflex.autotable.annotation.ColumnDefine;
import java.util.Date;
import com.cool.core.base.BaseEntity;
import lombok.Data;

/**
 * AI任务生成记录
 */
@Data
@Table("ai_task_generate_record")
public class AiTaskGenerateRecordEntity extends BaseEntity<AiTaskGenerateRecordEntity> {
    @Id(keyType = KeyType.Auto)
    private Long id;
    private Long userId;
    private String userName;
    private String taskDesc;
    @ColumnDefine(comment = "任务参数", type = "text")
    private String params; // JSON字符串
    @ColumnDefine(comment = "任务进度", type = "tinyint", defaultValue = "0")
    private Integer progress;
    @ColumnDefine(comment = "任务状态 0-排队中 1-生成中 2-已完成 3-失败", type = "tinyint", defaultValue = "0")
    private Integer status;
    @ColumnDefine(comment = "任务结果", type = "text")
    private String result; // JSON字符串
    @ColumnDefine(comment = "失败原因", type = "text")
    private String failReason;
    @ColumnDefine(comment = "耗时（毫秒）")
    private Integer costTime;
    @ColumnDefine(comment = "进度详情，存储JSON数组格式的日志", type = "text")
    private String progressDetails;
    private Boolean preview;
    @ColumnDefine(comment = "生成模式：preview-预览，generate-正式生成，accept_preview-接受预览", type = "varchar(20)", defaultValue = "'preview'")
    private String mode;
    @ColumnDefine(comment = "父记录ID，用于关联预览记录")
    private Long parentRecordId;

    private Date createTime;
    private Date updateTime;
    
    // Getters and Setters
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }
    public Long getUserId() { return userId; }
    public void setUserId(Long userId) { this.userId = userId; }
    public String getUserName() { return userName; }
    public void setUserName(String userName) { this.userName = userName; }
    public String getTaskDesc() { return taskDesc; }
    public void setTaskDesc(String taskDesc) { this.taskDesc = taskDesc; }
    public String getParams() { return params; }
    public void setParams(String params) { this.params = params; }
    public Integer getProgress() { return progress; }
    public void setProgress(Integer progress) { this.progress = progress; }
    public Integer getStatus() { return status; }
    public void setStatus(Integer status) { this.status = status; }
    public String getResult() { return result; }
    public void setResult(String result) { this.result = result; }
    public String getFailReason() { return failReason; }
    public void setFailReason(String failReason) { this.failReason = failReason; }
    public Integer getCostTime() { return costTime; }
    public void setCostTime(Integer costTime) { this.costTime = costTime; }
    public String getProgressDetails() { return progressDetails; }
    public void setProgressDetails(String progressDetails) { this.progressDetails = progressDetails; }
    public String getMode() { return mode; }
    public void setMode(String mode) { this.mode = mode; }
    public Long getParentRecordId() { return parentRecordId; }
    public void setParentRecordId(Long parentRecordId) { this.parentRecordId = parentRecordId; }
    public Date getCreateTime() { return createTime; }
    public void setCreateTime(Date createTime) { this.createTime = createTime; }
    public Date getUpdateTime() { return updateTime; }
    public void setUpdateTime(Date updateTime) { this.updateTime = updateTime; }
    // 兼容 setUpdatedTime 方法
    public void setUpdatedTime(Date updateTime) { this.setUpdateTime(updateTime); }
    public Boolean getPreview() { return preview; }
    public void setPreview(Boolean preview) { this.preview = preview; }
} 