package com.cool.modules.sop.dto.ai;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.util.List;

/**
 * AI多部门任务生成响应DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MultiDepartmentGenerateResponse {

    /**
     * 操作是否总体成功
     */
    private boolean success;

    /**
     * 顶层消息
     */
    private String message;

    /**
     * 总处理时间（毫秒）
     */
    private Long processingTime;

    /**
     * 涉及的部门总数
     */
    private int totalDepartments;

    /**
     * 生成的任务总数
     */
    private int totalTasksGenerated;

    /**
     * 每个部门的生成结果列表
     */
    private List<TaskGenerateResponse> results;

    /**
     * 总体摘要和建议
     */
    private List<String> summary;

} 