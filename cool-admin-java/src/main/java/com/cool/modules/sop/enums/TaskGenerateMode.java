package com.cool.modules.sop.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * 任务生成模式枚举
 */
@Getter
@RequiredArgsConstructor
public enum TaskGenerateMode {
    /**
     * 预览模式 - 只生成预览数据，不创建实际任务
     */
    PREVIEW("preview", "预览模式"),
    
    /**
     * 正式生成模式 - 创建实际的工单和任务
     */
    GENERATE("generate", "正式生成"),
    
    /**
     * 接受预览模式 - 基于已有预览结果创建任务
     */
    ACCEPT_PREVIEW("accept_preview", "接受预览");

    private final String code;
    private final String description;
} 