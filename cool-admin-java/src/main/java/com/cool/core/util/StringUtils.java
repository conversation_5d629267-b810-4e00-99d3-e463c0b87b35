package com.cool.core.util;

public class StringUtils {
    /**
     * 清理JSON响应，移除markdown代码块等格式
     */
    public static String cleanMarkdown(String response) {
        if (response == null) return null;
        response = cleanThinking(response);
        String cleaned = response.trim();

        // 移除markdown代码块
        if (cleaned.startsWith("```json")) {
            cleaned = cleaned.substring(7);
        } else if (cleaned.startsWith("```")) {
            cleaned = cleaned.substring(3);
        }

        if (cleaned.endsWith("```")) {
            cleaned = cleaned.substring(0, cleaned.length() - 3);
        }

        return cleaned.trim();
    }

    /**
     * 清理Dify思考过程, 去除所有 <think>...</think> 标签及其内容，并去除首尾空白
     * @param response 原始响应字符串
     * @return 清理后的字符串
     */
    public static String cleanThinking(String response) {
        if (response == null) return null;
        // 去除所有 <think>...</think> 标签及其内容
        String cleaned = response.replaceAll("<think>[\\s\\S]*?</think>", "");
        return cleaned.trim();
    }
}
