#!/bin/bash

# Cool Admin Java 后端服务启动脚本
# 支持多种启动方式

echo "=== Cool Admin Java 后端服务启动脚本 ==="

# 检查当前目录
echo "当前目录: $(pwd)"

# 方式1: 如果存在自定义的docker-compose配置
if [ -f "docker-compose-cool-backend.yml" ] && [ -f "cool-backend.env" ]; then
    echo "检测到自定义Docker配置，使用方式1启动..."
    docker-compose -f docker-compose-cool-backend.yml --env-file cool-backend.env up -d cool-backend
    if [ $? -eq 0 ]; then
        echo "✅ 后端服务启动成功 (方式1)"
        exit 0
    else
        echo "❌ 方式1启动失败，尝试其他方式..."
    fi
fi

# 方式2: 使用标准docker-compose启动数据库，然后启动Java应用
echo "使用方式2启动..."

# 启动数据库服务
echo "启动MySQL和Redis..."
docker-compose up -d mysql redis

# 等待数据库启动
echo "等待数据库启动..."
sleep 10

# 检查是否有已编译的jar包
JAR_FILE=$(find target -name "*.jar" -not -name "*sources.jar" 2>/dev/null | head -1)

if [ -n "$JAR_FILE" ] && [ -f "$JAR_FILE" ]; then
    echo "找到JAR文件: $JAR_FILE"
    echo "启动Java应用..."
    
    # 设置环境变量
    export SPRING_PROFILES_ACTIVE=local
    export SERVER_PORT=8001
    
    # 启动应用
    java -jar "$JAR_FILE" &
    JAVA_PID=$!
    
    echo "✅ Java应用已启动，PID: $JAVA_PID"
    echo "应用将在端口8001运行"
    echo "可以通过以下命令停止: kill $JAVA_PID"
    
    # 将PID保存到文件
    echo $JAVA_PID > cool-admin.pid
    
else
    echo "未找到JAR文件，尝试使用Maven启动..."
    
    # 方式3: 使用Maven启动
    if command -v mvn &> /dev/null; then
        echo "使用Maven启动开发服务器..."
        mvn spring-boot:run -Dspring-boot.run.profiles=local &
        MAVEN_PID=$!
        echo "✅ Maven开发服务器已启动，PID: $MAVEN_PID"
        echo $MAVEN_PID > cool-admin-maven.pid
    else
        echo "❌ 未找到Maven，请先编译项目:"
        echo "   mvn clean package -DskipTests"
        echo "   然后重新运行此脚本"
        exit 1
    fi
fi

# 等待服务启动
echo "等待服务启动..."
sleep 5

# 检查服务状态
echo "检查服务状态..."
if curl -s http://localhost:8001/admin/base/open/eps > /dev/null; then
    echo "✅ 后端服务启动成功，EPS接口可访问"
    echo "🌐 服务地址: http://localhost:8001"
    echo "📋 EPS接口: http://localhost:8001/admin/base/open/eps"
else
    echo "⚠️  服务可能还在启动中，请稍等片刻后检查"
    echo "🔍 可以通过以下命令检查日志:"
    echo "   docker-compose logs -f"
    echo "   或查看Java应用日志"
fi

echo "=== 启动完成 ===" 