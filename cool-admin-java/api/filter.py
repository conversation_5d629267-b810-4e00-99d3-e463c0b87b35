import json

# 文件路径
api_docs_path = "api-docs.json"
output_path = "api-docs.filtered.json"

# 需要过滤的前缀
exclude_prefixes = [
    "/admin/base/",
    "/admin/plugin/",
    "/admin/recycle/",
    "/admin/task/department/",
    "/admin/space/",
    "/admin/dict/",
    "/admin/sop/import/",
    "/admin/sop/s/o/p/industry/"
]

# 需要过滤的结尾
exclude_suffixes = [
    "add",
    "update",
    "delete",
    "disable",
    "enable",
    "health",
    "import",
    "export",
    "copy",
    "archive",
    "search",
    "sort",
    "order",
    "stats",
    "filter",
    "log"
]

# /admin/sop/s/ 只保留这些
sop_s_allowed = {"list", "page", "info"}

def should_exclude(path):
    # 1. 前缀过滤
    if any(path.startswith(prefix) for prefix in exclude_prefixes):
        return True
    # 2. /admin/sop/s/ 特殊规则
    if path.startswith("/admin/sop/s/"):
        last = path.rstrip('/').split('/')[-1]
        if last not in sop_s_allowed:
            return True
    # 3. 结尾过滤
    last = path.rstrip('/').split('/')[-1]
    if last in exclude_suffixes:
        return True
    return False

def main():
    with open(api_docs_path, "r", encoding="utf-8") as f:
        data = json.load(f)

    # 过滤 paths
    original_count = len(data.get("paths", {}))
    data["paths"] = {
        path: value
        for path, value in data.get("paths", {}).items()
        if not should_exclude(path)
    }
    filtered_count = len(data["paths"])

    # 保存结果
    with open(output_path, "w", encoding="utf-8") as f:
        json.dump(data, f, ensure_ascii=False, indent=2)

    print(f"过滤前接口数: {original_count}，过滤后接口数: {filtered_count}")
    print(f"已保存到: {output_path}")

if __name__ == "__main__":
    main()