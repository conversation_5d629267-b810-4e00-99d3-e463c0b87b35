{"openapi": "3.0.1", "info": {"title": "COOL-ADMIN", "description": "一个很酷的后台权限管理系统开发框架", "contact": {"name": "闪酷科技"}, "version": "4.0"}, "externalDocs": {"description": "参考文档", "url": "https://cool-js.com"}, "servers": [{"url": "http://localhost:18001", "description": "Generated server url"}], "security": [{"Authorization": []}], "tags": [{"name": "AI任务生成记录", "description": "AI任务生成记录"}, {"name": "字典信息", "description": "字典信息"}, {"name": "工作工单管理", "description": "工作工单的增删改查及状态管理"}, {"name": "任务执行管理", "description": "任务的执行、监控和管理"}, {"name": "用户查询管理", "description": "通用用户查询接口"}, {"name": "系统菜单", "description": "系统菜单"}, {"name": "任务状态管理", "description": "任务完成、关闭等状态管理"}, {"name": "任务执行管理", "description": "任务执行记录的管理和操作"}, {"name": "系统通用", "description": "系统通用"}, {"name": "SOP场景管理", "description": "SOP场景管理相关接口"}, {"name": "APIKEY管理", "description": "APIKEY授权管理接口"}, {"name": "SOP行业管理", "description": "SOP行业管理相关接口"}, {"name": "字典类型", "description": "字典类型"}, {"name": "系统开放", "description": "系统开放"}, {"name": "系统参数配置", "description": "系统参数配置"}, {"name": "系统用户", "description": "系统用户"}, {"name": "插件信息", "description": "插件信息"}, {"name": "系统日志", "description": "系统日志"}, {"name": "SOP导入管理", "description": "SOP场景和步骤的Excel导入功能"}, {"name": "AI任务生成器", "description": "AI智能任务生成相关接口"}, {"name": "任务管理", "description": "统一管理任务"}, {"name": "数据回收站", "description": "数据回收站"}, {"name": "系统部门", "description": "系统部门"}, {"name": "SOP步骤管理", "description": "SOP步骤管理相关接口"}, {"name": "任务部门权限管理", "description": "任务系统的部门权限控制"}, {"name": "系统角色", "description": "系统角色"}, {"name": "文件空间信息", "description": "文件空间信息"}, {"name": "任务分配管理", "description": "任务自动分配和手动分配管理"}, {"name": "用户信息", "description": "用户信息"}, {"name": "场景任务包管理", "description": "场景任务包管理"}], "paths": {"/admin/user/info/page": {"post": {"tags": ["用户信息"], "summary": "分页", "description": "分页查询多个信息", "operationId": "page", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RPageResultUserInfoEntity"}}}}}}}, "/admin/user/info/list": {"post": {"tags": ["用户信息"], "summary": "查询", "description": "查询多个信息", "operationId": "list", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RListUserInfoEntity"}}}}}}}, "/admin/task/status/task/reopen": {"post": {"tags": ["任务状态管理"], "summary": "重新开启任务", "operationId": "reopenTask", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TaskReopenRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}}}}, "/admin/task/status/task/force-complete": {"post": {"tags": ["任务状态管理"], "summary": "强制完成任务", "operationId": "forceCompleteTask", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TaskForceCompleteRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}}}}, "/admin/task/status/task/execution/complete": {"post": {"tags": ["任务状态管理"], "summary": "完成任务执行", "operationId": "completeTaskExecution", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TaskCompletionRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}}}}, "/admin/task/status/task/close": {"post": {"tags": ["任务状态管理"], "summary": "关闭任务", "operationId": "closeTask", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TaskCloseRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}}}}, "/admin/task/status/task/batch/reopen": {"post": {"tags": ["任务状态管理"], "summary": "批量重新开启任务", "operationId": "batchReopenTask", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BatchTaskOperationRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}}}}, "/admin/task/status/task/batch/force-complete": {"post": {"tags": ["任务状态管理"], "summary": "批量强制完成任务", "operationId": "batchForceCompleteTask", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BatchTaskOperationRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}}}}, "/admin/task/status/task/batch/close": {"post": {"tags": ["任务状态管理"], "summary": "批量关闭任务", "operationId": "batchCloseTask", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BatchTaskOperationRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}}}}, "/admin/task/package/updateStats/{packageId}": {"post": {"tags": ["场景任务包管理"], "summary": "更新任务包统计信息", "operationId": "updatePackageStats", "parameters": [{"name": "packageId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}}}}, "/admin/task/package/scenarioPackages": {"post": {"tags": ["场景任务包管理"], "summary": "获取场景任务包列表", "operationId": "scenarioPackages", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "additionalProperties": {"type": "object"}}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}}}}, "/admin/task/package/page": {"post": {"tags": ["场景任务包管理"], "summary": "分页", "description": "分页查询多个信息", "operationId": "page_1", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RPageResultTaskPackageEntity"}}}}}}}, "/admin/task/package/list": {"post": {"tags": ["场景任务包管理"], "summary": "查询", "description": "查询多个信息", "operationId": "list_1", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RListTaskPackageEntity"}}}}}}}, "/admin/task/package/complete/{packageId}": {"post": {"tags": ["场景任务包管理"], "summary": "完成任务包", "operationId": "completePackage", "parameters": [{"name": "packageId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}}}}, "/admin/task/package/close/{packageId}": {"post": {"tags": ["场景任务包管理"], "summary": "关闭任务包", "operationId": "closePackage", "parameters": [{"name": "packageId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}}}}, "/admin/task/info/stop": {"post": {"tags": ["任务管理"], "summary": "停止任务", "operationId": "stop", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}}}}, "/admin/task/info/start": {"post": {"tags": ["任务管理"], "summary": "开始任务", "operationId": "start", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}}}}, "/admin/task/info/start-task": {"post": {"tags": ["任务管理"], "summary": "开始任务", "description": "将待执行任务转换为执行中状态", "operationId": "startTask", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}}}}, "/admin/task/info/page": {"post": {"tags": ["任务管理"], "summary": "分页", "description": "分页查询多个信息", "operationId": "page_2", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RPageResultTaskInfoEntity"}}}}}}}, "/admin/task/info/once": {"post": {"tags": ["任务管理"], "summary": "执行一次", "operationId": "once", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}}}}, "/admin/task/info/batch-update": {"post": {"tags": ["任务管理"], "summary": "批量更新任务时间", "description": "批量更新多个任务的计划开始和结束时间", "operationId": "batchUpdateTaskTime", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "additionalProperties": {"type": "object"}}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}}}}, "/admin/task/info/batch-execution-details": {"post": {"tags": ["任务管理"], "summary": "批量获取任务执行详情", "description": "批量获取多个任务的执行时间和执行人信息", "operationId": "getBatchTaskExecutionDetails", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "additionalProperties": {"type": "object"}}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}}}}, "/admin/task/execution/updateStatus": {"post": {"tags": ["任务执行管理"], "summary": "更新执行状态", "operationId": "updateExecutionStatus", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "additionalProperties": {"type": "object"}}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RBoolean"}}}}}}}, "/admin/task/execution/page": {"post": {"tags": ["任务执行管理"], "summary": "分页", "description": "分页查询多个信息", "operationId": "page_3", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RPageResultTaskExecutionEntity"}}}}}}}, "/admin/task/execution/list": {"post": {"tags": ["任务执行管理"], "summary": "查询", "description": "查询多个信息", "operationId": "list_2", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RListTaskExecutionEntity"}}}}}}}, "/admin/task/execution/complete": {"post": {"tags": ["任务执行管理"], "summary": "完成任务执行", "operationId": "completeExecution", "parameters": [{"name": "taskId", "in": "query", "description": "任务ID", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "assigneeId", "in": "query", "description": "执行人ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RBoolean"}}}}}}}, "/admin/task/execution/cancel": {"post": {"tags": ["任务执行管理"], "summary": "取消任务分配", "operationId": "cancelAssignment", "parameters": [{"name": "taskId", "in": "query", "description": "任务ID", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "assigneeId", "in": "query", "description": "执行人ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RBoolean"}}}}}}}, "/admin/task/execution/batchUpdateStatus": {"post": {"tags": ["任务执行管理"], "summary": "批量更新执行状态", "operationId": "batchUpdateStatus", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "additionalProperties": {"type": "object"}}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RBoolean"}}}}}}}, "/admin/task/assignment/validate/{taskId}": {"post": {"tags": ["任务分配管理"], "summary": "验证分配结果", "operationId": "validateAssignment", "parameters": [{"name": "taskId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "array", "items": {"type": "integer", "format": "int64"}}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RBoolean"}}}}}}}, "/admin/task/assignment/single/{taskId}": {"post": {"tags": ["任务分配管理"], "summary": "为单个任务分配执行人", "operationId": "assignSingleTask", "parameters": [{"name": "taskId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "autoAssign", "in": "query", "required": false, "schema": {"type": "boolean", "default": true}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RAssignmentResult"}}}}}}}, "/admin/task/assignment/package/{packageId}": {"post": {"tags": ["任务分配管理"], "summary": "为任务包批量分配执行人", "operationId": "assignTaskPackage", "parameters": [{"name": "packageId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "autoAssign", "in": "query", "required": false, "schema": {"type": "boolean", "default": true}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RAssignmentResult"}}}}}}}, "/admin/task/assignment/manual": {"post": {"tags": ["任务分配管理"], "summary": "手动分配任务", "operationId": "manualAssignment", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ManualAssignmentRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RString"}}}}}}}, "/admin/task/assignment/execute": {"post": {"tags": ["任务分配管理"], "summary": "执行自动分配", "operationId": "executeAssignment", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AssignmentRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RAssignmentResult"}}}}}}}, "/admin/task/assignment/candidates/by-roles": {"post": {"tags": ["任务分配管理"], "summary": "根据角色筛选候选人", "operationId": "getCandidatesByRoles", "requestBody": {"content": {"application/json": {"schema": {"type": "array", "items": {"type": "string"}}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RListCandidateProfile"}}}}}}}, "/admin/sop/work/order/start": {"post": {"tags": ["工作工单管理"], "summary": "开始执行工单", "operationId": "start_1", "parameters": [{"name": "id", "in": "query", "description": "工单ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}}}}, "/admin/sop/work/order/resume": {"post": {"tags": ["工作工单管理"], "summary": "恢复工单", "operationId": "resume", "parameters": [{"name": "id", "in": "query", "description": "工单ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}}}}, "/admin/sop/work/order/pause": {"post": {"tags": ["工作工单管理"], "summary": "暂停工单", "operationId": "pause", "parameters": [{"name": "id", "in": "query", "description": "工单ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}}}}, "/admin/sop/work/order/page": {"post": {"tags": ["工作工单管理"], "summary": "分页", "description": "分页查询多个信息", "operationId": "page_6", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RPageResultWorkOrderEntity"}}}}}}}, "/admin/sop/work/order/list": {"post": {"tags": ["工作工单管理"], "summary": "查询", "description": "查询多个信息", "operationId": "list_5", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RListWorkOrderEntity"}}}}}}}, "/admin/sop/work/order/create-from-template": {"post": {"tags": ["工作工单管理"], "summary": "基于SOP模板创建工单", "operationId": "createFromTemplate", "parameters": [{"name": "templateId", "in": "query", "description": "SOP模板ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/WorkOrderEntity"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}}}}, "/admin/sop/work/order/complete": {"post": {"tags": ["工作工单管理"], "summary": "完成工单", "operationId": "complete", "parameters": [{"name": "id", "in": "query", "description": "工单ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}}}}, "/admin/sop/work/order/cancel": {"post": {"tags": ["工作工单管理"], "summary": "取消工单", "operationId": "cancel", "parameters": [{"name": "id", "in": "query", "description": "工单ID", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "reason", "in": "query", "description": "取消原因", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}}}}, "/admin/sop/work/order/batch-assign": {"post": {"tags": ["工作工单管理"], "summary": "批量分配工单", "operationId": "batchAssign", "parameters": [{"name": "assigneeId", "in": "query", "description": "负责人ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "array", "description": "工单ID列表", "items": {"type": "integer", "format": "int64"}}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}}}}, "/admin/sop/work/order/ai-schedule": {"post": {"tags": ["工作工单管理"], "summary": "AI智能调度工单", "operationId": "aiSchedule", "requestBody": {"content": {"application/json": {"schema": {"type": "array", "description": "工单ID列表", "items": {"type": "integer", "format": "int64"}}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}}}}, "/admin/sop/task/start": {"post": {"tags": ["任务执行管理"], "summary": "开始执行任务", "operationId": "start_2", "parameters": [{"name": "id", "in": "query", "description": "任务ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}}}}, "/admin/sop/task/resume": {"post": {"tags": ["任务执行管理"], "summary": "恢复任务", "operationId": "resume_1", "parameters": [{"name": "id", "in": "query", "description": "任务ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}}}}, "/admin/sop/task/quality-check": {"post": {"tags": ["任务执行管理"], "summary": "任务质量检查", "operationId": "qualityCheck", "parameters": [{"name": "taskId", "in": "query", "description": "任务ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "additionalProperties": {"type": "object"}}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}}}}, "/admin/sop/task/pause": {"post": {"tags": ["任务执行管理"], "summary": "暂停任务", "operationId": "pause_1", "parameters": [{"name": "id", "in": "query", "description": "任务ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}}}}, "/admin/sop/task/page": {"post": {"tags": ["任务执行管理"], "summary": "分页", "description": "分页查询多个信息", "operationId": "page_7", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RPageResultTaskExecuteRecordEntity"}}}}}}}, "/admin/sop/task/list": {"post": {"tags": ["任务执行管理"], "summary": "查询", "description": "查询多个信息", "operationId": "list_6", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RListTaskExecuteRecordEntity"}}}}}}}, "/admin/sop/task/complete": {"post": {"tags": ["任务执行管理"], "summary": "完成任务", "operationId": "complete_1", "parameters": [{"name": "id", "in": "query", "description": "任务ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}}}}, "/admin/sop/task/ai-suggest-scenarios": {"post": {"tags": ["任务执行管理"], "summary": "获取AI生成任务的建议场景", "description": "基于用户输入获取推荐的SOP场景", "operationId": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "parameters": [{"name": "description", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}}}}, "/admin/sop/task/ai-quality-check/{taskId}": {"post": {"tags": ["任务执行管理"], "summary": "AI质量智能检查", "operationId": "aiQualityCheck", "parameters": [{"name": "taskId", "in": "path", "description": "任务ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}}}}, "/admin/sop/task/ai-guidance": {"post": {"tags": ["任务执行管理"], "summary": "获取AI执行指导", "operationId": "getAIGuidance", "parameters": [{"name": "taskId", "in": "query", "description": "任务ID", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "currentSituation", "in": "query", "description": "当前情况描述", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}}}}, "/admin/sop/s/o/p/step/page": {"post": {"tags": ["SOP步骤管理"], "summary": "分页", "description": "分页查询多个信息", "operationId": "page_8", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RPageResultSOPStepEntity"}}}}}}}, "/admin/sop/s/o/p/step/list": {"post": {"tags": ["SOP步骤管理"], "summary": "查询", "description": "查询多个信息", "operationId": "list_7", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RListSOPStepEntity"}}}}}}}, "/admin/sop/s/o/p/scenario/page": {"post": {"tags": ["SOP场景管理"], "summary": "分页", "description": "分页查询多个信息", "operationId": "page_9", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RPageResultSOPScenarioEntity"}}}}}}}, "/admin/sop/s/o/p/scenario/list": {"post": {"tags": ["SOP场景管理"], "summary": "查询", "description": "查询多个信息", "operationId": "list_8", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RListSOPScenarioEntity"}}}}}}}, "/admin/sop/ai/task/generate/record/page": {"post": {"tags": ["AI任务生成记录"], "summary": "分页", "description": "分页查询多个信息", "operationId": "page_11", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RPageResultAiTaskGenerateRecordEntity"}}}}}}}, "/admin/sop/ai/task/generate/record/list": {"post": {"tags": ["AI任务生成记录"], "summary": "查询", "description": "查询多个信息", "operationId": "list_10", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RListAiTaskGenerateRecordEntity"}}}}}}}, "/admin/sop/ai-task-generator/suggest-scenarios": {"post": {"tags": ["AI任务生成器"], "summary": "获取场景建议", "description": "根据用户输入获取AI推荐的场景列表", "operationId": "suggestScenarios_1", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "additionalProperties": {"type": "string"}}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RListMapStringObject"}}}}}}}, "/admin/sop/ai-task-generator/smart-suggest-scenarios": {"post": {"tags": ["AI任务生成器"], "summary": "获取智能场景建议", "description": "根据用户输入从系统场景库中智能推荐相关场景", "operationId": "smartSuggestScenarios", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "additionalProperties": {"type": "string"}}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RListMapStringObject"}}}}}}}, "/admin/sop/ai-task-generator/quick-generate": {"post": {"tags": ["AI任务生成器"], "summary": "快速任务生成", "description": "基于简单描述快速生成任务（返回任务记录ID）", "operationId": "quickGenerate", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "additionalProperties": {"type": "string"}}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RMapStringLong"}}}}}}}, "/admin/sop/ai-task-generator/preview": {"post": {"tags": ["AI任务生成器"], "summary": "AI任务预览", "description": "根据用户自然语言描述，为多个部门生成任务预览（返回任务记录ID，通过SSE获取结果）", "operationId": "previewTasks", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TaskGenerateRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RMapStringLong"}}}}}}}, "/admin/sop/ai-task-generator/generate": {"post": {"tags": ["AI任务生成器"], "summary": "AI智能生成任务", "description": "根据用户自然语言描述，为多个部门使用AI识别场景并生成对应任务（返回任务记录ID，通过SSE获取结果）", "operationId": "generateTasks_1", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TaskGenerateRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RMapStringLong"}}}}}}}, "/admin/sop/ai-task-generator/generate-sync": {"post": {"tags": ["AI任务生成器"], "summary": "AI智能生成任务（同步，已过时）", "description": "同步方式生成任务，建议使用异步接口", "operationId": "generateTasksSync", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TaskGenerateRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RMultiDepartmentGenerateResponse"}}}}}, "deprecated": true}}, "/admin/sop/ai-task-generator/ai-generate-scenario-content": {"post": {"tags": ["AI任务生成器"], "summary": "AI智能生成场景内容", "description": "基于场景标签通过AI生成丰富的任务描述", "operationId": "generateScenarioContent", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "additionalProperties": {"type": "object"}}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RMapStringObject"}}}}}}}, "/admin/sop/ai-task-generator/adjust-preview-assignment": {"post": {"tags": ["AI任务生成器"], "summary": "调整预览任务的执行人分配", "description": "在预览阶段调整特定任务的执行人分配", "operationId": "adjustPreviewAssignment", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "additionalProperties": {"type": "object"}}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RMapStringObject"}}}}}}}, "/admin/sop/ai-task-generator/accept-preview/{previewRecordId}": {"post": {"tags": ["AI任务生成器"], "summary": "接受预览结果生成任务", "description": "基于已有预览结果生成正式任务（返回新的任务记录ID，通过SSE获取结果）", "operationId": "acceptPreviewAndGenerate", "parameters": [{"name": "previewRecordId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RMapStringLong"}}}}}}}, "/admin/user/info/info": {"get": {"tags": ["用户信息"], "summary": "信息", "description": "根据ID查询单个信息", "operationId": "info", "parameters": [{"name": "id", "in": "query", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RUserInfoEntity"}}}}}}}, "/admin/task/status/task/canComplete": {"get": {"tags": ["任务状态管理"], "summary": "检查任务是否可以完成", "operationId": "canCompleteTask", "parameters": [{"name": "taskId", "in": "query", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "assigneeId", "in": "query", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}}}}, "/admin/task/status/task/canClose": {"get": {"tags": ["任务状态管理"], "summary": "检查任务是否可以关闭", "operationId": "canCloseTask", "parameters": [{"name": "taskId", "in": "query", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "operatorId", "in": "query", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}}}}, "/admin/task/package/info": {"get": {"tags": ["场景任务包管理"], "summary": "信息", "description": "根据ID查询单个信息", "operationId": "info_1", "parameters": [{"name": "id", "in": "query", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RTaskPackageEntity"}}}}}}}, "/admin/task/package/detail/{packageId}": {"get": {"tags": ["场景任务包管理"], "summary": "获取任务包详情", "operationId": "getPackageDetail", "parameters": [{"name": "packageId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}}}}, "/admin/task/package/byScenario/{scenarioId}": {"get": {"tags": ["场景任务包管理"], "summary": "根据场景ID获取任务包列表", "operationId": "getPackagesByScenarioId", "parameters": [{"name": "scenarioId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}}}}, "/admin/task/info/personal-tasks": {"get": {"tags": ["任务管理"], "summary": "个人工作台任务列表", "description": "获取指定用户的个人任务，包含执行时间和执行人信息", "operationId": "getPersonalTasks", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}}}}, "/admin/task/info/info": {"get": {"tags": ["任务管理"], "summary": "信息", "description": "根据ID查询单个信息", "operationId": "info_2", "parameters": [{"name": "id", "in": "query", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RTaskInfoEntity"}}}}}}}, "/admin/task/info/execution-details/{taskId}": {"get": {"tags": ["任务管理"], "summary": "获取任务执行详情", "description": "获取任务的执行时间和执行人信息", "operationId": "getTaskExecutionDetails", "parameters": [{"name": "taskId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}}}}, "/admin/task/execution/personal-tasks/{assigneeId}": {"get": {"tags": ["任务执行管理"], "summary": "获取个人工作台任务列表", "description": "查询指定用户的所有任务及执行状态", "operationId": "getPersonalTasks_1", "parameters": [{"name": "assigneeId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RListMapStringObject"}}}}}}}, "/admin/task/execution/info": {"get": {"tags": ["任务执行管理"], "summary": "信息", "description": "根据ID查询单个信息", "operationId": "info_3", "parameters": [{"name": "id", "in": "query", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RTaskExecutionEntity"}}}}}}}, "/admin/task/execution/checkAssigned/{taskId}": {"get": {"tags": ["任务执行管理"], "summary": "检查任务是否已分配", "operationId": "isTaskAssigned", "parameters": [{"name": "taskId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RBoolean"}}}}}}}, "/admin/task/execution/byTask/{taskId}": {"get": {"tags": ["任务执行管理"], "summary": "根据任务ID获取执行记录", "operationId": "getByTaskId", "parameters": [{"name": "taskId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RListMapStringObject"}}}}}}}, "/admin/task/execution/byAssignee/{assigneeId}": {"get": {"tags": ["任务执行管理"], "summary": "根据执行人ID获取执行记录", "operationId": "getByAssigneeId", "parameters": [{"name": "assigneeId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RListTaskExecutionEntity"}}}}}}}, "/admin/task/assignment/candidates": {"get": {"tags": ["任务分配管理"], "summary": "获取所有可用的候选人", "operationId": "getAllCandidates", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RListCandidateProfile"}}}}}}}, "/admin/task/assignment/candidates/{taskId}": {"get": {"tags": ["任务分配管理"], "summary": "获取任务的候选人列表", "operationId": "getCandidatesForTask", "parameters": [{"name": "taskId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RListCandidateProfile"}}}}}}}, "/admin/sop/work/order/progress/{id}": {"get": {"tags": ["工作工单管理"], "summary": "获取工单执行进度", "operationId": "getProgress", "parameters": [{"name": "id", "in": "path", "description": "工单ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}}}}, "/admin/sop/work/order/info": {"get": {"tags": ["工作工单管理"], "summary": "信息", "description": "根据ID查询单个信息", "operationId": "info_6", "parameters": [{"name": "id", "in": "query", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RWorkOrderEntity"}}}}}}}, "/admin/sop/work/order/detail/{id}": {"get": {"tags": ["工作工单管理"], "summary": "获取工单详情及任务列表", "operationId": "getDetail", "parameters": [{"name": "id", "in": "path", "description": "工单ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}}}}, "/admin/sop/work/order/ai-optimization/{id}": {"get": {"tags": ["工作工单管理"], "summary": "获取AI优化建议", "operationId": "getAIOptimization", "parameters": [{"name": "id", "in": "path", "description": "工单ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}}}}, "/admin/sop/task/logs/{taskId}": {"get": {"tags": ["任务执行管理"], "summary": "获取任务执行日志", "operationId": "getExecutionLogs", "parameters": [{"name": "taskId", "in": "path", "description": "任务ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}}}}, "/admin/sop/task/info": {"get": {"tags": ["任务执行管理"], "summary": "信息", "description": "根据ID查询单个信息", "operationId": "info_7", "parameters": [{"name": "id", "in": "query", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RTaskExecuteRecordEntity"}}}}}}}, "/admin/sop/task/detail/{taskId}": {"get": {"tags": ["任务执行管理"], "summary": "获取任务详情", "operationId": "getTaskDetail", "parameters": [{"name": "taskId", "in": "path", "description": "任务ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}}}}, "/admin/sop/s/o/p/step/info": {"get": {"tags": ["SOP步骤管理"], "summary": "信息", "description": "根据ID查询单个信息", "operationId": "info_8", "parameters": [{"name": "id", "in": "query", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RSOPStepEntity"}}}}}}}, "/admin/sop/s/o/p/scenario/info": {"get": {"tags": ["SOP场景管理"], "summary": "信息", "description": "根据ID查询单个信息", "operationId": "info_9", "parameters": [{"name": "id", "in": "query", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RSOPScenarioEntity"}}}}}}}, "/admin/sop/ai/task/generate/record/info": {"get": {"tags": ["AI任务生成记录"], "summary": "信息", "description": "根据ID查询单个信息", "operationId": "info_11", "parameters": [{"name": "id", "in": "query", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RAiTaskGenerateRecordEntity"}}}}}}}, "/admin/sop/ai-task-generator/subscribe/{recordId}": {"get": {"tags": ["AI任务生成器"], "summary": "SSE订阅任务进度", "description": "通过SSE实时获取任务生成进度和结果", "operationId": "subscribeProgress", "parameters": [{"name": "recordId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "Authorization", "in": "query", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/SseEmitter"}}}}}}}, "/admin/sop/ai-task-generator/scenarios": {"get": {"tags": ["AI任务生成器"], "summary": "获取可用场景列表", "description": "获取系统中所有可用的SOP场景", "operationId": "getAvailableScenarios", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RString"}}}}}}}, "/admin/sop/ai-task-generator/scenario-tags": {"get": {"tags": ["AI任务生成器"], "summary": "获取场景标签", "description": "获取系统中所有可用的场景标签列表", "operationId": "getScenarioTags", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RListMapStringObject"}}}}}}}, "/admin/sop/ai-task-generator/record/{recordId}": {"get": {"tags": ["AI任务生成器"], "summary": "获取任务记录详情", "description": "获取指定任务记录的详细信息", "operationId": "getTaskRecord", "parameters": [{"name": "recordId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RObject"}}}}}}}, "/admin/sop/ai-task-generator/available-assignees": {"get": {"tags": ["AI任务生成器"], "summary": "获取可用的执行人列表", "operationId": "getAvailableAssignees", "parameters": [{"name": "departmentIds", "in": "query", "required": false, "schema": {"type": "array", "items": {"type": "integer", "format": "int64"}}}, {"name": "roleIds", "in": "query", "required": false, "schema": {"type": "array", "items": {"type": "integer", "format": "int64"}}}, {"name": "roleNames", "in": "query", "required": false, "schema": {"type": "array", "items": {"type": "string"}}}, {"name": "name", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "phone", "in": "query", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}}}}}, "components": {"schemas": {"UserInfoEntity": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "createTime": {"type": "string", "format": "date-time"}, "updateTime": {"type": "string", "format": "date-time"}, "unionid": {"type": "string"}, "avatarUrl": {"type": "string"}, "nickName": {"type": "string"}, "phone": {"type": "string"}, "gender": {"type": "integer", "format": "int32"}, "status": {"type": "integer", "format": "int32"}, "loginType": {"type": "string"}, "password": {"type": "string"}}}, "R": {"title": "响应数据结构", "type": "object", "properties": {"code": {"title": "编码：1000表示成功，其他值表示失败", "type": "integer", "format": "int32"}, "message": {"title": "消息内容", "type": "string"}, "data": {"title": "响应数据", "type": "object"}}}, "PageResultUserInfoEntity": {"title": "分页数据模型", "type": "object", "properties": {"list": {"title": "分页数据", "type": "array", "items": {"$ref": "#/components/schemas/UserInfoEntity"}}, "pagination": {"$ref": "#/components/schemas/Pagination"}}}, "Pagination": {"type": "object", "properties": {"page": {"title": "页码", "type": "integer", "format": "int64"}, "size": {"title": "本页数量", "type": "integer", "format": "int64"}, "total": {"title": "总页数", "type": "integer", "format": "int64"}}}, "RPageResultUserInfoEntity": {"title": "响应数据结构", "type": "object", "properties": {"code": {"title": "编码：1000表示成功，其他值表示失败", "type": "integer", "format": "int32"}, "message": {"title": "消息内容", "type": "string"}, "data": {"$ref": "#/components/schemas/PageResultUserInfoEntity"}}}, "RListUserInfoEntity": {"title": "响应数据结构", "type": "object", "properties": {"code": {"title": "编码：1000表示成功，其他值表示失败", "type": "integer", "format": "int32"}, "message": {"title": "消息内容", "type": "string"}, "data": {"title": "响应数据", "type": "array", "items": {"$ref": "#/components/schemas/UserInfoEntity"}}}}, "TaskReopenRequest": {"type": "object", "properties": {"taskId": {"type": "integer", "format": "int64"}, "reason": {"type": "string"}, "operatorId": {"type": "integer", "format": "int64"}}}, "TaskForceCompleteRequest": {"type": "object", "properties": {"taskId": {"type": "integer", "format": "int64"}, "reason": {"type": "string"}, "operatorId": {"type": "integer", "format": "int64"}}}, "TaskCompletionRequest": {"required": ["assigneeId", "taskId"], "type": "object", "properties": {"taskId": {"type": "integer", "description": "任务ID", "format": "int64"}, "assigneeId": {"type": "integer", "description": "执行人ID", "format": "int64"}, "completionNote": {"type": "string", "description": "完成说明"}, "attachments": {"type": "array", "description": "附件列表", "items": {"type": "string", "description": "附件列表"}}, "photos": {"type": "array", "description": "照片列表", "items": {"type": "string", "description": "照片列表"}}}, "description": "任务完成请求"}, "TaskCloseRequest": {"required": ["closeReason", "operatorId", "taskId"], "type": "object", "properties": {"taskId": {"type": "integer", "description": "任务ID", "format": "int64"}, "closeReason": {"type": "string", "description": "关闭原因"}, "operatorId": {"type": "integer", "description": "操作人ID", "format": "int64"}, "operatorName": {"type": "string", "description": "操作人姓名"}}, "description": "任务关闭请求"}, "BatchTaskOperationRequest": {"type": "object", "properties": {"taskIds": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "reason": {"type": "string"}, "operatorId": {"type": "integer", "format": "int64"}, "operationType": {"type": "string"}}}, "TaskPackageEntity": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "createTime": {"type": "string", "format": "date-time"}, "updateTime": {"type": "string", "format": "date-time"}, "packageName": {"type": "string"}, "description": {"type": "string"}, "scenarioId": {"type": "integer", "format": "int64"}, "scenarioName": {"type": "string"}, "scenarioCode": {"type": "string"}, "sopScenarioId": {"type": "integer", "format": "int64"}, "workOrderId": {"type": "integer", "format": "int64"}, "packageStatus": {"type": "integer", "format": "int32"}, "packageType": {"type": "integer", "format": "int32"}, "totalTasks": {"type": "integer", "format": "int32"}, "completedTasks": {"type": "integer", "format": "int32"}, "inProgressTasks": {"type": "integer", "format": "int32"}, "pendingTasks": {"type": "integer", "format": "int32"}, "completionRate": {"type": "integer", "format": "int32"}, "expectedStartTime": {"type": "string", "format": "date-time"}, "expectedEndTime": {"type": "string", "format": "date-time"}, "actualStartTime": {"type": "string", "format": "date-time"}, "actualEndTime": {"type": "string", "format": "date-time"}, "creatorId": {"type": "integer", "format": "int64"}, "creatorName": {"type": "string"}, "ownerId": {"type": "integer", "format": "int64"}, "ownerName": {"type": "string"}, "priority": {"type": "integer", "format": "int32"}, "tags": {"type": "string"}, "remarks": {"type": "string"}, "isDeleted": {"type": "integer", "format": "int32"}, "departmentId": {"type": "integer", "format": "int64"}, "departmentName": {"type": "string"}, "creatorDepartmentId": {"type": "integer", "format": "int64"}, "creatorDepartmentName": {"type": "string"}, "isCrossDepartment": {"type": "boolean"}}}, "PageResultTaskPackageEntity": {"title": "分页数据模型", "type": "object", "properties": {"list": {"title": "分页数据", "type": "array", "items": {"$ref": "#/components/schemas/TaskPackageEntity"}}, "pagination": {"$ref": "#/components/schemas/Pagination"}}}, "RPageResultTaskPackageEntity": {"title": "响应数据结构", "type": "object", "properties": {"code": {"title": "编码：1000表示成功，其他值表示失败", "type": "integer", "format": "int32"}, "message": {"title": "消息内容", "type": "string"}, "data": {"$ref": "#/components/schemas/PageResultTaskPackageEntity"}}}, "RListTaskPackageEntity": {"title": "响应数据结构", "type": "object", "properties": {"code": {"title": "编码：1000表示成功，其他值表示失败", "type": "integer", "format": "int32"}, "message": {"title": "消息内容", "type": "string"}, "data": {"title": "响应数据", "type": "array", "items": {"$ref": "#/components/schemas/TaskPackageEntity"}}}}, "TaskExecutionEntity": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "createTime": {"type": "string", "format": "date-time"}, "updateTime": {"type": "string", "format": "date-time"}, "taskId": {"type": "integer", "format": "int64"}, "assigneeId": {"type": "integer", "format": "int64"}, "assigneeName": {"type": "string"}, "executionStatus": {"type": "string"}, "acceptTime": {"type": "string", "format": "date-time"}, "completionTime": {"type": "string", "format": "date-time"}, "completionNote": {"type": "string"}, "attachments": {"type": "string"}, "photos": {"type": "string"}, "remark": {"type": "string"}, "assignmentType": {"type": "string"}, "confidence": {"type": "integer", "format": "int32"}, "assignmentReasons": {"type": "string"}, "assignedBy": {"type": "integer", "format": "int64"}, "departmentId": {"type": "integer", "format": "int64"}, "departmentName": {"type": "string"}, "assigneeDepartmentId": {"type": "integer", "format": "int64"}, "assigneeDepartmentName": {"type": "string"}, "assignmentTypeEnum": {"type": "string", "enum": ["MANUAL", "AUTO", "AI", "BATCH"]}, "executionStatusEnum": {"type": "string", "enum": ["ASSIGNED", "ACCEPTED", "IN_PROGRESS", "COMPLETED", "REJECTED", "CANCELLED"]}, "terminalStatus": {"type": "boolean"}, "completedStatus": {"type": "boolean"}, "activeStatus": {"type": "boolean"}, "autoAssignment": {"type": "boolean"}, "manualAssignment": {"type": "boolean"}, "executionStatusName": {"type": "string"}, "assignmentTypeName": {"type": "string"}}}, "TaskInfoEntity": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "createTime": {"type": "string", "format": "date-time"}, "updateTime": {"type": "string", "format": "date-time"}, "name": {"type": "string"}, "description": {"type": "string"}, "priority": {"type": "integer", "format": "int32"}, "taskStatus": {"type": "integer", "format": "int32"}, "taskCategory": {"type": "string"}, "completionTime": {"type": "string", "format": "date-time"}, "closeReason": {"type": "string"}, "closedBy": {"type": "string"}, "closeTime": {"type": "string", "format": "date-time"}, "scenarioId": {"type": "integer", "format": "int64"}, "packageId": {"type": "integer", "format": "int64"}, "scenarioCode": {"type": "string"}, "scenarioName": {"type": "string"}, "stepId": {"type": "integer", "format": "int64"}, "stepCode": {"type": "string"}, "stepName": {"type": "string"}, "entityTouchpoint": {"type": "string"}, "taskActivity": {"type": "string"}, "employeeBehavior": {"type": "string"}, "workHighlight": {"type": "string"}, "employeeRole": {"type": "string"}, "photoRequired": {"type": "boolean"}, "attachmentRequired": {"type": "boolean"}, "remark": {"type": "string"}, "jobId": {"type": "string"}, "repeatCount": {"type": "integer", "format": "int32"}, "every": {"type": "integer", "format": "int32"}, "scheduleStatus": {"type": "integer", "format": "int32"}, "service": {"type": "string"}, "scheduleType": {"type": "integer", "format": "int32"}, "type": {"type": "integer", "format": "int32"}, "data": {"type": "string"}, "cron": {"type": "string"}, "nextRunTime": {"type": "string", "format": "date-time"}, "startTime": {"type": "string", "format": "date-time"}, "endTime": {"type": "string", "format": "date-time"}, "assigneeName": {"type": "string"}, "assigneeId": {"type": "integer", "format": "int64"}, "executions": {"type": "array", "items": {"$ref": "#/components/schemas/TaskExecutionEntity"}}, "executionEntitys": {"type": "array", "items": {"$ref": "#/components/schemas/TaskExecutionEntity"}}, "departmentId": {"type": "integer", "format": "int64"}, "departmentName": {"type": "string"}, "creatorDepartmentId": {"type": "integer", "format": "int64"}, "creatorDepartmentName": {"type": "string"}}}, "PageResultTaskInfoEntity": {"title": "分页数据模型", "type": "object", "properties": {"list": {"title": "分页数据", "type": "array", "items": {"$ref": "#/components/schemas/TaskInfoEntity"}}, "pagination": {"$ref": "#/components/schemas/Pagination"}}}, "RPageResultTaskInfoEntity": {"title": "响应数据结构", "type": "object", "properties": {"code": {"title": "编码：1000表示成功，其他值表示失败", "type": "integer", "format": "int32"}, "message": {"title": "消息内容", "type": "string"}, "data": {"$ref": "#/components/schemas/PageResultTaskInfoEntity"}}}, "RBoolean": {"title": "响应数据结构", "type": "object", "properties": {"code": {"title": "编码：1000表示成功，其他值表示失败", "type": "integer", "format": "int32"}, "message": {"title": "消息内容", "type": "string"}, "data": {"title": "响应数据", "type": "boolean"}}}, "PageResultTaskExecutionEntity": {"title": "分页数据模型", "type": "object", "properties": {"list": {"title": "分页数据", "type": "array", "items": {"$ref": "#/components/schemas/TaskExecutionEntity"}}, "pagination": {"$ref": "#/components/schemas/Pagination"}}}, "RPageResultTaskExecutionEntity": {"title": "响应数据结构", "type": "object", "properties": {"code": {"title": "编码：1000表示成功，其他值表示失败", "type": "integer", "format": "int32"}, "message": {"title": "消息内容", "type": "string"}, "data": {"$ref": "#/components/schemas/PageResultTaskExecutionEntity"}}}, "RListTaskExecutionEntity": {"title": "响应数据结构", "type": "object", "properties": {"code": {"title": "编码：1000表示成功，其他值表示失败", "type": "integer", "format": "int32"}, "message": {"title": "消息内容", "type": "string"}, "data": {"title": "响应数据", "type": "array", "items": {"$ref": "#/components/schemas/TaskExecutionEntity"}}}}, "RMapLongBoolean": {"title": "响应数据结构", "type": "object", "properties": {"code": {"title": "编码：1000表示成功，其他值表示失败", "type": "integer", "format": "int32"}, "message": {"title": "消息内容", "type": "string"}, "data": {"title": "响应数据", "type": "object", "additionalProperties": {"title": "响应数据", "type": "boolean"}}}}, "Assignee": {"type": "object", "properties": {"userId": {"type": "integer", "format": "int64"}, "userName": {"type": "string"}, "userRole": {"type": "string"}, "taskRole": {"type": "string"}, "confidence": {"type": "integer", "format": "int32"}, "currentWorkload": {"type": "integer", "format": "int32"}, "skillMatch": {"type": "integer", "format": "int32"}}}, "AssignmentResult": {"title": "响应数据", "type": "object", "properties": {"success": {"type": "boolean"}, "assignments": {"type": "array", "items": {"$ref": "#/components/schemas/TaskAssignment"}}, "failedTasks": {"type": "array", "items": {"$ref": "#/components/schemas/FailedTask"}}, "suggestions": {"type": "array", "items": {"type": "string"}}, "summary": {"$ref": "#/components/schemas/AssignmentSummary"}}}, "AssignmentSummary": {"type": "object", "properties": {"totalTasks": {"type": "integer", "format": "int32"}, "successfulAssignments": {"type": "integer", "format": "int32"}, "failedAssignments": {"type": "integer", "format": "int32"}, "averageConfidence": {"type": "number", "format": "double"}, "processingTime": {"type": "integer", "format": "int64"}, "strategy": {"type": "string"}}}, "FailedTask": {"type": "object", "properties": {"taskId": {"type": "integer", "format": "int64"}, "taskName": {"type": "string"}, "failureReason": {"type": "string"}, "failureCode": {"type": "string"}, "suggestedActions": {"type": "array", "items": {"type": "string"}}}}, "RAssignmentResult": {"title": "响应数据结构", "type": "object", "properties": {"code": {"title": "编码：1000表示成功，其他值表示失败", "type": "integer", "format": "int32"}, "message": {"title": "消息内容", "type": "string"}, "data": {"$ref": "#/components/schemas/AssignmentResult"}}}, "TaskAssignment": {"type": "object", "properties": {"taskId": {"type": "integer", "format": "int64"}, "taskName": {"type": "string"}, "assignees": {"type": "array", "items": {"$ref": "#/components/schemas/Assignee"}}, "reason": {"type": "string"}, "aiGenerated": {"type": "boolean"}, "assignmentTime": {"type": "string", "format": "date-time"}, "confidence": {"type": "integer", "format": "int32"}}}, "ManualAssignmentRequest": {"type": "object", "properties": {"taskId": {"type": "integer", "format": "int64"}, "assigneeIds": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "collaborationAllowed": {"type": "boolean"}, "reason": {"type": "string"}}}, "RString": {"title": "响应数据结构", "type": "object", "properties": {"code": {"title": "编码：1000表示成功，其他值表示失败", "type": "integer", "format": "int32"}, "message": {"title": "消息内容", "type": "string"}, "data": {"title": "响应数据", "type": "string"}}}, "AssignmentConstraints": {"type": "object", "properties": {"requiredRoles": {"type": "array", "items": {"type": "string"}}, "maxWorkload": {"type": "integer", "format": "int32"}, "deadline": {"type": "string", "format": "date-time"}, "collaborationAllowed": {"type": "boolean"}, "locationRequirement": {"type": "string"}, "requiredSkills": {"type": "array", "items": {"type": "string"}}, "requiredAssigneeIds": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "minLoad": {"type": "integer", "format": "int32"}, "maxLoad": {"type": "integer", "format": "int32"}}}, "AssignmentPreferences": {"type": "object", "properties": {"prioritizeExperience": {"type": "boolean"}, "balanceWorkload": {"type": "boolean"}, "prioritizeLocal": {"type": "boolean"}, "roleMatchWeight": {"type": "integer", "format": "int32"}, "workloadWeight": {"type": "integer", "format": "int32"}, "performanceWeight": {"type": "integer", "format": "int32"}}}, "AssignmentRequest": {"type": "object", "properties": {"assignmentType": {"type": "string"}, "taskIds": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "autoAssign": {"type": "boolean"}, "constraints": {"$ref": "#/components/schemas/AssignmentConstraints"}, "preferences": {"$ref": "#/components/schemas/AssignmentPreferences"}}}, "CandidateProfile": {"title": "响应数据", "type": "object", "properties": {"userId": {"type": "integer", "format": "int64"}, "userName": {"type": "string"}, "roles": {"type": "array", "items": {"type": "string"}}, "skills": {"type": "array", "items": {"$ref": "#/components/schemas/UserSkill"}}, "currentWorkload": {"type": "integer", "format": "int32"}, "performanceScore": {"type": "integer", "format": "int32"}, "isOnline": {"type": "boolean"}, "isAvailable": {"type": "boolean"}, "location": {"type": "string"}, "workTimePreference": {"$ref": "#/components/schemas/WorkTimePreference"}, "recentSimilarTasks": {"type": "integer", "format": "int32"}, "averageCompletionTime": {"type": "number", "format": "double"}, "qualityScore": {"type": "integer", "format": "int32"}, "lastActiveTime": {"type": "string", "format": "date-time"}}}, "RListCandidateProfile": {"title": "响应数据结构", "type": "object", "properties": {"code": {"title": "编码：1000表示成功，其他值表示失败", "type": "integer", "format": "int32"}, "message": {"title": "消息内容", "type": "string"}, "data": {"title": "响应数据", "type": "array", "items": {"$ref": "#/components/schemas/CandidateProfile"}}}}, "UserSkill": {"type": "object", "properties": {"skillName": {"type": "string"}, "skillLevel": {"type": "integer", "format": "int32"}, "experienceYears": {"type": "number", "format": "double"}, "certified": {"type": "boolean"}}}, "WorkTimePreference": {"type": "object", "properties": {"startTime": {"type": "string"}, "endTime": {"type": "string"}, "workDays": {"type": "array", "items": {"type": "integer", "format": "int32"}}, "timezone": {"type": "string"}}}, "SpaceTypeEntity": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "createTime": {"type": "string", "format": "date-time"}, "updateTime": {"type": "string", "format": "date-time"}, "name": {"type": "string"}, "parentId": {"type": "integer", "format": "int32"}}}, "PageResultSpaceTypeEntity": {"title": "分页数据模型", "type": "object", "properties": {"list": {"title": "分页数据", "type": "array", "items": {"$ref": "#/components/schemas/SpaceTypeEntity"}}, "pagination": {"$ref": "#/components/schemas/Pagination"}}}, "RPageResultSpaceTypeEntity": {"title": "响应数据结构", "type": "object", "properties": {"code": {"title": "编码：1000表示成功，其他值表示失败", "type": "integer", "format": "int32"}, "message": {"title": "消息内容", "type": "string"}, "data": {"$ref": "#/components/schemas/PageResultSpaceTypeEntity"}}}, "RListSpaceTypeEntity": {"title": "响应数据结构", "type": "object", "properties": {"code": {"title": "编码：1000表示成功，其他值表示失败", "type": "integer", "format": "int32"}, "message": {"title": "消息内容", "type": "string"}, "data": {"title": "响应数据", "type": "array", "items": {"$ref": "#/components/schemas/SpaceTypeEntity"}}}}, "SpaceInfoEntity": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "createTime": {"type": "string", "format": "date-time"}, "updateTime": {"type": "string", "format": "date-time"}, "url": {"type": "string"}, "type": {"type": "string"}, "classifyId": {"type": "integer", "format": "int32"}, "fileId": {"type": "string"}, "name": {"type": "string"}, "size": {"type": "integer", "format": "int32"}, "version": {"type": "integer", "format": "int64"}, "filePath": {"type": "string"}, "key": {"type": "string"}}}, "PageResultSpaceInfoEntity": {"title": "分页数据模型", "type": "object", "properties": {"list": {"title": "分页数据", "type": "array", "items": {"$ref": "#/components/schemas/SpaceInfoEntity"}}, "pagination": {"$ref": "#/components/schemas/Pagination"}}}, "RPageResultSpaceInfoEntity": {"title": "响应数据结构", "type": "object", "properties": {"code": {"title": "编码：1000表示成功，其他值表示失败", "type": "integer", "format": "int32"}, "message": {"title": "消息内容", "type": "string"}, "data": {"$ref": "#/components/schemas/PageResultSpaceInfoEntity"}}}, "RListSpaceInfoEntity": {"title": "响应数据结构", "type": "object", "properties": {"code": {"title": "编码：1000表示成功，其他值表示失败", "type": "integer", "format": "int32"}, "message": {"title": "消息内容", "type": "string"}, "data": {"title": "响应数据", "type": "array", "items": {"$ref": "#/components/schemas/SpaceInfoEntity"}}}}, "WorkOrderEntity": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "createTime": {"type": "string", "format": "date-time"}, "updateTime": {"type": "string", "format": "date-time"}, "sopTemplateId": {"type": "integer", "format": "int64"}, "orderNo": {"type": "string"}, "title": {"type": "string"}, "description": {"type": "string"}, "priority": {"type": "integer", "format": "int32"}, "urgency": {"type": "integer", "format": "int32"}, "businessType": {"type": "string"}, "businessData": {"type": "string"}, "applicantId": {"type": "integer", "format": "int64"}, "applicantName": {"type": "string"}, "applicantDept": {"type": "string"}, "assigneeId": {"type": "integer", "format": "int64"}, "assigneeName": {"type": "string"}, "plannedStartTime": {"type": "string", "format": "date-time"}, "plannedEndTime": {"type": "string", "format": "date-time"}, "actualStartTime": {"type": "string", "format": "date-time"}, "actualEndTime": {"type": "string", "format": "date-time"}, "status": {"type": "integer", "format": "int32"}, "progress": {"type": "integer", "format": "int32"}, "estimatedWorkTime": {"type": "integer", "format": "int32"}, "actualWorkTime": {"type": "integer", "format": "int32"}, "qualityScore": {"type": "integer", "format": "int32"}, "aiScheduled": {"type": "boolean"}, "aiScheduleConfig": {"type": "string"}, "aiPredictedEndTime": {"type": "string", "format": "date-time"}, "aiRiskAssessment": {"type": "string"}, "executionTeam": {"type": "string"}, "remark": {"type": "string"}, "relatedBusinessId": {"type": "integer", "format": "int64"}, "relatedBusinessType": {"type": "string"}, "executionResult": {"type": "string"}, "failureReason": {"type": "string"}, "customerSatisfaction": {"type": "integer", "format": "int32"}, "customerFeedback": {"type": "string"}}}, "PageResultWorkOrderEntity": {"title": "分页数据模型", "type": "object", "properties": {"list": {"title": "分页数据", "type": "array", "items": {"$ref": "#/components/schemas/WorkOrderEntity"}}, "pagination": {"$ref": "#/components/schemas/Pagination"}}}, "RPageResultWorkOrderEntity": {"title": "响应数据结构", "type": "object", "properties": {"code": {"title": "编码：1000表示成功，其他值表示失败", "type": "integer", "format": "int32"}, "message": {"title": "消息内容", "type": "string"}, "data": {"$ref": "#/components/schemas/PageResultWorkOrderEntity"}}}, "RListWorkOrderEntity": {"title": "响应数据结构", "type": "object", "properties": {"code": {"title": "编码：1000表示成功，其他值表示失败", "type": "integer", "format": "int32"}, "message": {"title": "消息内容", "type": "string"}, "data": {"title": "响应数据", "type": "array", "items": {"$ref": "#/components/schemas/WorkOrderEntity"}}}}, "TaskExecuteRecordEntity": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "createTime": {"type": "string", "format": "date-time"}, "updateTime": {"type": "string", "format": "date-time"}, "workOrderId": {"type": "integer", "format": "int64"}, "sopStepId": {"type": "integer", "format": "int64"}, "taskName": {"type": "string"}, "taskDescription": {"type": "string"}, "executorId": {"type": "integer", "format": "int64"}, "executorName": {"type": "string"}, "plannedStartTime": {"type": "string", "format": "date-time"}, "plannedEndTime": {"type": "string", "format": "date-time"}, "actualStartTime": {"type": "string", "format": "date-time"}, "actualEndTime": {"type": "string", "format": "date-time"}, "status": {"type": "integer", "format": "int32"}, "progress": {"type": "integer", "format": "int32"}, "estimatedTime": {"type": "integer", "format": "int32"}, "actualTime": {"type": "integer", "format": "int32"}, "executionResult": {"type": "string"}, "executionProcess": {"type": "string"}, "aiGuidance": {"type": "string"}, "aiPrediction": {"type": "string"}, "aiRiskAlert": {"type": "string"}, "qualityCheckResult": {"type": "string"}, "qualityScore": {"type": "integer", "format": "int32"}, "exceptionInfo": {"type": "string"}, "exceptionHandling": {"type": "string"}, "skipReason": {"type": "string"}, "reworkCount": {"type": "integer", "format": "int32"}, "reworkReason": {"type": "string"}, "attachments": {"type": "string"}, "remark": {"type": "string"}, "skillMatch": {"type": "integer", "format": "int32"}, "difficultyRating": {"type": "integer", "format": "int32"}, "satisfactionRating": {"type": "integer", "format": "int32"}, "improvementSuggestion": {"type": "string"}}}, "PageResultTaskExecuteRecordEntity": {"title": "分页数据模型", "type": "object", "properties": {"list": {"title": "分页数据", "type": "array", "items": {"$ref": "#/components/schemas/TaskExecuteRecordEntity"}}, "pagination": {"$ref": "#/components/schemas/Pagination"}}}, "RPageResultTaskExecuteRecordEntity": {"title": "响应数据结构", "type": "object", "properties": {"code": {"title": "编码：1000表示成功，其他值表示失败", "type": "integer", "format": "int32"}, "message": {"title": "消息内容", "type": "string"}, "data": {"$ref": "#/components/schemas/PageResultTaskExecuteRecordEntity"}}}, "RListTaskExecuteRecordEntity": {"title": "响应数据结构", "type": "object", "properties": {"code": {"title": "编码：1000表示成功，其他值表示失败", "type": "integer", "format": "int32"}, "message": {"title": "消息内容", "type": "string"}, "data": {"title": "响应数据", "type": "array", "items": {"$ref": "#/components/schemas/TaskExecuteRecordEntity"}}}}, "SOPStepEntity": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "createTime": {"type": "string", "format": "date-time"}, "updateTime": {"type": "string", "format": "date-time"}, "sopId": {"type": "integer", "format": "int64"}, "industryId": {"type": "integer", "format": "int64"}, "industryName": {"type": "string"}, "stage": {"type": "string"}, "moduleCode": {"type": "string"}, "moduleName": {"type": "string"}, "scenarioCode": {"type": "string"}, "scenarioName": {"type": "string"}, "executionCycle": {"type": "string"}, "stepCode": {"type": "string"}, "stepName": {"type": "string"}, "stepDescription": {"type": "string"}, "stepOrder": {"type": "integer", "format": "int32"}, "entityTouchpoint": {"type": "string"}, "userActivity": {"type": "string"}, "employeeBehavior": {"type": "string"}, "workHighlight": {"type": "string"}, "employeeRole": {"type": "string"}, "relatedAttachments": {"type": "string"}, "stepType": {"type": "string"}, "isRequired": {"type": "boolean"}, "estimatedTime": {"type": "integer", "format": "int32"}, "skillRequirements": {"type": "string"}, "toolsRequired": {"type": "string"}, "qualityCheckPoints": {"type": "string"}, "riskWarnings": {"type": "string"}, "successCriteria": {"type": "string"}, "failureHandling": {"type": "string"}, "nextStepCondition": {"type": "string"}, "parallelSteps": {"type": "string"}, "prerequisiteSteps": {"type": "string"}, "status": {"type": "integer", "format": "int32"}, "version": {"type": "string"}}}, "PageResultSOPStepEntity": {"title": "分页数据模型", "type": "object", "properties": {"list": {"title": "分页数据", "type": "array", "items": {"$ref": "#/components/schemas/SOPStepEntity"}}, "pagination": {"$ref": "#/components/schemas/Pagination"}}}, "RPageResultSOPStepEntity": {"title": "响应数据结构", "type": "object", "properties": {"code": {"title": "编码：1000表示成功，其他值表示失败", "type": "integer", "format": "int32"}, "message": {"title": "消息内容", "type": "string"}, "data": {"$ref": "#/components/schemas/PageResultSOPStepEntity"}}}, "RListSOPStepEntity": {"title": "响应数据结构", "type": "object", "properties": {"code": {"title": "编码：1000表示成功，其他值表示失败", "type": "integer", "format": "int32"}, "message": {"title": "消息内容", "type": "string"}, "data": {"title": "响应数据", "type": "array", "items": {"$ref": "#/components/schemas/SOPStepEntity"}}}}, "SOPScenarioEntity": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "createTime": {"type": "string", "format": "date-time"}, "updateTime": {"type": "string", "format": "date-time"}, "industryId": {"type": "integer", "format": "int64"}, "industryName": {"type": "string"}, "stage": {"type": "string"}, "moduleCode": {"type": "string"}, "moduleName": {"type": "string"}, "scenarioCode": {"type": "string"}, "scenarioName": {"type": "string"}, "executionCycle": {"type": "string"}, "executionFrequency": {"type": "string"}, "executionCount": {"type": "integer", "format": "int32"}, "version": {"type": "string"}, "status": {"type": "integer", "format": "int32"}, "description": {"type": "string"}, "totalSteps": {"type": "integer", "format": "int32"}, "estimatedDuration": {"type": "integer", "format": "int32"}, "difficultyLevel": {"type": "integer", "format": "int32"}, "qualityStandard": {"type": "string"}, "successCriteria": {"type": "string"}, "riskPoints": {"type": "string"}, "attentionPoints": {"type": "string"}, "applicableArea": {"type": "string"}}}, "PageResultSOPScenarioEntity": {"title": "分页数据模型", "type": "object", "properties": {"list": {"title": "分页数据", "type": "array", "items": {"$ref": "#/components/schemas/SOPScenarioEntity"}}, "pagination": {"$ref": "#/components/schemas/Pagination"}}}, "RPageResultSOPScenarioEntity": {"title": "响应数据结构", "type": "object", "properties": {"code": {"title": "编码：1000表示成功，其他值表示失败", "type": "integer", "format": "int32"}, "message": {"title": "消息内容", "type": "string"}, "data": {"$ref": "#/components/schemas/PageResultSOPScenarioEntity"}}}, "RListSOPScenarioEntity": {"title": "响应数据结构", "type": "object", "properties": {"code": {"title": "编码：1000表示成功，其他值表示失败", "type": "integer", "format": "int32"}, "message": {"title": "消息内容", "type": "string"}, "data": {"title": "响应数据", "type": "array", "items": {"$ref": "#/components/schemas/SOPScenarioEntity"}}}}, "JSONConfig": {"type": "object", "properties": {"keyComparator": {"type": "object"}, "ignoreError": {"type": "boolean"}, "ignoreCase": {"type": "boolean"}, "dateFormat": {"type": "string"}, "ignoreNullValue": {"type": "boolean"}, "transientSupport": {"type": "boolean"}, "stripTrailingZeros": {"type": "boolean"}, "checkDuplicate": {"type": "boolean"}, "order": {"type": "boolean", "deprecated": true}}}, "JSONObject": {"type": "object", "properties": {"raw": {"type": "object", "additionalProperties": {"type": "object"}}, "config": {"$ref": "#/components/schemas/JSONConfig"}, "empty": {"type": "boolean"}}, "additionalProperties": {"type": "object"}}, "SOPIndustryEntity": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "createTime": {"type": "string", "format": "date-time"}, "updateTime": {"type": "string", "format": "date-time"}, "industryCode": {"type": "string"}, "industryName": {"type": "string"}, "description": {"type": "string"}, "parentId": {"type": "integer", "format": "int64"}, "sort": {"type": "integer", "format": "int32"}, "status": {"type": "integer", "format": "int32"}, "icon": {"type": "string"}, "config": {"type": "string"}}}, "PageResultSOPIndustryEntity": {"title": "分页数据模型", "type": "object", "properties": {"list": {"title": "分页数据", "type": "array", "items": {"$ref": "#/components/schemas/SOPIndustryEntity"}}, "pagination": {"$ref": "#/components/schemas/Pagination"}}}, "RPageResultSOPIndustryEntity": {"title": "响应数据结构", "type": "object", "properties": {"code": {"title": "编码：1000表示成功，其他值表示失败", "type": "integer", "format": "int32"}, "message": {"title": "消息内容", "type": "string"}, "data": {"$ref": "#/components/schemas/PageResultSOPIndustryEntity"}}}, "RListSOPIndustryEntity": {"title": "响应数据结构", "type": "object", "properties": {"code": {"title": "编码：1000表示成功，其他值表示失败", "type": "integer", "format": "int32"}, "message": {"title": "消息内容", "type": "string"}, "data": {"title": "响应数据", "type": "array", "items": {"$ref": "#/components/schemas/SOPIndustryEntity"}}}}, "ImportStatistics": {"type": "object", "properties": {"totalScenarios": {"type": "integer", "format": "int32"}, "totalSteps": {"type": "integer", "format": "int32"}, "successScenarios": {"type": "integer", "format": "int32"}, "failedScenarios": {"type": "integer", "format": "int32"}, "updatedScenarios": {"type": "integer", "format": "int32"}, "newScenarios": {"type": "integer", "format": "int32"}, "skippedScenarios": {"type": "integer", "format": "int32"}}}, "ImportedScenario": {"type": "object", "properties": {"scenarioId": {"type": "integer", "format": "int64"}, "scenarioCode": {"type": "string"}, "scenarioName": {"type": "string"}, "version": {"type": "string"}, "action": {"type": "string"}, "stepCount": {"type": "integer", "format": "int32"}}}, "RSOPImportResult": {"title": "响应数据结构", "type": "object", "properties": {"code": {"title": "编码：1000表示成功，其他值表示失败", "type": "integer", "format": "int32"}, "message": {"title": "消息内容", "type": "string"}, "data": {"$ref": "#/components/schemas/SOPImportResult"}}}, "SOPImportResult": {"title": "响应数据", "type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string"}, "statistics": {"$ref": "#/components/schemas/ImportStatistics"}, "versionConflicts": {"type": "array", "items": {"$ref": "#/components/schemas/VersionConflict"}}, "validationErrors": {"type": "array", "items": {"$ref": "#/components/schemas/ValidationError"}}, "importedScenarios": {"type": "array", "items": {"$ref": "#/components/schemas/ImportedScenario"}}, "confirmations": {"type": "array", "items": {"$ref": "#/components/schemas/UserConfirmation"}}, "data": {"type": "object"}}}, "UserConfirmation": {"type": "object", "properties": {"type": {"type": "string"}, "scenarioCode": {"type": "string"}, "scenarioName": {"type": "string"}, "currentVersion": {"type": "string"}, "newVersion": {"type": "string"}, "message": {"type": "string"}, "options": {"type": "object", "additionalProperties": {"type": "object"}}}}, "ValidationError": {"type": "object", "properties": {"type": {"type": "string"}, "field": {"type": "string"}, "value": {"type": "string"}, "message": {"type": "string"}, "rowNumber": {"type": "integer", "format": "int32"}, "scenarioCode": {"type": "string"}}}, "VersionConflict": {"type": "object", "properties": {"scenarioCode": {"type": "string"}, "scenarioName": {"type": "string"}, "existingVersion": {"type": "string"}, "importVersion": {"type": "string"}, "conflictType": {"type": "string"}, "recommendation": {"type": "string"}}}, "RObject": {"title": "响应数据结构", "type": "object", "properties": {"code": {"title": "编码：1000表示成功，其他值表示失败", "type": "integer", "format": "int32"}, "message": {"title": "消息内容", "type": "string"}, "data": {"title": "响应数据", "type": "object"}}}, "AiTaskGenerateRecordEntity": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "createTime": {"type": "string", "format": "date-time"}, "updateTime": {"type": "string", "format": "date-time"}, "userId": {"type": "integer", "format": "int64"}, "userName": {"type": "string"}, "taskDesc": {"type": "string"}, "params": {"type": "string"}, "progress": {"type": "integer", "format": "int32"}, "status": {"type": "integer", "format": "int32"}, "result": {"type": "string"}, "failReason": {"type": "string"}, "costTime": {"type": "integer", "format": "int32"}, "progressDetails": {"type": "string"}, "preview": {"type": "boolean"}, "mode": {"type": "string"}, "parentRecordId": {"type": "integer", "format": "int64"}, "updatedTime": {"type": "string", "format": "date-time", "writeOnly": true}}}, "PageResultAiTaskGenerateRecordEntity": {"title": "分页数据模型", "type": "object", "properties": {"list": {"title": "分页数据", "type": "array", "items": {"$ref": "#/components/schemas/AiTaskGenerateRecordEntity"}}, "pagination": {"$ref": "#/components/schemas/Pagination"}}}, "RPageResultAiTaskGenerateRecordEntity": {"title": "响应数据结构", "type": "object", "properties": {"code": {"title": "编码：1000表示成功，其他值表示失败", "type": "integer", "format": "int32"}, "message": {"title": "消息内容", "type": "string"}, "data": {"$ref": "#/components/schemas/PageResultAiTaskGenerateRecordEntity"}}}, "RListAiTaskGenerateRecordEntity": {"title": "响应数据结构", "type": "object", "properties": {"code": {"title": "编码：1000表示成功，其他值表示失败", "type": "integer", "format": "int32"}, "message": {"title": "消息内容", "type": "string"}, "data": {"title": "响应数据", "type": "array", "items": {"$ref": "#/components/schemas/AiTaskGenerateRecordEntity"}}}}, "RListMapStringObject": {"title": "响应数据结构", "type": "object", "properties": {"code": {"title": "编码：1000表示成功，其他值表示失败", "type": "integer", "format": "int32"}, "message": {"title": "消息内容", "type": "string"}, "data": {"title": "响应数据", "type": "array", "items": {"title": "响应数据", "type": "object", "additionalProperties": {"title": "响应数据", "type": "object"}}}}}, "RMapStringLong": {"title": "响应数据结构", "type": "object", "properties": {"code": {"title": "编码：1000表示成功，其他值表示失败", "type": "integer", "format": "int32"}, "message": {"title": "消息内容", "type": "string"}, "data": {"title": "响应数据", "type": "object", "additionalProperties": {"title": "响应数据", "type": "integer", "format": "int64"}}}}, "TaskGenerateRequest": {"required": ["taskDescription"], "type": "object", "properties": {"taskDescription": {"maxLength": 1000, "minLength": 0, "type": "string"}, "industryId": {"type": "integer", "format": "int64"}, "scenarioId": {"type": "integer", "format": "int64"}, "priority": {"type": "integer", "format": "int32"}, "expectedTaskCount": {"type": "integer", "format": "int32"}, "assigneeId": {"type": "integer", "format": "int64"}, "startTime": {"type": "string"}, "endTime": {"type": "string"}, "deadline": {"type": "string", "deprecated": true}, "additionalContext": {"type": "string"}, "useAIEnhancement": {"type": "boolean"}, "autoAssign": {"type": "boolean"}, "departmentIds": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "departmentId": {"type": "integer", "format": "int64", "writeOnly": true}}}, "GeneratedTask": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "name": {"type": "string"}, "taskName": {"type": "string"}, "description": {"type": "string"}, "taskDescription": {"type": "string"}, "taskType": {"type": "string"}, "taskCategory": {"type": "string"}, "priority": {"type": "integer", "format": "int32"}, "estimatedDuration": {"type": "integer", "format": "int32"}, "assigneeId": {"type": "integer", "format": "int64"}, "status": {"type": "string"}, "requirements": {"type": "array", "items": {"type": "string"}}, "metadata": {"type": "object", "additionalProperties": {"type": "object"}}, "scenarioId": {"type": "integer", "format": "int64"}, "scenarioCode": {"type": "string"}, "scenarioName": {"type": "string"}, "stepId": {"type": "integer", "format": "int64"}, "stepCode": {"type": "string"}, "stepName": {"type": "string"}, "entityTouchpoint": {"type": "string"}, "taskActivity": {"type": "string"}, "employeeBehavior": {"type": "string"}, "workHighlight": {"type": "string"}, "employeeRole": {"type": "string"}, "photoRequired": {"type": "boolean"}, "attachmentRequired": {"type": "boolean"}, "startTime": {"type": "string"}, "endTime": {"type": "string"}, "recommendedAssignees": {"type": "array", "items": {"$ref": "#/components/schemas/RecommendedAssignee"}}, "assigneeName": {"type": "string"}, "assigneeRole": {"type": "string"}, "assignmentReason": {"type": "string"}, "assignmentConfidence": {"type": "integer", "format": "int32"}, "executors": {"type": "array", "items": {"type": "object", "additionalProperties": {"type": "object"}}}, "candidates": {"type": "array", "items": {"type": "object", "additionalProperties": {"type": "object"}}}, "isAssigned": {"type": "boolean"}, "canReassign": {"type": "boolean"}, "assignmentStatus": {"type": "string"}}}, "MultiDepartmentGenerateResponse": {"title": "响应数据", "type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string"}, "processingTime": {"type": "integer", "format": "int64"}, "totalDepartments": {"type": "integer", "format": "int32"}, "totalTasksGenerated": {"type": "integer", "format": "int32"}, "results": {"type": "array", "items": {"$ref": "#/components/schemas/TaskGenerateResponse"}}, "summary": {"type": "array", "items": {"type": "string"}}}}, "RMultiDepartmentGenerateResponse": {"title": "响应数据结构", "type": "object", "properties": {"code": {"title": "编码：1000表示成功，其他值表示失败", "type": "integer", "format": "int32"}, "message": {"title": "消息内容", "type": "string"}, "data": {"$ref": "#/components/schemas/MultiDepartmentGenerateResponse"}}}, "RecommendedAssignee": {"type": "object", "properties": {"userId": {"type": "integer", "format": "int64"}, "userName": {"type": "string"}, "userRole": {"type": "string"}, "confidence": {"type": "integer", "format": "int32"}, "reason": {"type": "string"}, "currentWorkload": {"type": "integer", "format": "int32"}, "isPrimary": {"type": "boolean"}}}, "ScenarioInfo": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "name": {"type": "string"}, "code": {"type": "string"}, "description": {"type": "string"}, "industry": {"type": "string"}, "matchScore": {"type": "number", "format": "double"}}}, "TaskGenerateResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string"}, "scenario": {"$ref": "#/components/schemas/ScenarioInfo"}, "tasksGenerated": {"type": "integer", "format": "int32"}, "tasks": {"type": "array", "items": {"$ref": "#/components/schemas/GeneratedTask"}}, "confidenceScore": {"type": "number", "format": "double"}, "processingTime": {"type": "integer", "format": "int64"}, "suggestions": {"type": "array", "items": {"type": "string"}}, "metadata": {"type": "object", "additionalProperties": {"type": "object"}}, "departmentId": {"type": "integer", "format": "int64"}, "departmentName": {"type": "string"}}}, "RMapStringObject": {"title": "响应数据结构", "type": "object", "properties": {"code": {"title": "编码：1000表示成功，其他值表示失败", "type": "integer", "format": "int32"}, "message": {"title": "消息内容", "type": "string"}, "data": {"title": "响应数据", "type": "object", "additionalProperties": {"title": "响应数据", "type": "object"}}}}, "EntityInfo": {"type": "object", "properties": {"entityClassName": {"type": "string"}}}, "RecycleDataEntity": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "createTime": {"type": "string", "format": "date-time"}, "updateTime": {"type": "string", "format": "date-time"}, "entityInfo": {"$ref": "#/components/schemas/EntityInfo"}, "userId": {"type": "integer", "format": "int64"}, "data": {"type": "array", "items": {"type": "object"}}, "url": {"type": "string"}, "params": {"type": "object", "additionalProperties": {"type": "object"}}, "count": {"type": "integer", "format": "int32"}, "userName": {"type": "string"}}}, "PageResultRecycleDataEntity": {"title": "分页数据模型", "type": "object", "properties": {"list": {"title": "分页数据", "type": "array", "items": {"$ref": "#/components/schemas/RecycleDataEntity"}}, "pagination": {"$ref": "#/components/schemas/Pagination"}}}, "RPageResultRecycleDataEntity": {"title": "响应数据结构", "type": "object", "properties": {"code": {"title": "编码：1000表示成功，其他值表示失败", "type": "integer", "format": "int32"}, "message": {"title": "消息内容", "type": "string"}, "data": {"$ref": "#/components/schemas/PageResultRecycleDataEntity"}}}, "RListRecycleDataEntity": {"title": "响应数据结构", "type": "object", "properties": {"code": {"title": "编码：1000表示成功，其他值表示失败", "type": "integer", "format": "int32"}, "message": {"title": "消息内容", "type": "string"}, "data": {"title": "响应数据", "type": "array", "items": {"$ref": "#/components/schemas/RecycleDataEntity"}}}}, "PluginInfoEntity": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "createTime": {"type": "string", "format": "date-time"}, "updateTime": {"type": "string", "format": "date-time"}, "name": {"type": "string"}, "description": {"type": "string"}, "key": {"type": "string"}, "hook": {"type": "string"}, "readme": {"type": "string"}, "version": {"type": "string"}, "logo": {"type": "string"}, "author": {"type": "string"}, "status": {"type": "integer", "format": "int32"}, "pluginJson": {"$ref": "#/components/schemas/PluginJson"}, "config": {"type": "object"}, "keyName": {"type": "string"}}}, "PluginJson": {"type": "object", "properties": {"name": {"type": "string"}, "key": {"type": "string"}, "hook": {"type": "string"}, "version": {"type": "string"}, "description": {"type": "string"}, "author": {"type": "string"}, "logo": {"type": "string"}, "readme": {"type": "string"}, "config": {"type": "object", "additionalProperties": {"type": "object"}}, "jarPath": {"type": "string"}}}, "PageResultPluginInfoEntity": {"title": "分页数据模型", "type": "object", "properties": {"list": {"title": "分页数据", "type": "array", "items": {"$ref": "#/components/schemas/PluginInfoEntity"}}, "pagination": {"$ref": "#/components/schemas/Pagination"}}}, "RPageResultPluginInfoEntity": {"title": "响应数据结构", "type": "object", "properties": {"code": {"title": "编码：1000表示成功，其他值表示失败", "type": "integer", "format": "int32"}, "message": {"title": "消息内容", "type": "string"}, "data": {"$ref": "#/components/schemas/PageResultPluginInfoEntity"}}}, "RListPluginInfoEntity": {"title": "响应数据结构", "type": "object", "properties": {"code": {"title": "编码：1000表示成功，其他值表示失败", "type": "integer", "format": "int32"}, "message": {"title": "消息内容", "type": "string"}, "data": {"title": "响应数据", "type": "array", "items": {"$ref": "#/components/schemas/PluginInfoEntity"}}}}, "DictTypeEntity": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "createTime": {"type": "string", "format": "date-time"}, "updateTime": {"type": "string", "format": "date-time"}, "name": {"type": "string"}, "key": {"type": "string"}}}, "PageResultDictTypeEntity": {"title": "分页数据模型", "type": "object", "properties": {"list": {"title": "分页数据", "type": "array", "items": {"$ref": "#/components/schemas/DictTypeEntity"}}, "pagination": {"$ref": "#/components/schemas/Pagination"}}}, "RPageResultDictTypeEntity": {"title": "响应数据结构", "type": "object", "properties": {"code": {"title": "编码：1000表示成功，其他值表示失败", "type": "integer", "format": "int32"}, "message": {"title": "消息内容", "type": "string"}, "data": {"$ref": "#/components/schemas/PageResultDictTypeEntity"}}}, "RListDictTypeEntity": {"title": "响应数据结构", "type": "object", "properties": {"code": {"title": "编码：1000表示成功，其他值表示失败", "type": "integer", "format": "int32"}, "message": {"title": "消息内容", "type": "string"}, "data": {"title": "响应数据", "type": "array", "items": {"$ref": "#/components/schemas/DictTypeEntity"}}}}, "DictInfoEntity": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "createTime": {"type": "string", "format": "date-time"}, "updateTime": {"type": "string", "format": "date-time"}, "typeId": {"type": "integer", "format": "int64"}, "parentId": {"type": "integer", "format": "int64"}, "name": {"type": "string"}, "value": {"type": "string"}, "orderNum": {"type": "integer", "format": "int32"}, "remark": {"type": "string"}}}, "PageResultDictInfoEntity": {"title": "分页数据模型", "type": "object", "properties": {"list": {"title": "分页数据", "type": "array", "items": {"$ref": "#/components/schemas/DictInfoEntity"}}, "pagination": {"$ref": "#/components/schemas/Pagination"}}}, "RPageResultDictInfoEntity": {"title": "响应数据结构", "type": "object", "properties": {"code": {"title": "编码：1000表示成功，其他值表示失败", "type": "integer", "format": "int32"}, "message": {"title": "消息内容", "type": "string"}, "data": {"$ref": "#/components/schemas/PageResultDictInfoEntity"}}}, "RListDictInfoEntity": {"title": "响应数据结构", "type": "object", "properties": {"code": {"title": "编码：1000表示成功，其他值表示失败", "type": "integer", "format": "int32"}, "message": {"title": "消息内容", "type": "string"}, "data": {"title": "响应数据", "type": "array", "items": {"$ref": "#/components/schemas/DictInfoEntity"}}}}, "Dict": {"type": "object", "properties": {"empty": {"type": "boolean"}}, "additionalProperties": {"type": "object"}}, "UserQueryRequest": {"type": "object", "properties": {"departmentIds": {"type": "array", "description": "部门ID列表", "items": {"type": "integer", "description": "部门ID列表", "format": "int64"}}, "roleIds": {"type": "array", "description": "角色ID列表", "items": {"type": "integer", "description": "角色ID列表", "format": "int64"}}, "roleNames": {"type": "array", "description": "角色名称列表", "items": {"type": "string", "description": "角色名称列表"}}, "name": {"type": "string", "description": "姓名模糊查询"}, "phone": {"type": "string", "description": "手机号模糊查询"}, "username": {"type": "string", "description": "用户名模糊查询"}, "email": {"type": "string", "description": "邮箱模糊查询"}, "status": {"type": "integer", "description": "用户状态：0-禁用，1-启用", "format": "int32"}, "excludeAdmin": {"type": "boolean", "description": "是否排除admin用户", "default": true}, "includeRoles": {"type": "boolean", "description": "是否包含角色信息", "default": true}, "includeDepartment": {"type": "boolean", "description": "是否包含部门信息", "default": true}, "orderBy": {"type": "string", "description": "排序字段"}, "orderDirection": {"type": "string", "description": "排序方向：asc-升序，desc-降序", "default": "asc"}, "keyword": {"type": "string", "description": "统一关键字模糊查询（姓名 / 用户名 / 手机号）"}}, "description": "用户查询请求"}, "BaseSysUserEntity": {"title": "响应数据", "type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "createTime": {"type": "string", "format": "date-time"}, "updateTime": {"type": "string", "format": "date-time"}, "tenantId": {"type": "integer", "format": "int64"}, "departmentId": {"type": "integer", "format": "int64"}, "name": {"type": "string"}, "username": {"type": "string"}, "password": {"type": "string"}, "passwordV": {"type": "integer", "format": "int32"}, "nickName": {"type": "string"}, "headImg": {"type": "string"}, "phone": {"type": "string"}, "email": {"type": "string"}, "remark": {"type": "string"}, "status": {"type": "integer", "format": "int32"}, "departmentName": {"type": "string"}, "roleName": {"type": "string"}, "socketId": {"type": "string"}, "roleIdList": {"type": "array", "description": "角色列表", "items": {"type": "integer", "description": "角色列表", "format": "int64"}}, "workload": {"type": "integer", "format": "int32"}, "performanceScore": {"type": "integer", "format": "int32"}, "isAvailable": {"type": "boolean"}}}, "RListBaseSysUserEntity": {"title": "响应数据结构", "type": "object", "properties": {"code": {"title": "编码：1000表示成功，其他值表示失败", "type": "integer", "format": "int32"}, "message": {"title": "消息内容", "type": "string"}, "data": {"title": "响应数据", "type": "array", "items": {"$ref": "#/components/schemas/BaseSysUserEntity"}}}}, "PageResultBaseSysUserEntity": {"title": "分页数据模型", "type": "object", "properties": {"list": {"title": "分页数据", "type": "array", "items": {"$ref": "#/components/schemas/BaseSysUserEntity"}}, "pagination": {"$ref": "#/components/schemas/Pagination"}}}, "RPageResultBaseSysUserEntity": {"title": "响应数据结构", "type": "object", "properties": {"code": {"title": "编码：1000表示成功，其他值表示失败", "type": "integer", "format": "int32"}, "message": {"title": "消息内容", "type": "string"}, "data": {"$ref": "#/components/schemas/PageResultBaseSysUserEntity"}}}, "BaseSysRoleEntity": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "createTime": {"type": "string", "format": "date-time"}, "updateTime": {"type": "string", "format": "date-time"}, "userId": {"type": "integer", "format": "int64"}, "name": {"type": "string"}, "label": {"type": "string"}, "remark": {"type": "string"}, "relevance": {"type": "integer", "format": "int32"}, "menuIdList": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "departmentIdList": {"type": "array", "items": {"type": "integer", "format": "int64"}}}}, "PageResultBaseSysRoleEntity": {"title": "分页数据模型", "type": "object", "properties": {"list": {"title": "分页数据", "type": "array", "items": {"$ref": "#/components/schemas/BaseSysRoleEntity"}}, "pagination": {"$ref": "#/components/schemas/Pagination"}}}, "RPageResultBaseSysRoleEntity": {"title": "响应数据结构", "type": "object", "properties": {"code": {"title": "编码：1000表示成功，其他值表示失败", "type": "integer", "format": "int32"}, "message": {"title": "消息内容", "type": "string"}, "data": {"$ref": "#/components/schemas/PageResultBaseSysRoleEntity"}}}, "RListBaseSysRoleEntity": {"title": "响应数据结构", "type": "object", "properties": {"code": {"title": "编码：1000表示成功，其他值表示失败", "type": "integer", "format": "int32"}, "message": {"title": "消息内容", "type": "string"}, "data": {"title": "响应数据", "type": "array", "items": {"$ref": "#/components/schemas/BaseSysRoleEntity"}}}}, "BaseSysParamEntity": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "createTime": {"type": "string", "format": "date-time"}, "updateTime": {"type": "string", "format": "date-time"}, "keyName": {"type": "string"}, "name": {"type": "string"}, "data": {"type": "string"}, "dataType": {"type": "integer", "format": "int32"}, "remark": {"type": "string"}}}, "PageResultBaseSysParamEntity": {"title": "分页数据模型", "type": "object", "properties": {"list": {"title": "分页数据", "type": "array", "items": {"$ref": "#/components/schemas/BaseSysParamEntity"}}, "pagination": {"$ref": "#/components/schemas/Pagination"}}}, "RPageResultBaseSysParamEntity": {"title": "响应数据结构", "type": "object", "properties": {"code": {"title": "编码：1000表示成功，其他值表示失败", "type": "integer", "format": "int32"}, "message": {"title": "消息内容", "type": "string"}, "data": {"$ref": "#/components/schemas/PageResultBaseSysParamEntity"}}}, "BaseSysMenuEntity": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "createTime": {"type": "string", "format": "date-time"}, "updateTime": {"type": "string", "format": "date-time"}, "parentId": {"type": "integer", "format": "int64"}, "name": {"type": "string"}, "perms": {"type": "string"}, "type": {"type": "integer", "format": "int32"}, "icon": {"type": "string"}, "orderNum": {"type": "integer", "format": "int32"}, "router": {"type": "string"}, "viewPath": {"type": "string"}, "keepAlive": {"type": "boolean"}, "isShow": {"type": "boolean"}, "parentName": {"type": "string"}, "childMenus": {"type": "array", "items": {"$ref": "#/components/schemas/BaseSysMenuEntity"}}}}, "PageResultBaseSysMenuEntity": {"title": "分页数据模型", "type": "object", "properties": {"list": {"title": "分页数据", "type": "array", "items": {"$ref": "#/components/schemas/BaseSysMenuEntity"}}, "pagination": {"$ref": "#/components/schemas/Pagination"}}}, "RPageResultBaseSysMenuEntity": {"title": "响应数据结构", "type": "object", "properties": {"code": {"title": "编码：1000表示成功，其他值表示失败", "type": "integer", "format": "int32"}, "message": {"title": "消息内容", "type": "string"}, "data": {"$ref": "#/components/schemas/PageResultBaseSysMenuEntity"}}}, "RListBaseSysMenuEntity": {"title": "响应数据结构", "type": "object", "properties": {"code": {"title": "编码：1000表示成功，其他值表示失败", "type": "integer", "format": "int32"}, "message": {"title": "消息内容", "type": "string"}, "data": {"title": "响应数据", "type": "array", "items": {"$ref": "#/components/schemas/BaseSysMenuEntity"}}}}, "BaseSysLogEntity": {"title": "分页数据", "type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "createTime": {"type": "string", "format": "date-time"}, "updateTime": {"type": "string", "format": "date-time"}, "userId": {"type": "integer", "format": "int64"}, "action": {"type": "string"}, "ip": {"type": "string"}, "params": {"type": "object"}, "name": {"type": "string"}}}, "PageResultBaseSysLogEntity": {"title": "分页数据模型", "type": "object", "properties": {"list": {"title": "分页数据", "type": "array", "items": {"$ref": "#/components/schemas/BaseSysLogEntity"}}, "pagination": {"$ref": "#/components/schemas/Pagination"}}}, "RPageResultBaseSysLogEntity": {"title": "响应数据结构", "type": "object", "properties": {"code": {"title": "编码：1000表示成功，其他值表示失败", "type": "integer", "format": "int32"}, "message": {"title": "消息内容", "type": "string"}, "data": {"$ref": "#/components/schemas/PageResultBaseSysLogEntity"}}}, "BaseSysDepartmentEntity": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "createTime": {"type": "string", "format": "date-time"}, "updateTime": {"type": "string", "format": "date-time"}, "name": {"type": "string"}, "parentId": {"type": "integer", "format": "int64"}, "orderNum": {"type": "integer", "format": "int32"}, "parentName": {"type": "string"}}}, "RListBaseSysDepartmentEntity": {"title": "响应数据结构", "type": "object", "properties": {"code": {"title": "编码：1000表示成功，其他值表示失败", "type": "integer", "format": "int32"}, "message": {"title": "消息内容", "type": "string"}, "data": {"title": "响应数据", "type": "array", "items": {"$ref": "#/components/schemas/BaseSysDepartmentEntity"}}}}, "BaseSysApiKeyEntity": {"title": "响应数据", "type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "createTime": {"type": "string", "format": "date-time"}, "updateTime": {"type": "string", "format": "date-time"}, "userId": {"type": "integer", "format": "int64"}, "apikey": {"type": "string"}, "status": {"type": "integer", "format": "int32"}, "expireTime": {"type": "string", "format": "date-time"}, "remark": {"type": "string"}, "username": {"type": "string"}}}, "RBaseSysApiKeyEntity": {"title": "响应数据结构", "type": "object", "properties": {"code": {"title": "编码：1000表示成功，其他值表示失败", "type": "integer", "format": "int32"}, "message": {"title": "消息内容", "type": "string"}, "data": {"$ref": "#/components/schemas/BaseSysApiKeyEntity"}}}, "PageResultBaseSysApiKeyEntity": {"title": "分页数据模型", "type": "object", "properties": {"list": {"title": "分页数据", "type": "array", "items": {"$ref": "#/components/schemas/BaseSysApiKeyEntity"}}, "pagination": {"$ref": "#/components/schemas/Pagination"}}}, "RPageResultBaseSysApiKeyEntity": {"title": "响应数据结构", "type": "object", "properties": {"code": {"title": "编码：1000表示成功，其他值表示失败", "type": "integer", "format": "int32"}, "message": {"title": "消息内容", "type": "string"}, "data": {"$ref": "#/components/schemas/PageResultBaseSysApiKeyEntity"}}}, "RListBaseSysApiKeyEntity": {"title": "响应数据结构", "type": "object", "properties": {"code": {"title": "编码：1000表示成功，其他值表示失败", "type": "integer", "format": "int32"}, "message": {"title": "消息内容", "type": "string"}, "data": {"title": "响应数据", "type": "array", "items": {"$ref": "#/components/schemas/BaseSysApiKeyEntity"}}}}, "BaseSysLoginDto": {"required": ["captchaId", "password", "username", "verifyCode"], "type": "object", "properties": {"username": {"type": "string", "description": "用户名"}, "password": {"type": "string", "description": "密码"}, "captchaId": {"type": "string", "description": "验证码ID"}, "verifyCode": {"type": "string", "description": "验证码"}}, "description": "登录参数"}, "RUserInfoEntity": {"title": "响应数据结构", "type": "object", "properties": {"code": {"title": "编码：1000表示成功，其他值表示失败", "type": "integer", "format": "int32"}, "message": {"title": "消息内容", "type": "string"}, "data": {"$ref": "#/components/schemas/UserInfoEntity"}}}, "RTaskPackageEntity": {"title": "响应数据结构", "type": "object", "properties": {"code": {"title": "编码：1000表示成功，其他值表示失败", "type": "integer", "format": "int32"}, "message": {"title": "消息内容", "type": "string"}, "data": {"$ref": "#/components/schemas/TaskPackageEntity"}}}, "RTaskInfoEntity": {"title": "响应数据结构", "type": "object", "properties": {"code": {"title": "编码：1000表示成功，其他值表示失败", "type": "integer", "format": "int32"}, "message": {"title": "消息内容", "type": "string"}, "data": {"$ref": "#/components/schemas/TaskInfoEntity"}}}, "RTaskExecutionEntity": {"title": "响应数据结构", "type": "object", "properties": {"code": {"title": "编码：1000表示成功，其他值表示失败", "type": "integer", "format": "int32"}, "message": {"title": "消息内容", "type": "string"}, "data": {"$ref": "#/components/schemas/TaskExecutionEntity"}}}, "RSpaceTypeEntity": {"title": "响应数据结构", "type": "object", "properties": {"code": {"title": "编码：1000表示成功，其他值表示失败", "type": "integer", "format": "int32"}, "message": {"title": "消息内容", "type": "string"}, "data": {"$ref": "#/components/schemas/SpaceTypeEntity"}}}, "RSpaceInfoEntity": {"title": "响应数据结构", "type": "object", "properties": {"code": {"title": "编码：1000表示成功，其他值表示失败", "type": "integer", "format": "int32"}, "message": {"title": "消息内容", "type": "string"}, "data": {"$ref": "#/components/schemas/SpaceInfoEntity"}}}, "RWorkOrderEntity": {"title": "响应数据结构", "type": "object", "properties": {"code": {"title": "编码：1000表示成功，其他值表示失败", "type": "integer", "format": "int32"}, "message": {"title": "消息内容", "type": "string"}, "data": {"$ref": "#/components/schemas/WorkOrderEntity"}}}, "RTaskExecuteRecordEntity": {"title": "响应数据结构", "type": "object", "properties": {"code": {"title": "编码：1000表示成功，其他值表示失败", "type": "integer", "format": "int32"}, "message": {"title": "消息内容", "type": "string"}, "data": {"$ref": "#/components/schemas/TaskExecuteRecordEntity"}}}, "RSOPStepEntity": {"title": "响应数据结构", "type": "object", "properties": {"code": {"title": "编码：1000表示成功，其他值表示失败", "type": "integer", "format": "int32"}, "message": {"title": "消息内容", "type": "string"}, "data": {"$ref": "#/components/schemas/SOPStepEntity"}}}, "RSOPScenarioEntity": {"title": "响应数据结构", "type": "object", "properties": {"code": {"title": "编码：1000表示成功，其他值表示失败", "type": "integer", "format": "int32"}, "message": {"title": "消息内容", "type": "string"}, "data": {"$ref": "#/components/schemas/SOPScenarioEntity"}}}, "RSOPIndustryEntity": {"title": "响应数据结构", "type": "object", "properties": {"code": {"title": "编码：1000表示成功，其他值表示失败", "type": "integer", "format": "int32"}, "message": {"title": "消息内容", "type": "string"}, "data": {"$ref": "#/components/schemas/SOPIndustryEntity"}}}, "RAiTaskGenerateRecordEntity": {"title": "响应数据结构", "type": "object", "properties": {"code": {"title": "编码：1000表示成功，其他值表示失败", "type": "integer", "format": "int32"}, "message": {"title": "消息内容", "type": "string"}, "data": {"$ref": "#/components/schemas/AiTaskGenerateRecordEntity"}}}, "SseEmitter": {"type": "object", "properties": {"timeout": {"type": "integer", "format": "int64"}}}, "RRecycleDataEntity": {"title": "响应数据结构", "type": "object", "properties": {"code": {"title": "编码：1000表示成功，其他值表示失败", "type": "integer", "format": "int32"}, "message": {"title": "消息内容", "type": "string"}, "data": {"$ref": "#/components/schemas/RecycleDataEntity"}}}, "RPluginInfoEntity": {"title": "响应数据结构", "type": "object", "properties": {"code": {"title": "编码：1000表示成功，其他值表示失败", "type": "integer", "format": "int32"}, "message": {"title": "消息内容", "type": "string"}, "data": {"$ref": "#/components/schemas/PluginInfoEntity"}}}, "RDictTypeEntity": {"title": "响应数据结构", "type": "object", "properties": {"code": {"title": "编码：1000表示成功，其他值表示失败", "type": "integer", "format": "int32"}, "message": {"title": "消息内容", "type": "string"}, "data": {"$ref": "#/components/schemas/DictTypeEntity"}}}, "RDictInfoEntity": {"title": "响应数据结构", "type": "object", "properties": {"code": {"title": "编码：1000表示成功，其他值表示失败", "type": "integer", "format": "int32"}, "message": {"title": "消息内容", "type": "string"}, "data": {"$ref": "#/components/schemas/DictInfoEntity"}}}, "RBaseSysUserEntity": {"title": "响应数据结构", "type": "object", "properties": {"code": {"title": "编码：1000表示成功，其他值表示失败", "type": "integer", "format": "int32"}, "message": {"title": "消息内容", "type": "string"}, "data": {"$ref": "#/components/schemas/BaseSysUserEntity"}}}, "RBaseSysRoleEntity": {"title": "响应数据结构", "type": "object", "properties": {"code": {"title": "编码：1000表示成功，其他值表示失败", "type": "integer", "format": "int32"}, "message": {"title": "消息内容", "type": "string"}, "data": {"$ref": "#/components/schemas/BaseSysRoleEntity"}}}, "RBaseSysParamEntity": {"title": "响应数据结构", "type": "object", "properties": {"code": {"title": "编码：1000表示成功，其他值表示失败", "type": "integer", "format": "int32"}, "message": {"title": "消息内容", "type": "string"}, "data": {"$ref": "#/components/schemas/BaseSysParamEntity"}}}, "RBaseSysMenuEntity": {"title": "响应数据结构", "type": "object", "properties": {"code": {"title": "编码：1000表示成功，其他值表示失败", "type": "integer", "format": "int32"}, "message": {"title": "消息内容", "type": "string"}, "data": {"$ref": "#/components/schemas/BaseSysMenuEntity"}}}}, "securitySchemes": {"Authorization": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "Authorization", "in": "header"}}}}