# DIFY集成设计

## 1. 需求与目标
- 实现Dify Workflow应用的持久化管理（CURD），支持配置英文名、描述、地址、API Key、入参、输出说明、启用状态等。
- 支持通过工作流英文名（name）执行Dify Workflow，兼容阻塞（blocking）和流式（streaming）两种模式。
- 复用系统已有的HTTP客户端能力，保证API Key安全，接口参数灵活，便于前后端联调和后续扩展。
- 严格对齐Dify Workflow官方API（/workflows/run），inputs为必填，文件变量需先上传并填入inputs，无会话支持。

## 2. 数据结构与表设计
- 实体类：`DifyWorkflowEntity`，字段包括：
  - name（英文名称）、description（描述）、url（地址，/workflows/run）、apiKey（API Key）、inputParams（入参说明）、outputDesc（输出说明）、status（启用状态）
- 表结构由实体类自动生成，无需手写SQL。

## 3. 后端接口与实现
### 3.1 CURD接口
- 继承BaseController/BaseService/BaseMapper，标准CURD接口自动注册。
- Controller路径：`/admin/dify/workflow`，支持page、add、update、delete、info。
- 查询条件用TableDef静态字段链式配置，支持字段精确与关键字模糊搜索。

### 3.2 执行workflow接口
- 路径：`POST /admin/dify/workflow/executeByName`
- 入参DTO：`DifyWorkflowExecuteRequest`，字段：
  - workflowName（英文名，必填）
  - inputs（object，必填，App定义的所有变量，含文件变量）
  - user（string，必填，终端用户唯一标识）
  - responseMode（string，必填，streaming/blocking，默认blocking）
- 返回：
  - 阻塞模式：application/json，完整workflow结果
  - 流式模式：text/event-stream，SSE流式输出
- 说明：
  - 无query/conversationId/files等参数，inputs为唯一入口，文件变量需先上传文件，获得upload_file_id后填入inputs

### 3.3 文件上传接口
- 路径建议：`POST /admin/dify/workflow/uploadFile`
- 参数：file（二进制）、user（string，终端用户标识）
- 实现：后端转发到Dify `/files/upload`，返回upload_file_id，前端再将该ID填入inputs的对应变量
- 用法：
  1. 前端先上传文件，获取upload_file_id
  2. 构造inputs变量，文件类型变量按官方格式填入upload_file_id
  3. 调用/executeByName接口

### 3.4 Service实现要点
- 查找启用的DifyWorkflowEntity，校验url/apiKey
- 组装Dify API请求体（inputs、user、response_mode）
- 阻塞模式：RestTemplate调用，返回完整结果
- 流式模式：WebClient调用，SSE流转发
- 日志与异常：详细日志，CoolException统一抛出

### 3.5 HTTP客户端与依赖
- 阻塞模式：RestTemplate
- 流式模式：WebClient（需引入spring-boot-starter-webflux依赖）

## 4. 前后端调用流程
- 前端通过service.dify.workflow调用CURD接口
- 执行接口：POST /admin/dify/workflow/executeByName，参数与后端DTO一致
- 流式模式前端用EventSource或fetch+ReadableStream监听SSE
- 文件上传先调/uploadFile，后续inputs变量填入upload_file_id

## 5. 安全与异常处理
- API Key仅后端持有，前端不暴露
- 所有参数校验，异常用CoolException抛出
- 日志详细，便于排查

## 6. 扩展与维护建议
- 支持多workflow配置，按name灵活切换
- 可扩展更多Dify API参数与能力
- 建议补充单元测试与集成测试，覆盖正常与异常场景
- 建议将接口、参数、返回格式、异常说明同步到API文档，便于团队协作

---

本设计已完全落地于代码实现，严格遵循Cool Admin后端开发规范和Dify Workflow官方API，便于后续团队扩展、维护和前后端联调。
