# 双维度组织架构菜单权限设计说明

## 📋 设计原则

基于Cool Admin现有的菜单权限系统，双维度组织架构的菜单权限**完全通过管理界面配置**，而不是在代码中硬编码菜单结构。

## 🏗️ 现有菜单权限系统分析

### 1. 数据库表结构
```sql
-- 菜单表
base_sys_menu (id, name, router, perms, type, icon, parent_id, view_path, ...)

-- 角色表  
base_sys_role (id, name, label, menu_id_list, department_id_list, ...)

-- 角色菜单关联表
base_sys_role_menu (id, role_id, menu_id)

-- 用户角色关联表
base_sys_user_role (id, user_id, role_id)
```

### 2. 权限计算流程
```
用户登录 → 获取用户角色 → 获取角色菜单权限 → 返回菜单数据
```

### 3. 前端菜单获取
```typescript
// 前端通过统一接口获取菜单
await service.base.comm.permmenu()
// 返回: { menus: [...], perms: [...] }
```

## 🎯 双维度权限集成方案

### 1. 核心设计思路

**不改变现有菜单权限系统的任何逻辑**，只在权限计算时增加组织形态的判断：

```java
@Override
public Dict permmenu(Long adminUserId) {
    // 获取用户当前组织形态
    String currentMode = organizationModeService.getCurrentMode(adminUserId);
    
    if (OrganizationModeEnum.DEPARTMENT.getCode().equals(currentMode)) {
        // 部门形态：使用现有逻辑
        return Dict.create()
            .set("menus", getMenus(adminUserId))
            .set("perms", getPerms(adminUserId));
    } else if (OrganizationModeEnum.PROJECT.getCode().equals(currentMode)) {
        // 项目形态：获取项目相关菜单和权限
        return Dict.create()
            .set("menus", getProjectMenus(adminUserId))
            .set("perms", getProjectPerms(adminUserId));
    }
    
    // 默认返回部门形态
    return Dict.create()
        .set("menus", getMenus(adminUserId))
        .set("perms", getPerms(adminUserId));
}
```

### 2. 项目菜单权限获取

```java
private List<BaseSysMenuEntity> getProjectMenus(Long userId) {
    // 1. 获取用户在项目维度的角色ID列表
    List<Long> projectRoleIds = getProjectRoleIds(userId);
    
    if (projectRoleIds.isEmpty()) {
        return new ArrayList<>();
    }
    
    // 2. 复用现有的getMenus方法，传入项目角色ID
    return getMenus(projectRoleIds.toArray(new Long[0]));
}

private List<Long> getProjectRoleIds(Long userId) {
    // 从用户组织关系表获取项目角色
    List<UserOrganizationEntity> projectRoles = userOrganizationService
        .getByUserIdAndType(userId, OrganizationModeEnum.PROJECT.getCode());
    
    // 将项目角色代码转换为系统角色ID
    return projectRoles.stream()
        .filter(role -> role.getStatus() == 1)
        .map(role -> getSystemRoleIdByProjectRole(role.getRoleCode()))
        .filter(Objects::nonNull)
        .collect(Collectors.toList());
}
```

### 3. 全局项目角色创建

通过系统初始化创建全局项目角色：

```java
@Component
public class ProjectMenuInitializer {
    
    @PostConstruct
    public void initProjectRoles() {
        // 创建全局项目角色
        createProjectRole("PROJECT_OWNER", "项目负责人");
        createProjectRole("PROJECT_ADMIN", "项目管理员");
        createProjectRole("PROJECT_MEMBER", "项目成员");
        createProjectRole("PROJECT_VIEWER", "项目观察者");
    }
    
    private void createProjectRole(String label, String name) {
        BaseSysRoleEntity role = new BaseSysRoleEntity();
        role.setLabel(label);
        role.setName(name);
        role.setUserId(1L); // 系统创建
        
        // 检查是否已存在
        if (baseSysRoleService.getByLabel(label) == null) {
            baseSysRoleService.save(role);
        }
    }
}
```

## 📋 管理员配置流程

### 1. 菜单配置
管理员在"系统管理 → 菜单管理"中：
1. 创建项目工作台根菜单
2. 创建项目子菜单（项目概览、我的项目、任务管理等）
3. 为每个菜单配置权限点（增删改查等）

### 2. 角色权限配置
管理员在"系统管理 → 角色管理"中：
1. 编辑项目角色（PROJECT_OWNER、PROJECT_ADMIN等）
2. 为每个角色分配对应的菜单权限
3. 配置角色的功能权限范围

### 3. 用户项目分配
通过项目成员管理界面：
1. 将用户添加到项目中
2. 为用户分配项目角色
3. 系统自动在用户组织关系表中创建记录

## 🔄 前端菜单动态加载

### 1. 组织形态切换
```typescript
async switchMode(targetMode: string) {
    // 1. 调用后端切换组织形态
    await service.request({
        url: '/admin/organization/mode/switch',
        method: 'POST',
        data: { targetMode }
    });
    
    // 2. 重新获取菜单数据
    const { menu } = useBase();
    await menu.get(); // 重新调用permmenu接口
    
    // 3. 刷新页面加载新菜单
    window.location.reload();
}
```

### 2. 菜单权限验证
```typescript
// 前端权限验证（复用现有逻辑）
import { checkPerm } from '/@/modules/base/utils/permission';

// 检查权限
if (checkPerm('project:task:add')) {
    // 显示添加按钮
}
```

## ✅ 方案优势

### 1. **完全兼容现有系统**
- 不修改任何现有的数据库表结构
- 不改变现有的菜单权限逻辑
- 前端菜单组件无需修改

### 2. **管理员友好**
- 通过熟悉的管理界面配置菜单
- 灵活的权限分配机制
- 可视化的菜单结构管理

### 3. **扩展性强**
- 支持任意数量的项目菜单
- 支持复杂的权限组合
- 支持未来更多组织维度

### 4. **性能优化**
- 复用现有的缓存机制
- 最小化数据库查询
- 高效的权限计算

## 🚀 实施步骤

### 第一步：数据模型扩展
1. 创建用户组织关系表
2. 创建用户当前模式表
3. 创建项目信息表

### 第二步：后端服务开发
1. 实现组织形态管理服务
2. 扩展权限服务的permmenu方法
3. 创建项目菜单初始化组件

### 第三步：前端集成
1. 实现组织形态切换组件
2. 扩展菜单Store支持组织形态
3. 实现项目成员管理界面

### 第四步：菜单配置
1. 管理员配置项目菜单结构
2. 为项目角色分配菜单权限
3. 测试菜单权限的正确性

## 📝 注意事项

### 1. **菜单标识规范**
- 项目菜单路由以`/project`开头
- 权限点以`project:`开头
- 菜单名称包含"项目"关键字

### 2. **权限继承**
- 系统管理员拥有所有权限
- 项目负责人拥有项目完全权限
- 权限按角色级别递减

### 3. **缓存策略**
- 组织形态切换时清理相关缓存
- 菜单权限变更时刷新用户权限
- 合理设置缓存过期时间

### 4. **错误处理**
- 组织形态切换失败的回滚
- 权限不足时的友好提示
- 菜单加载异常的处理

## 🎯 总结

双维度组织架构的菜单权限设计完全基于Cool Admin现有的菜单权限系统，通过最小化的修改实现了灵活的双维度权限管理。管理员可以通过熟悉的界面配置菜单权限，用户可以在不同组织形态间无缝切换，系统保持了高度的兼容性和扩展性。
