# AI任务异步生成功能重构说明

## 重构背景

原有的`generateTasksByAI`方法存在以下问题：
1. **代码重复**：与`processPreviewGeneration`有大量重复代码
2. **非异步处理**：同步方法可能导致请求超时
3. **缺少预览接受功能**：无法基于预览结果直接生成任务
4. **Controller设计不合理**：同步接口不符合异步处理架构

## 重构方案

### 1. 新增枚举类型

```java
public enum TaskGenerateMode {
    PREVIEW("preview", "预览模式"),
    GENERATE("generate", "正式生成"),
    ACCEPT_PREVIEW("accept_preview", "接受预览");
}
```

### 2. 数据库字段扩展

在`ai_task_generate_record`表中添加：
- `mode` VARCHAR(20)：生成模式
- `parent_record_id` BIGINT：父记录ID，用于关联预览记录

### 3. 统一异步处理架构

#### Service层新增方法：

```java
// 统一的异步任务生成提交入口
Long submitAsyncTaskGeneration(TaskGenerateRequest request, TaskGenerateMode mode);

// 统一的异步任务生成处理
void processAsyncTaskGeneration(Long recordId, TaskGenerateMode mode);

// 接受预览结果并生成正式任务
Long acceptPreviewAndGenerate(Long previewRecordId);
```

#### 公共逻辑提取：

- `performAIRecognitionWithProgress()` - AI识别 + 进度推送
- `displayAIRecognitionResult()` - 显示AI识别结果
- `buildTasksFromAIResult()` - 根据AI结果构建任务数据
- `buildTaskFromStep()` - 从步骤构建任务
- `savePreviewResults()` - 保存预览结果
- `persistTasksToDatabase()` - 持久化任务到数据库
- `convertPreviewDataToTaskResponses()` - 预览数据转换

### 4. Controller接口重构

#### 新的异步接口：

```java
// AI任务预览（异步）
POST /admin/sop/ai-task-generator/preview
Response: { "id": 123 }

// AI任务生成（异步）
POST /admin/sop/ai-task-generator/generate
Response: { "id": 124 }

// 接受预览结果生成任务
POST /admin/sop/ai-task-generator/accept-preview/{previewRecordId}
Response: { "id": 125 }

// SSE订阅任务进度
GET /admin/sop/ai-task-generator/subscribe/{recordId}
Response: SSE流

// 获取任务记录详情
GET /admin/sop/ai-task-generator/record/{recordId}
Response: 任务记录对象
```

#### 保持向后兼容：

```java
// 旧的同步接口（已标记为过时）
POST /admin/sop/ai-task-generator/generate-sync
```

## 使用流程

### 1. 预览模式流程

```javascript
// 1. 提交预览请求
const response = await fetch('/admin/sop/ai-task-generator/preview', {
  method: 'POST',
  body: JSON.stringify({
    taskDescription: "安排明天的会议",
    departmentIds: [1, 2],
    priority: 3
  })
});
const { id } = await response.json();

// 2. 订阅SSE获取进度
const eventSource = new EventSource(`/admin/sop/ai-task-generator/subscribe/${id}`);
eventSource.onmessage = (event) => {
  const data = JSON.parse(event.data);
  console.log('进度:', data.progress, '状态:', data.status);
  
  if (data.status === 2) { // 完成
    console.log('预览结果:', JSON.parse(data.result));
    eventSource.close();
  }
};
```

### 2. 接受预览生成任务流程

```javascript
// 基于预览记录生成正式任务
const response = await fetch(`/admin/sop/ai-task-generator/accept-preview/${previewRecordId}`, {
  method: 'POST'
});
const { id } = await response.json();

// 订阅新任务的进度
const eventSource = new EventSource(`/admin/sop/ai-task-generator/subscribe/${id}`);
eventSource.onmessage = (event) => {
  const data = JSON.parse(event.data);
  if (data.status === 2) {
    console.log('任务生成完成');
    eventSource.close();
  }
};
```

### 3. 直接生成模式流程

```javascript
// 跳过预览，直接生成任务
const response = await fetch('/admin/sop/ai-task-generator/generate', {
  method: 'POST',
  body: JSON.stringify({
    taskDescription: "处理客户投诉",
    departmentIds: [3],
    priority: 4
  })
});
const { id } = await response.json();

// 订阅进度...
```

## 进度流程优化

### 统一的进度阶段：

1. **5%** - 任务开始
2. **10%** - 需求确认：[具体需求内容]
3. **20%** - AI正在进行综合识别（场景匹配、时间分析、优先级评估）
4. **40%** - AI识别完成 - 场景：xxx；优先级：xxx；时间范围：xxx
5. **60%** - 任务预览生成中 / 任务生成中
6. **80%** - 正在创建工单和任务（仅GENERATE模式）
7. **100%** - 完成

### SSE消息格式：

```json
{
  "id": 123,
  "status": 1,
  "progress": 40,
  "mode": "preview",
  "progressDetails": [
    {
      "timestamp": "14:30:15",
      "message": "AI识别完成 - 场景：会议安排；优先级：中；时间范围：明天"
    }
  ],
  "result": "{\"scenario\":{...},\"tasks\":[...]}"
}
```

## 错误处理

### 业务异常：

- 预览记录不存在
- 预览任务未完成
- AI识别失败
- 数据序列化失败

### 技术异常：

- 网络连接问题
- 数据库操作失败
- SSE连接中断

## 性能优化

1. **异步处理**：避免阻塞主线程
2. **连接管理**：合理的SSE连接超时和清理
3. **数据库索引**：优化查询性能
4. **缓存策略**：缓存场景数据和AI识别结果

## 向后兼容性

- 保留原有的`submitPreviewTask`和`processPreviewGeneration`方法
- 提供`generateTasksByAI`的兼容实现（标记为过时）
- 原有的`/preview`接口继续可用
- 新增`/generate-sync`接口保持同步调用方式

## 测试验证

### 功能测试：

1. ✅ 预览功能正常
2. ✅ 正式生成功能正常
3. ✅ 接受预览功能正常
4. ✅ SSE推送正常
5. ✅ 错误处理正常

### 性能测试：

1. 并发预览请求处理
2. 长时间SSE连接稳定性
3. 大量任务生成性能

### 兼容性测试：

1. 原有接口继续可用
2. 数据迁移正确
3. 前端无需大幅修改

## 部署说明

1. **数据库迁移**：执行`ai_task_generate_record_migration.sql`
2. **代码部署**：更新后端代码
3. **前端适配**：可选择性使用新接口
4. **监控验证**：确认SSE连接和异步处理正常

## 后续优化建议

1. **任务调度优化**：实现任务队列和优先级调度
2. **结果缓存**：缓存AI识别结果，避免重复计算
3. **批量处理**：支持批量任务生成
4. **实时通知**：集成WebSocket或其他实时通知机制
5. **监控告警**：添加任务生成失败率监控 