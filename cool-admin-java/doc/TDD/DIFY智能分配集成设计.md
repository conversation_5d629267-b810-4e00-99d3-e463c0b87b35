# DIFY智能分配集成设计

## 1. 业务背景与目标

- 通过Dify工作流实现任务的智能分配，提升分配的智能化和灵活性。
- Cool Admin后端负责任务核心信息的收集、Dify工作流的调用、分配结果的回填。
- 分配逻辑、规则、AI推理全部在Dify工作流中实现，Cool Admin只做数据适配和结果落地。

## 2. 数据结构与接口设计

### 2.1 输入结构（传给Dify工作流）

```java
@Data
public class TaskAssignInput {
    /** 任务唯一标识（本地ID或UUID，必须唯一） */
    private String taskKey;
    /** 任务所需角色 */
    private String role;
    /** 任务名称 */
    private String name;
    /** 任务描述 */
    private String description;
    /** 任务期望开始时间（yyyy-MM-dd HH:mm:ss） */
    private String startTime;
    /** 任务期望结束时间（yyyy-MM-dd HH:mm:ss） */
    private String endTime;
    /** 任务地点要求 */
    private String location;
    /** 任务所属部门（ID或名称） */
    private String department;
}
```

- 说明：所有时间字段统一格式为`yyyy-MM-dd HH:mm:ss`。

### 2.2 输出结构（Dify工作流返回）

```java
@Data
public class TaskAssignResult {
    /** 任务唯一标识（与输入taskKey一一对应） */
    private String taskKey;
    /** 分配的执行人ID */
    private String assigneeId;
    /** 分配的执行人姓名（可选） */
    private String assigneeName;
    /** 分配时间（yyyy-MM-dd HH:mm:ss） */
    private String assignedTime;
    /** 分配理由（AI推理说明） */
    private String reason;
    /** 匹配分（AI评分，0-100） */
    private Integer score;
}
```

- 说明：Dify工作流输出为任务分配结果列表，Cool Admin根据taskKey回填本地任务。

### 2.3 后端接口

- 统一通过已有的`DifyWorkflowService.executeWorkflowByName`方法调用Dify智能分配工作流。
- 输入参数为任务分配输入列表，输出为分配结果列表。

## 3. Dify工作流输入输出规范

- **inputs**字段为任务分配输入列表（List<TaskAssignInput>），字段名建议为`tasks`。
- Dify工作流需实现：根据每个任务的角色、时间、地点、部门等要求，智能分配最合适的执行人，并给出分配理由和匹配分。
- 输出为分配结果列表（List<TaskAssignResult>），字段名建议为`assignments`。

## 4. 后端集成实现要点

- 任务分配入口统一调用Dify智能分配工作流（如`assign-tasks`）。
- 组装输入：遍历待分配任务，提取核心字段，组装为TaskAssignInput列表。
- 调用Dify工作流，传入inputs（tasks），user为当前操作人ID，response_mode为blocking。
- 解析Dify返回的assignments，按taskKey回填本地任务的执行人、分配时间、分配理由、匹配分等。
- 所有分配结果、异常、日志均落库，便于追溯和统计。

## 5. 前后端调用流程

1. 前端发起任务分配请求（可为批量任务）。
2. 后端组装TaskAssignInput列表，调用Dify智能分配工作流。
3. Dify返回assignments结果，后端回填本地任务，更新执行人、分配时间等。
4. 前端刷新任务分配状态，展示分配理由、匹配分等AI解释信息。

## 6. 安全与扩展建议

- Dify API Key仅后端持有，前端不暴露。
- 所有参数校验、异常用CoolException统一抛出，日志详细。
- 支持多种分配策略（如按部门、按角色、按技能等），可通过Dify工作流灵活扩展。
- 建议补充单元测试与集成测试，覆盖正常与异常场景。
- 建议将接口、参数、返回格式、异常说明同步到API文档，便于团队协作。

## 7. 典型用例说明

### 7.1 输入示例

```json
{
  "tasks": [
    {
      "taskKey": "123",
      "role": "维修工",
      "name": "修理水管",
      "description": "业主家中水管漏水，需紧急维修",
      "startTime": "2024-07-10 09:00:00",
      "endTime": "2024-07-10 12:00:00",
      "location": "1号楼201室",
      "department": "工程部"
    }
  ]
}
```

### 7.2 输出示例

```json
{
  "assignments": [
    {
      "taskKey": "123",
      "assigneeId": "u456",
      "assigneeName": "张三",
      "assignedTime": "2024-07-10 09:05:00",
      "reason": "张三本周维修工空闲，技能匹配，距离近",
      "score": 98
    }
  ]
}
```

---

本设计文档已对接Cool Admin现有Dify集成体系，完全支持智能分配能力的灵活扩展和团队高效协作。
如需调整字段、扩展分配规则或对接更多AI能力，可在此文档基础上持续完善。 