# 需求名称：工单(WorkOrder)系统与核心任务流程整合

- **版本**: 1.0
- **日期**: 2024-07-04
- **负责人**: AI Assistant

---

## 1. 背景与痛点分析

### 1.1. 现状

经过对当前系统的深入代码分析，我们发现以下核心事实：

1.  **工单(WorkOrder)功能处于"遗留"状态**: 系统中存在 `sop_work_order` 数据库表以及对应的后端`Controller`, `Service`, `Entity`全套代码。然而，这些代码与旧的、已被移除的`SOPTemplate`功能绑定，并且**没有任何前端UI界面**对其进行支持（列表、创建、查看）。
2.  **核心任务流程绕过了工单**: 当前活跃的任务创建流程（特别是AI任务生成器）是 `AI -> SOPScenario -> TaskPackage -> TaskInfo -> Assignee`。这个流程直接创建任务包和任务，**完全没有`WorkOrder`的参与**。
3.  **概念断层**: 这导致了业务概念上的断层。系统能够处理和执行具体的"任务"，但缺少一个更高维度的、可用于项目跟踪和管理的"工单"或"项目"容器。

### 1.2. 痛点

- **缺乏宏观管理视角**: 无法从一个项目的维度（如"新员工入职"、"服务器季度巡检"）来跟踪所有相关任务的总体进度和状态。
- **功能冗余与技术债务**: 后端存在大量`WorkOrder`相关的孤立代码，构成了技术债务，增加了系统的维护复杂性。
- **业务流程不完整**: 缺少从一个明确的"指令"或"工单"到具体"任务执行"的完整业务闭环。

---

## 2. 核心需求与目标

**核心目标**: 将`WorkOrder`正式整合到核心任务流程中，使其成为一个可被创建、管理、跟踪和查看的顶层业务指令，最终形成 `WorkOrder -> TaskPackage -> TaskInfo -> Assignee` 的完整、清晰的业务流程。

### 2.1. 功能性需求

#### FR-1: 后端 - 重构工单生成逻辑
- **FR-1.1**: `WorkOrderEntity`需要与`TaskPackageEntity`建立明确的关联关系（一对多）。一个工单可以包含多个任务包（以支持一次性为多个部门生成任务的场景）。
- **FR-1.2**: 修改`AILLMService`（AI任务生成服务），在执行任务生成操作时，必须**首先创建一个`WorkOrder`实体**。
- **FR-1.3**: 生成的`TaskPackageEntity`必须与新创建的`WorkOrder`进行关联。
- **FR-1.4**: 提供新的API接口，用于支持前端对`WorkOrder`的CRUD操作。

#### FR-2: 前端 - 开发工单管理界面
- **FR-2.1**: 需要创建一个全新的前端视图页面：**工单管理** (`/sop/work-order`)。
- **FR-2.2**: **工单列表页**:
    - 以列表形式展示所有`WorkOrder`。
    - 列表项需清晰展示工单的关键信息：工单名称/标题、状态（待处理、进行中、完成、取消）、优先级、创建时间、关联部门等。
    - 提供搜索和筛选功能。
- **FR-2.3**: **工单详情页**:
    - 点击列表项可进入工单详情页。
    - 详细展示工单的所有信息。
    - **最核心的功能**: 以列表或卡片形式，清晰地展示该工单下关联的所有`TaskPackage`（任务包）以及每个任务包下的`TaskInfo`（具体任务）及其状态和执行人。
- **FR-2.4 (可选)**: 提供手动创建工单的入口表单。

### 2.2. 非功能性需求
- **NFR-1**: 所有新接口和页面必须遵循现有项目的UI/UX风格和代码规范。
- **NFR-2**: 新功能需要有良好的性能，在工单包含大量任务时，详情页加载不应出现明显延迟。

---

## 3. 业务流程设计

### 3.1. 整合后的业务流程图

```mermaid
graph TD
    subgraph "用户/AI"
        A[用户或AI发起任务生成]
    end

    subgraph "后端服务"
        B(AILLMService)
        C(WorkOrderService)
        D(TaskPackageService)
        E(TaskInfoService)
    end
    
    subgraph "数据库实体"
        F[WorkOrderEntity]
        G[TaskPackageEntity]
        H[TaskInfoEntity]
    end

    A -- "1. 描述/部门" --> B
    B -- "2. 创建工单指令" --> C
    C -- "3. 持久化" --> F
    B -- "4. 基于SOP创建任务包" --> D
    D -- "5. 持久化并关联WorkOrder" --> G
    B -- "6. 生成具体任务" --> E
    E -- "7. 持久化并关联TaskPackage" --> H
    
    F -- "1:N" --> G
    G -- "1:N" --> H
```

### 3.2. 流程说明
1.  **发起**: 用户在AI任务生成页面输入描述，选择部门，点击"生成"。
2.  **创建工单**: `AILLMService`接收到请求后，不再直接创建任务包，而是先调用`WorkOrderService`创建一个顶层的`WorkOrderEntity`，记录本次操作的业务意图（如"AI生成的XX任务"）。
3.  **创建任务包**: `AILLMService`继续为每个选定的部门创建`TaskPackageEntity`，并在保存时，将这些任务包与刚刚创建的`WorkOrder`的ID进行关联。
4.  **创建任务**: `AILLMService`解析SOP，生成`TaskInfoEntity`并分配执行人，流程与之前保持一致。
5.  **前端呈现**: 用户可以在新的"工单管理"页面看到刚刚创建的工单，并能点进去跟踪其下所有任务包和任务的进度。

---

## 4. 验收标准

1.  **后端**:
    - `sop_work_order`表结构已更新，能够关联`sop_task_package`。
    - AI生成任务时，数据库中能正确地创建`WorkOrder`记录，并与`TaskPackage`关联。
    - 新增的工单管理API能够正常返回数据，且符合接口文档定义。
2.  **前端**:
    - "工单管理"菜单和页面已成功添加，并能正常访问。
    - 工单列表页能正确显示所有工单数据，且搜索、筛选功能可用。
    - 工单详情页能正确展示工单自身信息，并能加载和显示其下所有关联的任务包和任务列表。
    - 整体UI风格与系统保持一致。
3.  **流程验证**:
    - 完成一次AI多部门任务生成，能在工单管理页面找到对应的工单记录，且该记录下的任务与AI预览时一致。 