# 任务按照部门数据权限管理 - 产品需求文档 (PRD)

## 📋 文档信息

| 项目 | 内容 |
|------|------|
| 文档标题 | 任务按照部门数据权限管理需求文档 |
| 版本号 | v1.0 |
| 创建日期 | 2025-07-02 |
| 最后更新 | 2025-07-02 |
| 负责人 | 产品经理 |
| 开发团队 | yayaai 开发团队 |

## 🎯 需求概述

### 背景描述

当前Cool Admin系统已经具备完善的部门数据权限控制机制，但在任务管理模块中，任务包（TaskPackage）、任务信息（TaskInfo）、任务执行（TaskExecution）等核心数据尚未完全整合部门权限控制，导致以下问题：

1. **数据隔离不完整**：用户可能看到不属于其管理范围的任务数据
2. **权限边界模糊**：任务分配和执行缺乏部门级别的权限控制
3. **管理效率低下**：部门管理者无法专注于本部门的任务管理
4. **数据安全风险**：跨部门数据泄露的潜在风险

### 需求目标

实现任务系统与部门数据权限的深度集成，确保：
- 用户只能查看和管理其有权限的部门任务
- 任务分配严格遵循部门权限边界
- 提供灵活的跨部门协作支持
- 保持良好的用户体验和系统性能

## 👥 用户角色分析

### 主要用户角色

#### 1. 超级管理员 (admin)
- **权限范围**：全部门、全任务权限
- **主要职责**：系统管理、权限分配、跨部门任务协调
- **使用场景**：系统维护、重要任务监控、权限配置

#### 2. 部门经理
- **权限范围**：本部门及下属部门（根据relevance配置）
- **主要职责**：部门任务规划、任务分配、进度监控
- **使用场景**：制定部门任务计划、分配任务给下属、监控执行进度

#### 3. 项目主管
- **权限范围**：特定项目相关的多个部门
- **主要职责**：跨部门项目任务协调、资源调配
- **使用场景**：管理跨部门项目、协调多部门资源

#### 4. 普通员工
- **权限范围**：个人任务及参与的协作任务
- **主要职责**：执行分配的任务、汇报进度
- **使用场景**：查看个人任务、更新任务状态、提交工作成果

#### 5. 质量检查员
- **权限范围**：特定部门的任务质量检查权限
- **主要职责**：任务质量验收、问题反馈
- **使用场景**：检查任务完成质量、反馈问题

## 📋 功能需求

### 1. 任务查看权限控制

#### 1.1 任务包列表权限过滤
**功能描述**：用户在任务包列表页面只能看到其有权限的部门任务包

**详细需求**：
- 根据用户的部门权限过滤任务包列表
- 支持上下级部门权限继承（基于relevance配置）
- 提供部门筛选器，用户可选择查看特定部门的任务包
- 显示任务包所属部门信息

**验收标准**：
- [ ] 普通用户只能看到自己部门的任务包
- [ ] 部门经理能看到本部门及下属部门的任务包（relevance=1时）
- [ ] 超级管理员能看到所有任务包
- [ ] 列表中正确显示部门信息

#### 1.2 任务详情权限验证
**功能描述**：用户访问任务详情时进行权限验证

**详细需求**：
- 任务包、任务信息、任务执行的详情查看都需要权限验证
- 无权限访问时返回友好的错误提示
- 支持通过URL直接访问时的权限验证

**验收标准**：
- [ ] 无权限用户访问任务详情时显示权限不足提示
- [ ] 有权限用户正常查看任务详情
- [ ] 权限验证响应时间 < 100ms

### 2. 任务分配权限控制

#### 2.1 执行人选择范围限制
**功能描述**：任务分配时，执行人选择范围限制在用户有权限的部门内

**详细需求**：
- 任务分配时，执行人下拉列表只显示用户有权限部门的员工
- 支持跨部门协作的特殊权限配置
- 提供部门快速筛选功能

**验收标准**：
- [ ] 执行人列表只显示权限范围内的用户
- [ ] 支持按部门筛选执行人
- [ ] 跨部门协作权限正确控制

#### 2.2 批量任务分配权限
**功能描述**：批量分配任务时的权限控制

**详细需求**：
- 批量分配任务时验证每个任务的部门权限
- 权限不足的任务自动跳过并提示
- 提供分配结果的详细反馈

**验收标准**：
- [ ] 批量分配时正确验证每个任务权限
- [ ] 权限不足的任务有明确提示
- [ ] 显示分配成功和失败的统计信息

### 3. 任务创建权限控制

#### 3.1 任务包创建权限
**功能描述**：用户创建任务包时的部门权限控制

**详细需求**：
- 新建任务包时自动设置创建人的部门为默认部门
- 支持选择用户有权限的其他部门作为任务包所属部门
- 记录任务包的创建者和所属部门

**验收标准**：
- [ ] 任务包创建时正确设置所属部门
- [ ] 用户只能选择有权限的部门
- [ ] 正确记录创建者信息

#### 3.2 任务生成权限集成
**功能描述**：AI任务生成器与部门权限的集成

**详细需求**：
- AI生成任务时自动继承当前用户的部门权限
- AI生成任务时支持高级选项自定义选择具有权限的部门
- 生成的任务自动分配到用户有权限的部门
- 支持手动调整生成任务的部门归属

**验收标准**：
- [ ] AI生成的任务正确设置部门信息
- [ ] 生成任务的部门权限符合当前用户权限
- [ ] 支持手动调整任务部门

### 4. 任务状态变更权限

#### 4.1 任务状态更新权限
**功能描述**：任务状态变更时的权限验证

**详细需求**：
- 任务开始、暂停、完成等状态变更需要部门权限验证
- 只有任务执行人和有权限的管理者可以变更任务状态
- 记录状态变更的操作人和时间

**验收标准**：
- [ ] 状态变更时正确验证操作权限
- [ ] 无权限用户无法变更任务状态
- [ ] 正确记录操作日志

#### 4.2 任务完成验收权限
**功能描述**：任务完成后的验收权限控制

**详细需求**：
- 任务验收需要部门管理权限或质量检查权限
- 支持跨部门任务的联合验收
- 验收结果记录操作人信息

**验收标准**：
- [ ] 验收操作正确验证权限
- [ ] 跨部门任务验收流程正确
- [ ] 验收记录完整准确

### 5. 数据统计与报表

#### 5.1 部门任务统计
**功能描述**：按部门权限提供任务统计数据

**详细需求**：
- 任务统计数据按用户部门权限过滤
- 提供部门维度的任务完成率、及时率等指标
- 支持多部门对比分析（权限范围内）

**验收标准**：
- [ ] 统计数据正确反映用户权限范围
- [ ] 部门指标计算准确
- [ ] 对比分析功能正常

#### 5.2 权限审计日志
**功能描述**：记录任务相关的权限操作日志

**详细需求**：
- 记录任务查看、分配、状态变更的权限验证日志
- 提供权限违规操作的告警机制
- 支持权限操作的统计分析

**验收标准**：
- [ ] 权限操作完整记录
- [ ] 违规操作及时告警
- [ ] 日志查询和分析功能正常

## 🎨 用户体验需求

### 1. AI任务生成器界面设计

#### 1.1 部门选择功能
**功能描述**：在AI任务生成器的高级选项中提供部门选择功能

**设计要求**：
- 在高级选项面板中新增"目标部门"选择器
- 部门选择器显示用户有权限的所有部门
- 支持搜索和树形展示部门层级结构
- 默认选中当前用户所属部门
- 提供部门权限范围的可视化提示

**交互流程**：
1. 用户点击AI任务生成器的"高级选项"
2. 展开高级选项面板，显示部门选择器
3. 用户可搜索或浏览选择目标部门
4. 选择部门后，显示该部门的基本信息和权限范围
5. 生成任务时自动关联到选定部门

**验收标准**：
- [ ] 高级选项中正确显示部门选择器
- [ ] 只显示用户有权限的部门
- [ ] 支持部门搜索和层级展示
- [ ] 部门选择后正确关联生成的任务

#### 1.2 权限提示界面
**功能描述**：在生成任务前显示权限和影响范围提示

**设计要求**：
- 显示选定部门的权限范围
- 提示生成任务的可见性和分配范围
- 展示相关协作部门（如有）
- 提供权限确认机制

### 2. 任务列表界面设计

#### 2.1 部门信息展示
**功能描述**：在任务包和任务列表中展示部门信息

**设计要求**：
- 任务包/任务列表增加"所属部门"列
- 使用部门标签（Tag）展示，支持颜色编码
- 显示创建者部门信息
- 支持跨部门任务的特殊标识
- 提供部门权限状态指示器

**视觉设计**：
```
┌─────────────────────────────────────────────────────────┐
│ 任务包名称 | 所属部门 | 创建者 | 状态 | 权限状态 | 操作  │
├─────────────────────────────────────────────────────────┤
│ AI生成任务包| [技术部] | 张三   | 进行中| ●完全权限| 编辑 │
│ 测试任务包  | [产品部] | 李四   | 待开始| ○只读权限| 查看 │
│ 跨部门项目  | [技术部] | 王五   | 已完成| ◐受限权限| 详情 │
└─────────────────────────────────────────────────────────┘
```

#### 2.2 部门筛选功能
**功能描述**：提供基于部门的任务筛选功能

**设计要求**：
- 在搜索工具栏添加部门筛选器
- 支持多部门选择和快速切换
- 提供"我的部门"、"全部权限部门"快捷选项
- 筛选器显示每个部门的任务数量
- 支持部门筛选与其他条件的组合

**交互设计**：
```
┌─────────────────────────────────────────────────────────┐
│ [🔍搜索框] [📅时间] [📊状态] [🏢部门] [➕新建] [↻刷新]  │
├─────────────────────────────────────────────────────────┤
│ 部门筛选：                                               │
│ ☑️ 我的部门(5)  ☑️ 技术部(12)  ☐ 产品部(8)  ☐ 设计部(3) │
│ [全选] [清空] [我的权限部门] [全部部门]                   │
└─────────────────────────────────────────────────────────┘
```

### 3. 任务卡片界面设计

#### 3.1 部门信息展示
**功能描述**：在任务卡片中显示详细的部门信息

**设计要求**：
- 任务卡片头部显示所属部门标签
- 右上角显示权限状态图标
- 卡片底部显示创建者部门信息
- 支持跨部门协作的特殊标识
- 提供部门快速切换链接

**卡片设计布局**：
```
┌─────────────────────────────────────────────────┐
│ [技术部] 任务包名称                      [●完全权限] │
├─────────────────────────────────────────────────┤
│ 📝 任务描述内容...                               │
│ ⏰ 截止时间：2025-01-15                         │
│ 👤 执行人：张三                                 │
│ 📊 进度：60%                                   │
├─────────────────────────────────────────────────┤
│ 创建者：李四(产品部) | 创建时间：2025-01-01       │
│ [🔗跨部门协作] [📋详情] [✏️编辑] [👥分配]         │
└─────────────────────────────────────────────────┘
```

#### 3.2 权限状态可视化
**功能描述**：通过视觉元素清晰展示权限状态

**设计规范**：
- 完全权限：绿色圆点(●) + "完全权限"
- 只读权限：黄色圆点(○) + "只读权限"  
- 受限权限：橙色半圆(◐) + "受限权限"
- 无权限：灰色圆点(◯) + "无权限"
- 跨部门协作：特殊图标(🔗) + "跨部门"

### 4. 界面设计规范

#### 4.1 部门标签设计
- 主色调：#409EFF（蓝色系）
- 当前用户部门：#67C23A（绿色）
- 跨部门协作：#E6A23C（橙色）
- 无权限部门：#909399（灰色）
- 标签圆角：4px，字体：12px

#### 4.2 权限图标设计
- 使用Element Plus图标库
- 完全权限：el-icon-success（绿色）
- 只读权限：el-icon-view（黄色）
- 受限权限：el-icon-warning（橙色）
- 无权限：el-icon-close（灰色）

#### 4.3 响应式设计
- 移动端隐藏部分部门信息，保留核心权限状态
- 平板端优化部门筛选器的展示方式
- 桌面端提供完整的部门信息展示

### 5. 操作流程优化

#### 5.1 权限提示优化
**设计要求**：
- 权限不足时显示友好的提示信息
- 提供权限申请或联系管理员的快捷方式
- 显示权限不足的具体原因
- 提供替代操作建议

**提示信息示例**：
```
⚠️ 权限不足
您当前无权限访问该任务，可能的原因：
• 任务属于其他部门(产品部)
• 您的角色权限不足
• 任务已被设置为受限访问

建议操作：
🔗 申请访问权限  📞 联系部门经理  💬 发起协作请求
```

#### 5.2 操作效率提升
**设计要求**：
- 智能推荐执行人（优先推荐同部门人员）
- 提供部门常用操作的快捷入口
- 支持批量操作的权限预检查
- 历史操作的智能记忆和推荐

#### 5.3 多部门协作流程
**设计要求**：
- 支持跨部门任务的协作流程设计
- 提供部门间权限授权机制
- 协作任务的状态同步和通知
- 协作历史的完整记录和追踪

## 📊 非功能性需求

### 1. 性能要求

| 指标 | 要求 | 说明 |
|------|------|------|
| 权限验证响应时间 | < 100ms | 单次权限验证的响应时间 |
| 列表加载时间 | < 2s | 任务列表页面的加载时间 |
| 并发用户数 | ≥ 1000 | 系统支持的并发用户数 |
| 缓存命中率 | ≥ 95% | 权限数据的缓存命中率 |

### 2. 安全要求

- 所有任务操作必须进行权限验证
- 敏感操作记录完整的审计日志
- 支持权限变更的实时生效
- 防止权限绕过和越权访问

### 3. 兼容性要求

- 兼容现有的部门权限体系
- 不影响现有任务功能的正常使用
- 支持渐进式权限控制升级
- 兼容现有的数据库结构

## 🔄 实施计划

### 阶段一：基础权限集成 (Week 1-2)
- [ ] 任务包、任务信息、任务执行实体添加部门字段
- [ ] 实现基础的权限查询过滤
- [ ] 更新任务列表API的权限控制

### 阶段二：核心功能实现 (Week 3-4)
- [ ] 实现任务分配的权限控制
- [ ] 完成任务状态变更的权限验证
- [ ] 集成AI任务生成器的权限控制

### 阶段三：用户体验优化 (Week 5-6)
- [ ] 优化前端界面的权限展示
- [ ] 实现权限友好的错误提示
- [ ] 完成批量操作的权限控制

### 阶段四：测试与发布 (Week 7-8)
- [ ] 完整的功能测试和性能测试
- [ ] 权限安全测试
- [ ] 用户验收测试
- [ ] 生产环境发布

## ✅ 验收标准

### 功能验收
- [ ] 所有任务功能都正确集成部门权限控制
- [ ] 权限边界清晰，无越权访问情况
- [ ] 用户体验友好，操作流程顺畅
- [ ] 性能指标达到要求

### 前端交互验收
- [ ] **AI任务生成器验收**
  - [ ] 高级选项中显示部门选择器
  - [ ] 部门选择器只显示有权限的部门
  - [ ] 支持部门搜索和层级展示
  - [ ] 生成的任务正确关联到选定部门
  - [ ] 权限范围提示信息准确

- [ ] **任务列表验收**
  - [ ] 任务列表正确显示所属部门列
  - [ ] 部门标签颜色编码正确
  - [ ] 部门筛选器功能正常
  - [ ] 支持多部门选择和快速切换
  - [ ] 权限状态指示器显示准确

- [ ] **任务卡片验收**
  - [ ] 卡片头部正确显示部门标签
  - [ ] 权限状态图标显示准确
  - [ ] 跨部门协作标识正确显示
  - [ ] 创建者部门信息展示完整
  - [ ] 部门相关操作权限控制正确

- [ ] **权限可视化验收**
  - [ ] 权限状态图标设计规范统一
  - [ ] 部门标签颜色符合设计规范
  - [ ] 权限提示信息友好清晰
  - [ ] 响应式设计在各设备正常显示

### 安全验收
- [ ] 通过安全渗透测试
- [ ] 权限绕过测试通过
- [ ] 审计日志完整准确
- [ ] 数据隔离效果良好
- [ ] 前端权限控制与后端同步

### 兼容性验收
- [ ] 现有功能正常工作
- [ ] 数据迁移完整无误
- [ ] API接口向后兼容
- [ ] 第三方集成正常
- [ ] 各浏览器兼容性良好

## 📝 附录

### 相关文档
- [Cool Admin 部门数据权限设计](../TDD/部门数据权限设计.md)
- [任务管理系统技术设计文档](../TDD/任务按照部门数据权限管理-技术设计.md)
- [API接口文档](../API/任务权限控制接口文档.md)

### 术语表
- **部门权限**：用户基于角色获得的部门数据访问权限
- **relevance**：角色配置中控制是否包含上下级部门的字段
- **权限继承**：上级部门权限自动包含下级部门权限的机制
- **跨部门协作**：不同部门之间的任务协作机制

---

**文档状态**: 待评审  
**下一步行动**: 技术团队评估可行性，制定详细的技术设计方案
