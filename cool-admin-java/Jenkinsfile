pipeline {
    agent any
    
    environment {
        // 项目配置
        PROJECT_NAME = 'cool-admin-java'
        DOCKER_IMAGE = 'cool-admin/backend'
        DOCKER_TAG = "${BUILD_NUMBER}"
        MAVEN_OPTS = '-Dmaven.repo.local=.m2/repository'
        
        // Docker Registry配置
        DOCKER_REGISTRY = '**************:5000'
        DOCKER_CREDENTIALS_ID = 'docker-registry-credentials'
        
        // 部署配置
        DEPLOY_SERVER = '**************'
        DEPLOY_USER = 'root'
        DEPLOY_PATH = '/opt/deploy/cool-admin'
    }
    
    tools {
        maven 'Maven-3.6'
        jdk 'java21'
    }
    
    stages {
        stage('Checkout') {
            steps {
                echo '🔄 检出代码...'
                checkout scm
                
                script {
                    // 获取Git信息
                    env.GIT_COMMIT_SHORT = sh(
                        script: "git rev-parse --short HEAD",
                        returnStdout: true
                    ).trim()
                    env.GIT_BRANCH = sh(
                        script: "git rev-parse --abbrev-ref HEAD",
                        returnStdout: true
                    ).trim()
                }
                
                echo "Git分支: ${env.GIT_BRANCH}"
                echo "Git提交: ${env.GIT_COMMIT_SHORT}"
            }
        }
        
        stage('Build') {
            steps {
                echo '🔨 Maven构建中...'
                
                // 显示Java版本信息
                sh 'java -version'
                sh 'mvn -version'
                
                // 检查pom.xml是否存在
                sh 'ls -la pom.xml'
                
                // 确保使用Java 17进行构建
                sh 'mvn clean compile -DskipTests=true -Dmaven.compiler.source=17 -Dmaven.compiler.target=17'
            }
        }
        
        stage('Package') {
            steps {
                echo '📦 Maven打包中...'
                sh 'mvn package -DskipTests=true -Dmaven.compiler.source=17 -Dmaven.compiler.target=17'
                
                // 验证JAR文件
                sh 'ls -la target/*.jar'
                
                // 归档构建产物
                archiveArtifacts artifacts: 'target/*.jar', fingerprint: true
            }
        }
        
        stage('Docker Build') {
            steps {
                echo '🐳 构建Docker镜像...'
                
                // 检查Dockerfile是否存在
                sh 'ls -la Dockerfile'
                
                script {
                    // 构建Docker镜像
                    def image = docker.build("${DOCKER_IMAGE}:${DOCKER_TAG}")
                    
                    // 同时打上latest标签
                    sh "docker tag ${DOCKER_IMAGE}:${DOCKER_TAG} ${DOCKER_IMAGE}:latest"
                    
                    // 推送到Registry
                    docker.withRegistry("http://${DOCKER_REGISTRY}", DOCKER_CREDENTIALS_ID) {
                        image.push()
                        image.push('latest')
                    }
                }
            }
        }
        
        stage('Deploy') {
            steps {
                echo '🚀 部署到服务器...'
                script {
                    // 使用 ssh-agent 方式部署
                    sshagent(credentials: ['ssh-gkubuntu']) {
                        withCredentials([
                            usernamePassword(
                                credentialsId: 'docker-registry-credentials',
                                usernameVariable: 'DOCKER_REGISTRY_USERNAME',
                                passwordVariable: 'DOCKER_REGISTRY_PASSWORD'
                            )
                        ]) {
                            sh """
                                echo "🚀 直接通过SSH执行后端部署命令..."
                                ssh -o StrictHostKeyChecking=no -p 6000 ${DEPLOY_USER}@${DEPLOY_SERVER} " \\
                                    set -e && \\
                                    cd ${DEPLOY_PATH} && \\
                                    echo '>>> 登录到 Docker Registry...' && \\
                                    echo '${DOCKER_REGISTRY_PASSWORD}' | docker login ${DOCKER_REGISTRY} --username '${DOCKER_REGISTRY_USERNAME}' --password-stdin && \\
                                    echo '>>> 拉取最新后端镜像...' && \\
                                    docker-compose -f docker-compose-cool.yml --env-file cool.env pull cool-backend && \\
                                    echo '>>> 重启后端服务...' && \\
                                    docker-compose -f docker-compose-cool.yml --env-file cool.env up -d cool-backend && \\
                                    echo '>>> 清理旧镜像...' && \\
                                    docker image prune -f && \\
                                    echo '✅ 后端部署完成'
                                "
                            """
                        }
                    }
                }
            }
        }
    }
    
    post {
        always {
            echo '🧹 清理工作空间...'
            
            // 清理Docker镜像（更安全的方式）
            script {
                try {
                    // 检查镜像是否存在再删除
                    def imageExists = sh(
                        script: "docker images -q ${DOCKER_IMAGE}:${DOCKER_TAG}",
                        returnStdout: true
                    ).trim()
                    
                    if (imageExists) {
                        echo "删除镜像: ${DOCKER_IMAGE}:${DOCKER_TAG}"
                        sh "docker rmi ${DOCKER_IMAGE}:${DOCKER_TAG}"
                    } else {
                        echo "镜像不存在，跳过删除: ${DOCKER_IMAGE}:${DOCKER_TAG}"
                    }
                } catch (Exception e) {
                    echo "清理镜像时出错: ${e.getMessage()}"
                }
                
                try {
                    // 清理悬空镜像和容器
                    sh "docker system prune -f"
                } catch (Exception e) {
                    echo "系统清理时出错: ${e.getMessage()}"
                }
            }
            
            // 清理工作空间
            // cleanWs()
        }
        
        success {
            echo '✅ 构建成功！'
            echo "项目: ${PROJECT_NAME}"
            echo "分支: ${env.GIT_BRANCH}"
            echo "构建号: ${BUILD_NUMBER}"
            echo "提交: ${env.GIT_COMMIT_SHORT}"
        }
        
        failure {
            echo '❌ 构建失败！'
            echo "项目: ${PROJECT_NAME}"
            echo "分支: ${env.GIT_BRANCH}"
            echo "构建号: ${BUILD_NUMBER}"
            echo "查看详情: ${BUILD_URL}"
        }
        
        unstable {
            echo '⚠️ 构建不稳定！'
        }
    }
} 