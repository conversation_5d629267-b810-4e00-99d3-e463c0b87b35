-- AI任务生成记录表字段迁移
-- 添加mode和parentRecordId字段

-- 检查并添加mode字段
ALTER TABLE ai_task_generate_record 
ADD COLUMN IF NOT EXISTS mode VARCHAR(20) DEFAULT 'preview' COMMENT '生成模式：preview-预览，generate-正式生成，accept_preview-接受预览';

-- 检查并添加parentRecordId字段
ALTER TABLE ai_task_generate_record 
ADD COLUMN IF NOT EXISTS parent_record_id BIGINT COMMENT '父记录ID，用于关联预览记录';

-- 更新现有记录的mode字段
UPDATE ai_task_generate_record 
SET mode = CASE 
    WHEN preview = 1 THEN 'preview'
    WHEN preview = 0 THEN 'generate'
    ELSE 'preview'
END
WHERE mode IS NULL OR mode = '';

-- 添加索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_ai_task_generate_record_mode ON ai_task_generate_record(mode);
CREATE INDEX IF NOT EXISTS idx_ai_task_generate_record_parent_id ON ai_task_generate_record(parent_record_id);
CREATE INDEX IF NOT EXISTS idx_ai_task_generate_record_user_status ON ai_task_generate_record(user_id, status);
CREATE INDEX IF NOT EXISTS idx_ai_task_generate_record_create_time ON ai_task_generate_record(create_time);

-- 可选：如果不再需要preview字段，可以删除（谨慎操作）
-- ALTER TABLE ai_task_generate_record DROP COLUMN IF EXISTS preview; 