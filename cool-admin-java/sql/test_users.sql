-- 测试用户数据 - 重新生成
-- 密码统一为：123456 (MD5: e10adc3949ba59abbe56e057f20f883e)

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ==========================================
-- 1. 清理现有测试数据
-- ==========================================
DELETE FROM `base_sys_user_skill` WHERE user_id >= 100;
DELETE FROM `base_sys_user_role` WHERE user_id >= 100;
DELETE FROM `base_sys_user` WHERE id >= 100;

-- ==========================================
-- 2. 使用现有角色数据
-- ==========================================
-- 角色ID映射：
-- 12: 物业管理人员 (property_manager)
-- 13: 客服人员 (customer_service)
-- 14: 宣传员 (publicity_staff)
-- 18: 社区工作站 (community_station)
-- 22: 业委会联络员 (community_liaison)

-- ==========================================
-- 3. 创建测试用户数据
-- ==========================================

-- 物业管理人员 (property_manager) - 3个用户
INSERT INTO `base_sys_user` (`id`, `department_id`, `name`, `username`, `password`, `password_v`, `nick_name`, `head_img`, `phone`, `email`, `remark`, `status`, `create_time`, `update_time`) VALUES
(100, 1, '张建国', 'property_zhang', 'e10adc3949ba59abbe56e057f20f883e', 1, '张物业', 'https://cool-js.com/admin/headimg.jpg', '13800100001', '<EMAIL>', '物业管理主管，10年物业管理经验，擅长设施维护和业主服务', 1, NOW(), NOW()),
(101, 1, '王丽华', 'property_wang', 'e10adc3949ba59abbe56e057f20f883e', 1, '王管家', 'https://cool-js.com/admin/headimg.jpg', '13800100002', '<EMAIL>', '物业管理专员，负责日常巡检和维修协调工作', 1, NOW(), NOW()),
(102, 1, '李维修', 'property_li', 'e10adc3949ba59abbe56e057f20f883e', 1, '李师傅', 'https://cool-js.com/admin/headimg.jpg', '13800100003', '<EMAIL>', '物业维修工程师，精通水电暖维修和设备保养', 1, NOW(), NOW());

-- 客服人员 (customer_service) - 3个用户
INSERT INTO `base_sys_user` (`id`, `department_id`, `name`, `username`, `password`, `password_v`, `nick_name`, `head_img`, `phone`, `email`, `remark`, `status`, `create_time`, `update_time`) VALUES
(110, 1, '陈小雅', 'service_chen', 'e10adc3949ba59abbe56e057f20f883e', 1, '陈客服', 'https://cool-js.com/admin/headimg.jpg', '13800110001', '<EMAIL>', '客服主管，负责客户投诉处理和服务质量管控', 1, NOW(), NOW()),
(111, 1, '刘美丽', 'service_liu', 'e10adc3949ba59abbe56e057f20f883e', 1, '刘小美', 'https://cool-js.com/admin/headimg.jpg', '13800110002', '<EMAIL>', '客服专员，热情耐心，擅长业主咨询和问题解答', 1, NOW(), NOW()),
(112, 1, '孙晓敏', 'service_sun', 'e10adc3949ba59abbe56e057f20f883e', 1, '孙客服', 'https://cool-js.com/admin/headimg.jpg', '13800110003', '<EMAIL>', '客服专员，负责电话接听和信息记录工作', 1, NOW(), NOW());

-- 宣传员 (publicity_staff) - 2个用户
INSERT INTO `base_sys_user` (`id`, `department_id`, `name`, `username`, `password`, `password_v`, `nick_name`, `head_img`, `phone`, `email`, `remark`, `status`, `create_time`, `update_time`) VALUES
(120, 1, '赵文宣', 'publicity_zhao', 'e10adc3949ba59abbe56e057f20f883e', 1, '赵宣传', 'https://cool-js.com/admin/headimg.jpg', '13800120001', '<EMAIL>', '宣传部主任，负责社区活动策划和品牌推广工作', 1, NOW(), NOW()),
(121, 1, '吴摄影', 'publicity_wu', 'e10adc3949ba59abbe56e057f20f883e', 1, '吴老师', 'https://cool-js.com/admin/headimg.jpg', '13800120002', '<EMAIL>', '摄影师兼内容编辑，负责社区活动拍摄和宣传素材制作', 1, NOW(), NOW());

-- 社区工作站 (community_station) - 3个用户
INSERT INTO `base_sys_user` (`id`, `department_id`, `name`, `username`, `password`, `password_v`, `nick_name`, `head_img`, `phone`, `email`, `remark`, `status`, `create_time`, `update_time`) VALUES
(130, 1, '周建民', 'station_zhou', 'e10adc3949ba59abbe56e057f20f883e', 1, '周站长', 'https://cool-js.com/admin/headimg.jpg', '13800130001', '<EMAIL>', '社区工作站站长，负责社区事务协调和政策宣传', 1, NOW(), NOW()),
(131, 1, '徐志华', 'station_xu', 'e10adc3949ba59abbe56e057f20f883e', 1, '徐专员', 'https://cool-js.com/admin/headimg.jpg', '13800130002', '<EMAIL>', '社区工作专员，负责居民服务和社区治理工作', 1, NOW(), NOW()),
(132, 1, '朱秀兰', 'station_zhu', 'e10adc3949ba59abbe56e057f20f883e', 1, '朱阿姨', 'https://cool-js.com/admin/headimg.jpg', '13800130003', '<EMAIL>', '社区工作专员，擅长居民矛盾调解和邻里关系协调', 1, NOW(), NOW());

-- 业委会联络员 (community_liaison) - 3个用户
INSERT INTO `base_sys_user` (`id`, `department_id`, `name`, `username`, `password`, `password_v`, `nick_name`, `head_img`, `phone`, `email`, `remark`, `status`, `create_time`, `update_time`) VALUES
(140, 1, '马建设', 'liaison_ma', 'e10adc3949ba59abbe56e057f20f883e', 1, '马主任', 'https://cool-js.com/admin/headimg.jpg', '13800140001', '<EMAIL>', '业委会联络主管，负责业委会与物业的沟通协调', 1, NOW(), NOW()),
(141, 1, '冯代表', 'liaison_feng', 'e10adc3949ba59abbe56e057f20f883e', 1, '冯老师', 'https://cool-js.com/admin/headimg.jpg', '13800140002', '<EMAIL>', '业主代表，热心公益，负责收集业主意见和反馈', 1, NOW(), NOW()),
(142, 1, '高秘书', 'liaison_gao', 'e10adc3949ba59abbe56e057f20f883e', 1, '高文员', 'https://cool-js.com/admin/headimg.jpg', '13800140003', '<EMAIL>', '业委会秘书，负责会议记录和文档管理工作', 1, NOW(), NOW());

-- ==========================================
-- 4. 用户角色关联（使用现有角色ID）
-- ==========================================

-- 物业管理人员角色关联
INSERT INTO `base_sys_user_role` (`user_id`, `role_id`) VALUES
(100, 12), -- 张建国 -> 物业管理人员
(101, 12), -- 王丽华 -> 物业管理人员
(102, 12); -- 李维修 -> 物业管理人员

-- 客服人员角色关联
INSERT INTO `base_sys_user_role` (`user_id`, `role_id`) VALUES
(110, 13), -- 陈小雅 -> 客服人员
(111, 13), -- 刘美丽 -> 客服人员
(112, 13); -- 孙晓敏 -> 客服人员

-- 宣传员角色关联
INSERT INTO `base_sys_user_role` (`user_id`, `role_id`) VALUES
(120, 14), -- 赵文宣 -> 宣传员
(121, 14); -- 吴摄影 -> 宣传员

-- 社区工作站角色关联
INSERT INTO `base_sys_user_role` (`user_id`, `role_id`) VALUES
(130, 18), -- 周建民 -> 社区工作站
(131, 18), -- 徐志华 -> 社区工作站
(132, 18); -- 朱秀兰 -> 社区工作站

-- 业委会联络员角色关联
INSERT INTO `base_sys_user_role` (`user_id`, `role_id`) VALUES
(140, 22), -- 马建设 -> 业委会联络员
(141, 22), -- 冯代表 -> 业委会联络员
(142, 22); -- 高秘书 -> 业委会联络员

-- ==========================================
-- 5. 用户技能配置数据（用于LLM任务调度）
-- ==========================================

INSERT INTO `base_sys_user_skill` (`user_id`, `skill_name`, `skill_level`, `description`) VALUES
-- 物业管理人员技能
(100, '设施维护', 5, '精通各类设施设备的维护保养，10年经验'),
(100, '业主服务', 5, '熟练处理业主投诉和服务需求，沟通能力强'),
(100, '安全管理', 4, '专业的安全管理和应急处理能力'),
(100, '团队管理', 4, '具备团队管理和工作协调能力'),
(101, '客户沟通', 4, '良好的客户沟通和协调能力'),
(101, '日常巡检', 5, '熟练进行物业日常巡检工作，细心负责'),
(101, '问题处理', 4, '快速识别和处理各类物业问题'),
(102, '设备维修', 5, '专业的设备维修和故障排除技能'),
(102, '电气维护', 5, '电气设备维护和安全检查专家'),
(102, '水暖维修', 4, '熟练的水暖系统维修技能'),

-- 客服人员技能
(110, '客户服务', 5, '专业的客户服务和投诉处理能力'),
(110, '沟通协调', 5, '优秀的沟通协调和问题解决能力'),
(110, '团队管理', 4, '客服团队管理和培训经验'),
(111, '电话接听', 5, '熟练的电话接听和信息记录技能'),
(111, '问题解答', 4, '快速准确的问题解答和引导能力'),
(111, '耐心服务', 5, '极强的耐心和服务意识'),
(112, '信息记录', 4, '准确的信息记录和数据整理能力'),
(112, '多线处理', 4, '能够同时处理多个客户咨询'),

-- 宣传员技能
(120, '活动策划', 5, '专业的活动策划和组织执行能力'),
(120, '品牌推广', 4, '社区品牌建设和推广经验'),
(120, '团队协调', 4, '宣传团队协调和项目管理能力'),
(121, '摄影摄像', 5, '专业的摄影摄像和后期制作技能'),
(121, '视觉设计', 4, '良好的视觉设计和美术功底'),
(121, '内容编辑', 4, '文案编辑和内容创作能力'),

-- 社区工作站技能
(130, '社区管理', 5, '丰富的社区管理和协调经验'),
(130, '政策宣传', 5, '熟悉政策法规和宣传工作'),
(130, '领导协调', 4, '具备领导力和多方协调能力'),
(131, '居民服务', 5, '热心的居民服务和问题处理能力'),
(131, '档案管理', 4, '规范的档案管理和信息统计技能'),
(132, '矛盾调解', 5, '专业的居民矛盾调解和协调能力'),
(132, '邻里关系', 4, '善于处理邻里关系和社区和谐'),

-- 业委会联络员技能
(140, '沟通协调', 5, '优秀的多方沟通协调能力'),
(140, '会议组织', 5, '熟练的会议组织和流程管理技能'),
(140, '决策分析', 4, '具备决策分析和问题解决能力'),
(141, '民意收集', 5, '善于收集和整理业主意见建议'),
(141, '问题反馈', 4, '及时准确的问题反馈和跟进能力'),
(141, '公益服务', 4, '热心公益，具备服务意识'),
(142, '文档管理', 5, '专业的文档管理和会议记录能力'),
(142, '信息整理', 5, '高效的信息整理和归档技能'),
(142, '办公软件', 4, '熟练使用各类办公软件');

SET FOREIGN_KEY_CHECKS = 1;

-- ==========================================
-- 6. 查询验证数据
-- ==========================================

SELECT '=== 角色数据验证 ===' as info;
SELECT id, name, label, remark FROM base_sys_role WHERE id IN (12, 13, 14, 18, 22) ORDER BY id;

SELECT '=== 用户数据验证 ===' as info;
SELECT
    u.id,
    u.name,
    u.username,
    u.nick_name,
    u.phone,
    u.email,
    r.name as role_name,
    r.label as role_label
FROM base_sys_user u
LEFT JOIN base_sys_user_role ur ON u.id = ur.user_id
LEFT JOIN base_sys_role r ON ur.role_id = r.id
WHERE u.id >= 100
ORDER BY u.id;

SELECT '=== 用户技能统计 ===' as info;
SELECT
    u.name as user_name,
    r.name as role_name,
    COUNT(s.skill_name) as skill_count,
    GROUP_CONCAT(CONCAT(s.skill_name, '(', s.skill_level, '级)') ORDER BY s.skill_name) as skills
FROM base_sys_user u
LEFT JOIN base_sys_user_role ur ON u.id = ur.user_id
LEFT JOIN base_sys_role r ON ur.role_id = r.id
LEFT JOIN base_sys_user_skill s ON u.id = s.user_id
WHERE u.id >= 100
GROUP BY u.id, u.name, r.name
ORDER BY u.id;

SELECT '=== 按角色统计用户数量 ===' as info;
SELECT
    r.name as role_name,
    r.label as role_label,
    COUNT(ur.user_id) as user_count
FROM base_sys_role r
LEFT JOIN base_sys_user_role ur ON r.id = ur.role_id
WHERE r.id IN (12, 13, 14, 18, 22)
GROUP BY r.id, r.name, r.label
ORDER BY r.id;
