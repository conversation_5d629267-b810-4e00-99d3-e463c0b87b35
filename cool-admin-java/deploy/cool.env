# Cool Admin Backend 环境变量配置
# 用于 docker-compose-cool-backend.yml

# Docker Registry 配置
DOCKER_REGISTRY=**************:5000

# 镜像标签
BACKEND_TAG=latest
FRONTEND_TAG=latest

# 端口配置
BACKEND_PORT=18001
FRONTEND_PORT=19000
FRONTEND_HTTPS_PORT=9443

# JVM 参数（修复特殊字符问题）
JAVA_OPTS=-Xms1024m -Xmx2048m -XX:+UseG1GC

# 数据库配置（如果需要）
DB_HOST=localhost
DB_PORT=3306
DB_NAME=cool
DB_USERNAME=root
DB_PASSWORD=123456

# Redis配置（如果需要）
REDIS_HOST=**************
REDIS_PORT=6379
REDIS_PASSWORD=5tgbNHY^

# 应用配置
SPRING_PROFILES_ACTIVE=prod
SERVER_PORT=8001

# 日志配置
LOGGING_LEVEL_ROOT=INFO
LOGGING_LEVEL_COM_COOL=DEBUG
LOGGING_FILE_PATH=/app/logs

# 文件上传配置
COOL_FILE_UPLOAD_PATH=/app/uploads
COOL_FILE_UPLOAD_MAX_SIZE=100MB

# 安全配置
COOL_JWT_EXPIRE=7200
COOL_JWT_REFRESH_EXPIRE=604800

# 时区配置
TZ=Asia/Shanghai 